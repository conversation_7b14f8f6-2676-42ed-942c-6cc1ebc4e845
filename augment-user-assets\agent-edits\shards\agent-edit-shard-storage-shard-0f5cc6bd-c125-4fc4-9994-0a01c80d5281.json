{"id": "shard-0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "checkpoints": {"0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\INTEGRATION_PLAN.md": [{"sourceToolCallRequestId": "6298860d-b315-409c-921b-9814a5242d3f", "timestamp": 1748361127800, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/INTEGRATION_PLAN.md"}}}, {"sourceToolCallRequestId": "e9b9ab5c-f7ec-4163-954f-df478b9a9acf", "timestamp": 1748361593294, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\INTEGRATION_PLAN.md"}}}, {"sourceToolCallRequestId": "b9198862-9249-4e22-9009-5a167e4dd2eb", "timestamp": 1748361617198, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\INTEGRATION_PLAN.md"}}}, {"sourceToolCallRequestId": "3bd019fa-4f7a-4ef5-b1ac-3ac30852aaba", "timestamp": 1748371070958, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\INTEGRATION_PLAN.md"}}}, {"sourceToolCallRequestId": "89bc404e-90c1-42ee-8bfd-4efbea9cafbb", "timestamp": 1748371085389, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\INTEGRATION_PLAN.md"}}}, {"sourceToolCallRequestId": "f622c06e-293d-472b-ba2c-23fccfd8bcca", "timestamp": 1748371104628, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\INTEGRATION_PLAN.md"}}}], "0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\models_deep_exp\\__init__.py": [{"sourceToolCallRequestId": "7bf49226-fc29-4ed3-8018-408a72c25168", "timestamp": 1748361496039, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/models_deep_exp/__init__.py"}}}], "0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\models_deep_exp\\NonlocalNet\\__init__.py": [{"sourceToolCallRequestId": "f6f2c23d-5602-42ee-90a3-ae7ecf5fa620", "timestamp": 1748361503446, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/models_deep_exp/NonlocalNet/__init__.py"}}}, {"sourceToolCallRequestId": "60a950a5-0a14-40db-8de0-efaba6f8780a", "timestamp": 1748371649081, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\models_deep_exp\\NonlocalNet\\__init__.py"}}}], "0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\models_deep_exp\\NonlocalNet\\VGG19_pytorch.py": [{"sourceToolCallRequestId": "886eb4a0-67ef-4468-9c88-a4d1ed086387", "timestamp": 1748361543367, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/models_deep_exp/NonlocalNet/VGG19_pytorch.py"}}}], "0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py": [{"sourceToolCallRequestId": "b87f5e3e-bc5a-4923-835a-b401472ea278", "timestamp": 0, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ldm/models/diffusion/ddpm.py"}}}, {"sourceToolCallRequestId": "e29ac143-3637-4878-9e7f-0c05b144f8b3", "timestamp": 1748370894217, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "860908c5-6368-4419-8ea3-b48e48c65ed2", "timestamp": 1748370929259, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "e9a5448a-f0c3-4e16-8a85-d108a2c8e2dc", "timestamp": 1748370949593, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "444717a5-f02f-4e2e-b483-5d5bd469648f", "timestamp": 1748371678736, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "741dd58d-350b-416e-9948-3c5cf0928995", "timestamp": 1748371689759, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "70a8b2cc-b633-4042-b71f-2ec7a2c82461", "timestamp": 1748371746714, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}], "0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\models_deep_exp\\NonlocalNet\\contextual_loss.py": [{"sourceToolCallRequestId": "106bca86-fe42-4c1e-9a95-1fcce2241d46", "timestamp": 1748371619190, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/models_deep_exp/NonlocalNet/contextual_loss.py"}}}, {"sourceToolCallRequestId": "9cc8e4d8-9440-4512-924b-5d5469f76d3a", "timestamp": 1748371639713, "conversationId": "0f5cc6bd-c125-4fc4-9994-0a01c80d5281", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\models_deep_exp\\NonlocalNet\\contextual_loss.py"}}}]}, "metadata": {"checkpointDocumentIds": ["0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\INTEGRATION_PLAN.md", "0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\models_deep_exp\\__init__.py", "0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\models_deep_exp\\NonlocalNet\\__init__.py", "0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\models_deep_exp\\NonlocalNet\\VGG19_pytorch.py", "0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py", "0f5cc6bd-c125-4fc4-9994-0a01c80d5281:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\models_deep_exp\\NonlocalNet\\contextual_loss.py"], "size": 1549438, "checkpointCount": 19, "lastModified": 1748371747071}}