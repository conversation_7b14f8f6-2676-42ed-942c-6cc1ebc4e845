{"id": "shard-************************************", "checkpoints": {"************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md": [{"sourceToolCallRequestId": "9c56118c-f2ab-400a-b62c-54ca412fbf91", "timestamp": 1748196110347, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "dab7936a-34fc-45bd-9b2d-0cc0596b4faf", "timestamp": 1748196132328, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "8d9a633f-6f91-4a4c-9f19-61808e91ef53", "timestamp": 1748200433676, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "5b81eb4b-5181-4ba7-afd9-79eb78bdfe53", "timestamp": 1748240777017, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "ef380eff-549a-4920-a80a-149d09906c30", "timestamp": 1748240801147, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "368f7fef-f01d-4eba-8b0a-29ac372b035e", "timestamp": 1748240818779, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "82f757e4-d38f-4b43-943e-c1ac97d4fe5d", "timestamp": 1748240838531, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "a5ffc03f-3e87-48c9-900b-0c6723fcc362", "timestamp": 1748240851538, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "3bdc1186-8cbc-48e1-881b-630bd223b07c", "timestamp": 1748240864846, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "279d2081-34bf-4f00-a295-8f338d141bed", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "3f909609-2c5b-4a55-8c4a-827ffac2aa12", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "0c7a04ca-431d-4e2a-95ea-539b0e98b879", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "5165f703-55d3-468a-8dc4-9063562b19b5", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "8780d496-137b-4303-947b-6898cdf5ca9d", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "48518c0c-044b-4781-a199-73cfee17b495", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "a894c1c2-51e2-4e8d-aae7-83b1f5587d5f", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "c9838f0e-9dcb-403f-ba08-866a4d29deea", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "daac6765-874d-4e87-adac-e40927e51f6d", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "dcbf0447-403f-40f1-9148-d63f95ecda38", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "5c4f9cea-3cbf-4232-ab9f-7e2c6991ee91", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "cc2bcccc-37d6-4ef5-8b23-c66cf0f1d4e0", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}, {"sourceToolCallRequestId": "ff45daae-e80d-49e5-99b4-c1e371d96885", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\BUG_FIXES.md": [{"sourceToolCallRequestId": "81dbe24c-5233-4d5f-9d19-d243e087ce6b", "timestamp": 1748199186711, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "0d095ab6-5f4c-43ba-8c01-43bdaecd72f6", "timestamp": 1748200433676, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "26862ff6-e456-4190-a471-aea94bcedc5e", "timestamp": 1748240704564, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "5180d113-649e-4f80-a33b-e5f9176f091b", "timestamp": 1748240725401, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "9bb3df2c-93c7-47e1-8292-3e7b91980a87", "timestamp": 1748240740265, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "ed0c31aa-f04a-4e64-b9ab-9d5df2190a2f", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "c3a0a2e7-e82a-4b04-b2af-051438c8bf27", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "d8c1aba6-6b65-4442-9b10-c4b203c0e948", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "d7632164-bf8e-4aa9-82c4-e86ec045f286", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "1d2d12b2-03a3-4591-a379-ad66c9d455a3", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "959fe487-e8c8-45f6-a16d-0629cbbc2aa3", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "3a259782-e87d-4232-b294-89fb2234acd4", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "4abfdab1-a559-4138-824f-578b4beb1959", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "bc8fe865-ff0b-4dba-9ac8-7a3cec39ec46", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "7f0c29b0-296b-4b11-8b56-6d8b234e46f5", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "4a0faa8b-548e-4d70-b89e-1538b905d112", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "bf28e1bf-436d-4a2a-aee6-6bc299ec2482", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}, {"sourceToolCallRequestId": "533a59d9-0f50-419a-9173-1b0f00a440dd", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\BUG_FIXES.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\modules\\attention.py": [{"sourceToolCallRequestId": "063e8e56-5b32-4197-b8d0-e06186abc9d1", "timestamp": 0, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ldm/modules/attention.py"}}}, {"sourceToolCallRequestId": "385c65d0-522e-4444-8fab-42ed07455335", "timestamp": 1748199200356, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "b699ddf6-0e6d-49a9-886f-7b9755be2210", "timestamp": 1748199212908, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "8ead785f-a1a9-4246-a660-bbc9afa84666", "timestamp": 1748199248357, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "71a78bfe-e224-43e2-a501-f4785957145a", "timestamp": 1748199261686, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "50e63f91-56b1-4b8f-ab0f-6c5a226c28c1", "timestamp": 1748199280425, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "908e74e3-0114-4b77-928d-c4d9cf570d27", "timestamp": 1748199390626, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "8804917a-d325-462c-a427-86e77da1604c", "timestamp": 1748200433676, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "afe3ad8d-5a6f-4ab4-84f4-05a455bfde28", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "c1bc5d51-7d23-4de5-9d31-2d49247404ef", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "959bdc6e-afa0-458f-b901-3913572475e4", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "b95e3f40-bb56-40e8-be62-d31d6c369405", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "41365979-701f-4b6a-a8c9-75812173e11f", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "15fb8a52-f810-4cb4-b263-8e93994cd557", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "d2412d6a-a34c-47f7-a7c3-c00fe412da22", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "40e93a99-3992-4978-bb58-f5a4ef98372b", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "8cd0ed2f-3d05-4f43-92b4-2032a5ef0278", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "e34d3c9b-7592-43e9-8032-a7fb9c0f97c6", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "020bfe2d-11eb-4b93-977e-54ca5de14d1c", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "7f9bae46-a35d-451c-b7fe-a47d862e4ee0", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}, {"sourceToolCallRequestId": "06ae9dcc-3364-4551-930b-07fc29854955", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py": [{"sourceToolCallRequestId": "bf2024c7-42f5-4e64-b5eb-979cf7eedc43", "timestamp": 0, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ldm/modules/attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "4452ab23-1850-4e6b-82ab-9373ed41133c", "timestamp": 1748199403660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "0cf2acf7-b097-49f3-9d25-66e2ece65506", "timestamp": 1748199421137, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "972e3251-566e-42e4-8926-0117e5b06d3a", "timestamp": 1748199446733, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "ed0d7d40-7fc2-449b-8221-23624852495c", "timestamp": 1748199481284, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "a9fdcddc-af00-450a-bcf0-cb6eeaf5857b", "timestamp": 1748199500552, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "4833d74f-853b-43c6-82b4-7467a01bdb81", "timestamp": 1748199521556, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "8a42d8d6-7def-4516-b07b-fe81be3c6dae", "timestamp": 1748200433677, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "f82afee3-8377-49b6-bf16-5080003d2e4c", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "57bfc6d3-c218-49dd-bdaf-da2e5155d511", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "d88ff758-c38e-435f-ae82-38b47a06e75f", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "8c630ccc-d32e-4cf1-a062-3cb97a7e7f36", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "f6c6da7b-0404-42c5-8681-eb8278f8fb1f", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "d1aea600-0778-4750-9b82-ed7c3d015559", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "00a64028-b05e-47ab-966a-60d141fdc089", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "75eed46e-3089-4df0-bfaa-1d8c73e79556", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "10ced123-fde3-4f10-bd1b-59e094aa2367", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "73b0c8cd-5cb5-4827-b811-1d81088caa5d", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "fc2f8125-cf04-4752-be70-c18450b2ac35", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "3b8153d3-d080-4b8f-bbe0-83fef09e7fc3", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}, {"sourceToolCallRequestId": "c8cea04b-948f-4af3-9a83-5557e26894f1", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py": [{"sourceToolCallRequestId": "21885b69-d899-453f-bc18-58ade7aee010", "timestamp": 0, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ldm/models/diffusion/ddpm.py"}}}, {"sourceToolCallRequestId": "d38f6243-ab7b-4378-82ab-4fe8cee04d05", "timestamp": 1748199543664, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "3f22c0b8-bea2-4f99-8ab8-531512ce8145", "timestamp": 1748199581136, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "1f47e5ee-18aa-4bf7-ad43-1d0c22bfad06", "timestamp": 1748199597408, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "ddb02cb3-8b60-470f-9fbe-33937ebc7cb2", "timestamp": 1748199616364, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "43bc18b6-a1b3-4c44-ba7e-ebf627dc6205", "timestamp": 1748199632672, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "986414c1-9d65-4f5e-863d-0e3ed5f09956", "timestamp": 1748199647776, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "c741333f-aff9-4c1b-8832-a8642c47b4f1", "timestamp": 1748199665957, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "7a7086ad-e995-4835-9a7a-906a34688c9c", "timestamp": 1748199685178, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "74a48d87-4210-45f8-b40d-d7cc39db2e1b", "timestamp": 1748200433677, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "c3e3a2ba-f8f6-4e4a-adb1-dd6a68336033", "timestamp": 1748201058147, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "1de42101-65a0-4f86-bdee-c359d57a1414", "timestamp": 1748201661146, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "090e690c-9421-4940-9e4d-9408754442f7", "timestamp": 1748201676440, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "df0c65e0-748b-44cb-9ea8-3c8bc28dca2c", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "b076abd3-4dbc-4266-8ede-594e79da2492", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "6729233d-c2cd-42be-b6a4-3cdfa251512a", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "78ec6842-9eb2-4c3a-82b7-2c121b46e30d", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "b68d57f2-7d26-48ef-be87-5cfb364878f6", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "2c04218e-6240-4440-8d49-2503182b7c1b", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "ba7febb0-7326-4abd-9722-f73d63d92d0b", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "86366d84-d9e0-4338-99d7-8b97b46f0add", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "3507d75e-1840-4a0e-9ccc-5cfc04078c6b", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "48636362-0e5d-4a28-9670-6757768aed45", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "8113345f-7635-4c2a-b0df-e6bd0bd8a634", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "cc317236-c8de-4fa1-b613-4a23bcc26965", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}, {"sourceToolCallRequestId": "4aace6dd-2fb3-4956-91e4-4afc11b2b9a5", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py": [{"sourceToolCallRequestId": "b6e51da6-2d17-4101-89ec-ae7fb168c073", "timestamp": 0, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ldm/modules/encoders/modules.py"}}}, {"sourceToolCallRequestId": "66b5f5e8-9f12-4870-9e1c-15a4288f3b76", "timestamp": 1748199870583, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "a6f27362-9ee2-48e3-b67c-a5459c89a753", "timestamp": 1748199891768, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "315aaf5d-82d3-4558-b75c-1f91c5294169", "timestamp": 1748199911695, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "2d6d3896-e0f9-4e97-a401-2d1b5da7fdef", "timestamp": 1748199927990, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "1ed3f6e1-23b9-4919-8438-75368d15de07", "timestamp": 1748200340945, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "17e4010c-7f30-43b5-bab6-f5c3cc0f52b5", "timestamp": 1748200353674, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "62d36d50-b5cc-48a7-bc41-66a737eadf28", "timestamp": 1748200366007, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "b0d5f9e9-7210-4056-8ce6-ff4736fcb1f2", "timestamp": 1748200389036, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "d0e9facd-61b3-4451-8c2e-69dad25b84a1", "timestamp": 1748200433677, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "eb327731-9cd0-4331-ba39-4fab9e656537", "timestamp": 1748200581765, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "871797fc-4434-4b78-a4a3-cc7a435bf9b9", "timestamp": 1748200598777, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "a17342c9-51c0-4c0c-b648-470a4c304d86", "timestamp": 1748200611570, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "66474056-7b6f-486f-99b6-bc3c2ce76dcb", "timestamp": 1748200640439, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "e1b94344-3c2c-46a1-a37f-740827b65b42", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "9a992f9c-68fb-4215-913a-a90ceee049ea", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "72478bd6-0d32-4018-8adc-7d656863fe60", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "6258ff32-9a94-4279-9013-73678015aac8", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "5417376d-c2d0-4c2b-a603-a1bc90411bd3", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "2359a3d7-ff93-4ed5-abfe-7844123dcdde", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "987f1b85-87bb-4946-892b-a1444c8c119c", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "e3650751-5d9e-4199-8eba-38d5ef2f4b79", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "0aca9036-e53b-451a-815d-395ba5cc61d2", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "b8279e31-8eb5-4b7f-a770-3b8161f74f24", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "9adfe6e9-421f-4b91-bb29-a0979d280547", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "244208a4-b5dd-4137-83e5-a2b37f0dcfb7", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}, {"sourceToolCallRequestId": "a43fe017-1da7-493a-b9f0-1477ed2e332f", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py": [{"sourceToolCallRequestId": "dc5988fe-fb1d-4e84-a174-b612cae971ff", "timestamp": 0, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ldm/modules/image_degradation/utils_image.py"}}}, {"sourceToolCallRequestId": "167f2f3c-34cb-4a97-9356-b297914dd9b2", "timestamp": 1748200424514, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "251b8056-e32b-4128-8fb1-3a4e0d14d80e", "timestamp": 1748200433677, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "5eec6b45-d1c9-4417-8425-3ff88e4adcc3", "timestamp": 1748200712570, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "20ccfeda-2de6-4f9d-970a-8e5350f287cd", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "6682d015-eeea-4406-afae-41932fcf19ee", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "8ab45577-8ee0-40c6-a693-76e5273e763d", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "74e2babd-b60f-4d93-8a63-b66cdbe50621", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "2bfa3526-2c32-439c-8add-900d74c3a1bc", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "24348a17-fea0-469b-9c80-71a7b6d2249a", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "731bc4ed-3797-49ab-9f00-27bf8f4c383a", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "2e64d1d8-5e2e-4ba8-a25b-a905451e2c3e", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "6aa4600d-b1c9-41d3-89e0-9d484b12cf3f", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "79a0df3a-9ad4-42fa-a78b-ea9c321c4f23", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "4a0c8188-9eb9-4756-84c5-3e8838c89905", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "1c91b7c9-0c7d-45f9-bf3b-bf36c2603019", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}, {"sourceToolCallRequestId": "877c59f2-10c3-405f-be10-050357471a35", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py": [{"sourceToolCallRequestId": "74040ed9-de68-4f6c-a6d7-22e4ea809f98", "timestamp": 0, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/taming/modules/vqvae/quantize.py"}}}, {"sourceToolCallRequestId": "16fdd008-acb1-4e12-9775-e814263a0894", "timestamp": 1748200797014, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "1a62ad4f-5f37-4748-b933-3e4961824150", "timestamp": 1748200860386, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "8965b7a5-499f-4105-9982-92a8c8c8bc23", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "8696d4d2-212b-401d-af53-6d1df203308c", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "bf0d1716-857c-4dba-8994-d5f20fa9638d", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "4ce254cb-313f-4bb2-a0f7-8765020d36a3", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "5128b36a-9b5f-4f6d-a90c-3a682886d269", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "946c66f7-af14-4785-b5f5-3f08b1a7c017", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "b06648a3-81ec-436a-8113-1aaaaa0406ca", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "4538303d-234b-4eff-8335-483771a9fef9", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "e50a77c1-53ff-47a0-9981-df7f25aac330", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "d19eb147-bb03-4294-b617-8ef37d0d4fd8", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "5176e56d-9722-4158-a9d8-649910f92418", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "171b2459-8202-4a8a-a6d8-4720b8531fcc", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}, {"sourceToolCallRequestId": "be68419b-dc69-4127-b8dd-14c1e10d25e1", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py": [{"sourceToolCallRequestId": "23949d07-b52c-40f4-9139-ad7e2b99f73c", "timestamp": 0, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/taming/modules/diffusionmodules/model.py"}}}, {"sourceToolCallRequestId": "66fd4e6e-2a35-4fe5-90ce-a7e95c776215", "timestamp": 1748201016291, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "9bb58dd6-56fd-43ab-a0ed-998c0ff64e71", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "6637778f-d08a-4354-a3e0-67bb4dcbca96", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "46c8e789-3fd9-4de9-869f-c6dac11385b1", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "4392cd82-24fa-4ef3-9095-27301c3a5b24", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "8888fcda-54e3-48e8-a0e8-d3a925a20fde", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "f455ce2c-2ddc-446c-b942-41ec116e1c9b", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "bc36bb24-7651-4fdf-bc72-81fb263f2ba1", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "fd99d4a6-dd02-4489-b673-c46e449cea06", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "a74c3edf-33dd-4daa-bb79-8a2ffda8e3aa", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "0b383ad8-79f3-420a-aecb-d86ea6285df5", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "e13613db-fe81-42c4-9abf-a38774aeb6af", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "48fb999f-b220-4e7a-9671-705f88a3dc2f", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}, {"sourceToolCallRequestId": "3f4f2684-13a2-4653-85eb-2d721108c57c", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py": [{"sourceToolCallRequestId": "7e148ad3-dec9-448c-8392-76fe9e127ce6", "timestamp": 0, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ldm/modules/image_degradation/bsrgan.py"}}}, {"sourceToolCallRequestId": "e2de0b1c-6ca4-4608-9a97-134c7423e6b5", "timestamp": 1748201100822, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "77a696c1-0e4f-4cec-b454-bd16b407e8c0", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "aa4f51c5-ec37-4bc0-a3f2-f48c3612be07", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "c7196aaa-b454-46d0-8391-a22859142591", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "ba3d456f-71ca-4c43-a255-711d2c101b81", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "194b8fed-abd3-4a99-b1fe-3da4bbeea89a", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "e230dee0-5f16-4037-b5a4-ea3f93c20d64", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "daf51ccb-b3a9-4a26-b4fb-3ecf8512df43", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "30d10721-dd11-4d9d-9e0e-3153efb6ae41", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "04c798ff-c87b-4e34-aee8-2388ff0aa6b5", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "1ed31d9d-68f5-45ca-8cdd-e8f48efe17ef", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "da396a5f-7023-4ba4-9273-da22a4f4f388", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "f1504f4b-4279-4a9e-8a21-6d927ac97fa0", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}, {"sourceToolCallRequestId": "d0affff9-fc93-4148-b8af-c6fdfd0cb252", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\test_environment.py": [{"sourceToolCallRequestId": "9679a10a-9919-4625-867b-0393299bf406", "timestamp": 1748207236468, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976555, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "9e561e59-bd50-46bb-bd52-9bf83e754892", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "24952c89-6d69-4de6-a029-d40ec10b4566", "timestamp": 1748278989659, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "21496f61-915a-48e0-90c4-af376ebf1d96", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "e0af9fd5-8b39-4098-a2d9-ba29d3df1ad0", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "7d77b36c-75fe-423a-8968-a4dcd6d216d3", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "86c4d685-a0a1-4ae7-9a19-87637558b32e", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "28163c67-18d0-4a22-af6a-f202e6180e81", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "cc1bd40b-06b6-4f6c-8830-98526b2a07e3", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "39a52458-ea28-4ab0-997c-c7c3b1f5c6f9", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "1326d2fd-c8a3-46e7-a9f9-4a1de875e25c", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "cb827ad8-a22d-4188-9c34-72dc6b7f6a8a", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "3e3900cb-b5d1-47e9-bfb2-994f00d6d410", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}, {"sourceToolCallRequestId": "48db0e72-81bc-4f3a-a236-cf58a6ecc2b2", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\test_components.py": [{"sourceToolCallRequestId": "231af94b-1afa-4a30-95f1-9d6f98cc9121", "timestamp": 1748207264559, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "81f7f940-bcc8-4c3a-95cf-acfb01c10b35", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "03395422-6db0-47b6-8562-0e70242a331b", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "dfe8e985-efbc-4195-b744-051ab68b3f8f", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "37f7e34f-25aa-4e56-aebd-a67d07257fc6", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "1d2854ea-94c6-40e3-84b0-99ce5ec5fa23", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "800f64d5-dd3b-46b5-a5f7-112c7543ba62", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "82393f7c-aabb-46b2-b8c1-6fc24fd73453", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "3f7793ce-d696-455e-9a6f-0af34f2d1209", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "9c4781bd-2730-4b68-85d0-04a963d25a6f", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "d22bc247-9423-426f-b086-55eb0548fd3d", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "195cd80c-8207-40cc-8134-83c40f48ea15", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "3fcd53ab-8129-4cc7-a646-2cd414f09817", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}, {"sourceToolCallRequestId": "6af60233-06cc-448a-829d-6c4d733433e3", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}}}], "************************************:.": [{"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976460, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976474, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976483, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976511, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976521, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976530, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976565, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976873, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}, {"sourceToolCallRequestId": "0792c271-4ea2-4ecb-9247-1c774b869bf3", "timestamp": 1748240976893, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "", "relPath": ""}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\comprehensive_test_suite.py": [{"sourceToolCallRequestId": "2b056e1f-f180-4c9d-98c2-081a55c1f5fd", "timestamp": 1748207405299, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "ecd24384-d9f7-4b4a-84b0-afea02b5dba9", "timestamp": 1748207435619, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "5f3fd841-94a2-4eaf-a7cf-ef57871c8cfc", "timestamp": 1748207473104, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "f3de16d0-ac9e-4218-89fb-a0d04eeadfd0", "timestamp": 1748207504352, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "d1577da4-2d82-4c0e-8a4a-57b1223cd888", "timestamp": 1748240309872, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "0e439901-0aee-4bcf-956d-a0ebd2a529a3", "timestamp": 1748240321535, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "f3e688bf-42d5-4552-9207-80642fa5aa3c", "timestamp": 1748240336300, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "4609e341-b058-464d-8342-a77b3daf29ea", "timestamp": 1748240352817, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "952526b5-cd0a-49eb-898d-3551a59fed01", "timestamp": 1748240435855, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "135da137-c525-491f-bc26-61dfea8856a1", "timestamp": 1748240451344, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "33cb6adc-f652-402c-94a1-016531530278", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "fe7acfe2-beb0-4082-9a64-d034d020ea80", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "586828e6-4b5d-44c9-8b60-1d85e2627bed", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "2a42b3ce-69d4-4784-bc4b-4d31b797d8d2", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "cd300f05-73ce-4c64-8230-3d1bc9265a68", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "701fd411-6654-4076-8a83-ea1a73906b13", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "ea103e47-ba7b-472a-9b79-3da37f58bd62", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "0aa92f63-8d32-4ca2-8c8b-54e4bfcd76c0", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "99a229ec-d0e8-43a4-b43c-b73656c91bd2", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "99efbaaf-d433-4744-8d43-eae36b96a972", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "8b8525b5-1824-4876-88f5-dc105c0ec728", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "ee893af5-c9f9-48f5-a497-4d6ef6f33403", "timestamp": 1748279128505, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}, {"sourceToolCallRequestId": "7904c850-91d1-4c2b-bf8e-515b33c5a2ba", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\comprehensive_test_suite.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\run_full_tests.py": [{"sourceToolCallRequestId": "7e42bf82-0961-42ca-9781-fb1ce8abe387", "timestamp": 1748207553010, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "40270617-d5c9-4ed1-ad25-ca9a53e342d6", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "6bad7cca-c903-48c6-ad55-fc509557dfd4", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "8143dbe3-7273-4c50-b092-abf6c13039a8", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "38b7fec8-71e6-46d4-af0c-425718d434c0", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "04a2d706-6233-4851-a284-7d9e0c218d58", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "3514b946-dd52-4fc7-bd20-f348702feaf2", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "57568a88-5f8c-48b6-b76c-ea3bcdfcd516", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "6ddbbcf4-80dd-49cd-b603-bbef11ac7bbd", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "bd1ac3d4-0c0d-40dc-9d0f-de1aaa6e2054", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "c9fd679d-e680-4b2e-861b-d4972aa42948", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "23dd9925-c6d5-4c9c-b882-ba5b922317e5", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "c2eb6eb5-2e67-43b3-b259-01e93f72dd40", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}, {"sourceToolCallRequestId": "9fe27124-2331-4fc6-9f57-ed02a08834e5", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_full_tests.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\quick_test.py": [{"sourceToolCallRequestId": "61288870-ef16-4f47-9002-13cc1921b516", "timestamp": 1748207607886, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "9fa20415-6064-4101-bf19-c1dc0eab5cc0", "timestamp": 1748266937810, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "60a2a24e-2c2a-4cd7-b26d-166cd32e7500", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "2369b851-43f1-4649-a5e6-6a437cccc87a", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "ef1ef31d-61b1-4eb7-8e97-dc24a5970364", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "153b4c1c-a516-4acb-b90f-5801ef517498", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "43812780-f613-4ebc-9066-bbb492795862", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "9fa31635-92e8-464c-b31f-c085620002a0", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "3b6e2119-d9e0-4781-8b66-1d719a2926d1", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "44e8894c-3e85-4ab9-88b8-1e0fc0e182fb", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "91911da6-4b34-4fdb-835b-c4f3fba89d36", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "78b5593f-e243-4321-ad7c-5c54bbb60855", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "a4dc4cff-2a11-43a9-ae47-b9021f35234a", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "7c7ee91f-e4f8-4d16-9132-863091ca6328", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}, {"sourceToolCallRequestId": "cae3524e-4cf9-445e-8f04-8595d7f7afaa", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\quick_test.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\TESTING_GUIDE.md": [{"sourceToolCallRequestId": "1e62153a-9336-45b1-b8ad-27b10dfb5a79", "timestamp": 1748207654372, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "7c8c3ec9-e9aa-4cb7-9194-60930131175a", "timestamp": 1748240524225, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "9b89eb58-0587-4c76-9372-e50ceb18e471", "timestamp": 1748240536727, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "c605eb4d-c5b5-4a60-bd9d-4be3e2607bf0", "timestamp": 1748240554459, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "f9b457da-0f03-4516-a1bb-45a829c26ebf", "timestamp": 1748240583564, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "d29b8c61-16fc-4016-a06a-64b8161fe253", "timestamp": 1748240602688, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "e77af044-ab14-4bca-8fcc-c30b725ca4be", "timestamp": 1748240615269, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "da29f821-9abd-49cd-a6fc-901ea7b08409", "timestamp": 1748240629018, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "cba89385-4927-43e3-9136-12ff4d1ad6d3", "timestamp": 1748240654217, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "a30df187-ad6c-4569-8f4a-e1f6f29ee944", "timestamp": 1748240668357, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "a889f4da-900a-4afe-8c8e-77ebb7e22b24", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "95ede3bc-1236-4988-9dcd-2f9ee4327dd1", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "dc98e8ff-803b-435a-b369-b21e1da0441f", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "03076b7a-8367-4dbd-bb2f-8d38339f4e07", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "24f47ccb-d103-430f-a44e-eb9f3af602ce", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "413923f8-e4ca-4be1-8dfa-645cc62f776f", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "ee27fe36-2217-4c12-b942-48306b7b8f61", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "a534fb6b-ef1d-4dda-906a-52bcc6f6b295", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "196035fc-7077-487b-8a31-e81c54f1f3c9", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "a6b6d3c4-4cae-4a45-9a5b-1c0f991234ec", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "6a83a1d6-b79d-4dcf-a211-d4dc473cc124", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "575d28b2-091d-40e8-9e43-7dd0bb1bfd4f", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}, {"sourceToolCallRequestId": "15758816-4883-4f3e-8ad0-370b7fa62c49", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\TESTING_GUIDE.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\debug_test.py": [{"sourceToolCallRequestId": "491981fb-83f2-4cc4-87af-ed04d7d1fba1", "timestamp": 1748208749735, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/debug_test.py"}}}, {"sourceToolCallRequestId": "0fe006f1-753f-4fd9-a05a-22b67fc5e7cd", "timestamp": 1748209059690, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "f366722f-419e-48bf-a8ab-5d3a5061b167", "timestamp": 1748209075779, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "7d9df087-57cd-4ebf-8d26-ad1cc36f01c6", "timestamp": 1748209090724, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "dd8f75f7-6b33-4518-afe3-d310c9d0e467", "timestamp": 1748209109214, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "9b1f3bec-251a-4afd-bf4f-a40c578d7bdf", "timestamp": 1748209139434, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "ea6b21f6-a765-442e-aa24-56fb19b33896", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "04dc7d93-55e5-4211-9da7-4c2127045083", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "bc8080e8-ae22-4faf-af42-5faa9050aa9b", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "44689225-2e25-4acc-b96b-451771983224", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "939aeea5-68fb-4da6-8334-e79525dc82ee", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "269a1d0f-b937-4dc8-a525-339238c4bbfe", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "ff4c4006-c196-433d-adcd-47faeab424a0", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "2fcc32a4-efc4-4160-b2f3-8847e265de55", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "6edcc6fd-2e1a-4c08-b8c5-f97dc3a4e2d2", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "f72b1122-5429-48b8-86e7-4e9bf48d6885", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "24bb192a-2f90-48a2-93d3-86222149fddf", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "46a80fd7-40fd-47a1-a7fc-24e225dda221", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}, {"sourceToolCallRequestId": "4e9adcf2-779b-45dc-bece-40dd8102c32b", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\debug_test.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\safe_test.py": [{"sourceToolCallRequestId": "0d4a1bf6-6e52-4e5c-870b-0021efed9ed3", "timestamp": 1748208788826, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "f6f31fee-6868-4fe8-9060-286067684871", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "9576309b-ecd7-4fe9-9f19-098ccd04f918", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "e65cf015-4421-433a-926d-e251507d6ce3", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "b52f9dbc-3da9-46b6-9c6c-b189f3fb98f3", "timestamp": 1748278992575, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "d840384d-5f57-4064-b64f-dad523a31fae", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "72c0227f-10b7-40c6-86f9-ddb87e313327", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "808a5260-daf2-42b0-9849-d2f3615b50f3", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "5792ea8c-b804-44d4-b471-7f5fd4a2e355", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "1d9f448d-1771-499f-8428-85c0f245c6fb", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "478a8969-3073-40a1-a332-97cf1c1ac703", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "0894d5bf-6f8f-4afa-9cac-387908f500b7", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "88353852-37c5-4e74-bc07-fbec846425e2", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}, {"sourceToolCallRequestId": "41f273e6-1fae-4b12-8387-4fadaff69e21", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/safe_test.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\simple_debug.py": [{"sourceToolCallRequestId": "f1f8c017-736f-4c2a-a6e6-952a924564f3", "timestamp": 1748209169935, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "1a667761-605e-41a5-a443-215daa34f110", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "f2e54e0f-d99f-4aa1-85cc-7b34d1e6c09e", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "71f4b801-bb0a-4f32-b4d9-98b2151f6a08", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "df79eaab-6ba5-43db-b704-66a91aaa7357", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "df4bdea3-2926-4c6d-bd14-fc266a1300b7", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "38df03a7-3371-44d8-8f7e-8b1957ab7e39", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "e76b3645-d10b-4f22-ae7e-9f7ed1ee56c8", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "071d63b8-5744-4ebf-82c1-a153cb9f4696", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "8f253e91-7485-40cb-9c18-4f0289860e58", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "b6881c35-0015-4191-ba3d-47e0eea5f3f2", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "1b573074-be95-4a0f-88db-635c8a2b708c", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "0071a599-a72b-4fc0-8640-8a1f318d5b21", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}, {"sourceToolCallRequestId": "f56c0c5a-a12a-4d10-b0a3-dd4f7f03749a", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\test_safe.py": [{"sourceToolCallRequestId": "0ea4d346-c9cf-481b-97f4-9b105ad1de1b", "timestamp": 1748209403417, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "9c9c66d9-71c3-4dd5-97b8-f57ad5ecd22d", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "8dde99b3-5e97-4fc5-8d43-337bc01a2791", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "05435a7d-9690-4c0b-a52d-03c2e0a44662", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "ae68d496-1c42-41b8-9786-e899282235fe", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "53577d7e-c266-4821-a630-4b86b02dbf62", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "b5e5055f-034c-45cb-8662-10980075f754", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "a728b801-a13d-47d8-9e73-13ba652b136b", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "746fd642-1c58-4755-b68f-a98cb34c7c72", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "9d0a7de1-cec3-4ee4-b456-f619a0d227e9", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "ee629d56-fd9e-4913-909a-65fb18f2ee93", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "388b71da-e770-44a3-8183-d76c11d8dd42", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "19610392-0327-4932-80fb-da9410c2418f", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}, {"sourceToolCallRequestId": "b5989d82-a29e-4489-9340-af2b1790886b", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\working_test.py": [{"sourceToolCallRequestId": "cd7ed7fc-37c4-4fdd-bbfa-0d28013b3e68", "timestamp": 1748209441271, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "437598e6-29a4-4370-9bd6-e4e967754b64", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "bbcef83b-4423-4db2-a3a9-1f504db5ce26", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "d78074ae-11e2-41e1-8b5e-85dbda282188", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "20c099b3-930a-44b2-9e0b-93a73b2619d6", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "ac3620cb-017e-42bd-a3b6-58fa153998e4", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "c960e087-56d0-4721-847b-30b472bde660", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "c1357104-9d6b-45c1-9116-c84b8570b958", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "50f652f1-7a92-46d2-b8cb-c2a3517f927a", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "63459f4d-15ef-4329-86db-139fe8fde5f5", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "11fbff00-7a01-424e-a384-fd3e8771f7a3", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "7888101e-d7b6-4232-941a-857d007d4a3f", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "b0cfafc8-b9a6-4d42-b881-90cf8833fe3d", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}, {"sourceToolCallRequestId": "d7d2fc89-d6a6-4b3e-94a7-1df7a5b7669d", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\minimal_test.py": [{"sourceToolCallRequestId": "bf19b8f9-1b56-48bf-9e66-bf0d2aa9cd6a", "timestamp": 1748209601325, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "0b81b9a8-c5b5-4426-bccb-89489878d126", "timestamp": 1748266937810, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "c4135a33-2133-49f4-89d9-a00b161c0ec0", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "461dea09-4064-4a2e-a000-0a96b4082147", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "91cb625e-60a7-4f6c-8995-616b93cb5ea6", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "61f45ef5-2d96-4679-ab0c-8a2e1d44d1b4", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "33b662b4-61dc-4934-9b05-baf4e551c43f", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "d76e7953-816d-491f-a91d-6e1a6ef08489", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "224b2375-7bbe-4e0c-bdd4-eb1470640f81", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "edb4c24e-ebf0-4a29-9a38-7b1dc5d58fce", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "80b09a27-8548-4ee4-acd5-0f5a326c43fa", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "f10fcf1e-ab7e-4701-9761-cb4f0feea5a1", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "3ca20c63-15fd-4be9-983e-d41e7f84736c", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "071d43e7-9f07-4fe0-9e22-7344af8c1214", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}, {"sourceToolCallRequestId": "82d71692-272f-4ae0-8757-948ad2037336", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/minimal_test.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\cpu_test.py": [{"sourceToolCallRequestId": "4d7ef673-a054-4240-a6b0-80a8e64295f2", "timestamp": 1748209618919, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "5fc2c6b9-ca64-4b7f-b231-ec2ebe1f5bd8", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "1bc327c6-5144-4204-b661-ebe7227e5210", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "11f3b82d-d7f6-44ca-a215-ed916b5af8d8", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "a791b7a6-a4cc-45c2-8cb4-8e2e83ca60af", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "4541b59c-b098-4632-9c1d-ca2c58ef9585", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "f7a894aa-bdb4-4a7a-b637-a5727468af35", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "0ce3f1a6-b867-4112-b2ea-c3b6f7f4cdf2", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "f8939a75-ec30-472d-9b24-48da88fd9b29", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "83e30f0f-0427-4b0d-a8cc-c1bda9073e20", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "8e4b6a65-a400-463d-88af-d241d6ae815d", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "92e90a50-ac05-4a0f-a5bf-a8de93e8d706", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "f3c86fc9-2e09-4fc4-8d78-2d4770ef6fbd", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}, {"sourceToolCallRequestId": "6fb0b226-8f1d-4765-b7d7-9afafe1f8d55", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/cpu_test.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\diagnose_segfault.py": [{"sourceToolCallRequestId": "6d40658a-6792-43ca-8966-9cad66251838", "timestamp": 1748209650148, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "4752c22a-c9e5-41e0-94e4-5077fca710f8", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "158c9b19-5b94-414e-a533-78cc0bd6878a", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "37d7314b-878e-45d1-b3fb-8a399e898b0c", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "26fcc5f6-5e62-4ed5-9e32-45091253c683", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "c564e510-bc63-4234-9ce8-738ab894ba7d", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "3707de13-06c1-4da3-a9ef-2653ee253bd2", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "1d609b7d-8ea5-41b6-bbb7-190daebcebdc", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "11fa1889-7a1a-42f1-9ae5-14d9c9a202e1", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "56c7ec8a-175b-4904-905a-26596b41866f", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "b9c51da1-a2fa-4f01-b544-8bb056fab79a", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "d77caedb-0f1e-4def-99e1-b0d493d99f7d", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "03e8e6a6-68c7-4cc3-a792-408f19c8483b", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}, {"sourceToolCallRequestId": "60958928-5230-4a57-bfc6-25d7ee88973a", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\diagnose_segfault.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ultra_minimal_test.py": [{"sourceToolCallRequestId": "570504b1-48a5-4e6f-9f8d-d87d7038c2ce", "timestamp": 1748210359787, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "c2bba089-0f74-4891-b041-aeb0a06a316d", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "c3a3bf7d-8a6a-4333-be1a-a4c2b717f2ad", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "3e0acd42-c731-4cfc-833a-b943c7329bd3", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "3d699aff-d125-402c-b691-a86e48057a0c", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "7d1fa759-95de-4f91-925a-251f528ca4b9", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "61f5fcf5-2e58-4587-b983-e9ea0e389e3f", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "a72b79b2-abe8-4f00-83b5-8aef7d966095", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "d75547cf-e194-4d81-b8c1-d2013c99ddae", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "8d55f771-f96e-4c73-981a-6f4e61102b67", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "8b351d05-9a60-4be4-a6e0-00c62c8c0242", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "6597ceb9-318f-4750-b6b4-490fefb46752", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "3e83ddfc-080e-4c7b-86ca-94c15f4bc1d8", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}, {"sourceToolCallRequestId": "0b273b31-3a67-48c3-a727-e1b35a12846f", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\bypass_test.py": [{"sourceToolCallRequestId": "4ffc0d4c-afbc-49fc-a6f1-bfe10def36e0", "timestamp": 1748211216175, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "0639a162-2b7e-4699-8a44-df53e3b04427", "timestamp": 1748239560397, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "df12ab7b-c466-4972-8e4d-fb70eaeedab0", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "c445e324-6367-40f8-a941-8f9ce9d75df8", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "b0796d1c-c2e7-4ed7-bff7-e59a0b9290a7", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "93ad1c13-cc4b-4ce9-94cd-8dcad653da46", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "ccb4a0f2-3c2b-4b29-bc71-87e07fb64167", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "e1d0aa9e-2d2a-4b70-8630-bf5c3d26189b", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "d603883c-7e94-47c0-9ed2-36dac86c0389", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "19e19cc9-2a15-499d-a9c9-43e95104fce7", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "ca2de62a-3e0c-44df-b1b3-8fc8d573e7ec", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "1414e353-4b8d-4ebf-a88c-2112759a9094", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "02c14d1b-4a28-4ab0-bb9c-4b8631b9ff78", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "466398eb-07d7-4bd4-a1f7-466e1bb92680", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}, {"sourceToolCallRequestId": "0b7e6e12-b0c6-46e2-895d-dd1a947d3cb6", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/bypass_test.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\docker_solution.py": [{"sourceToolCallRequestId": "1d1520ae-e864-42d0-a2c4-983dc7f134a9", "timestamp": 1748211262329, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "170223f3-0d20-484c-bd43-538fc9678690", "timestamp": 1748239561717, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "dc272dd7-28f5-409b-b35b-eb79026a4cae", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "6490c895-4c03-469e-803a-818aa2c1e3fa", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "fd411c8b-e832-4a2a-b1fe-7753a7e08952", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "ee02cc0c-0787-479d-b6a4-16b3399a0adf", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "a14992f7-d3af-4b8d-89dd-e<PERSON><PERSON>b227d", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "2ca0d8c8-2e4f-4746-b0f3-9fe9ab3b9838", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "207800a8-18d9-4718-b116-dea8a346db43", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "5ce5eb29-956b-4133-9f15-63ad3651b2bf", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "48dbef38-e278-49f2-997e-6e1abaea96fc", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "f02ea93b-d2bd-43be-8b06-61b390c10469", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "ac94531d-7a9f-4389-8270-4bddf4f742db", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "2d713786-4598-4b4e-a307-5bcab47fac6e", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}, {"sourceToolCallRequestId": "35ca72da-9532-4e35-b6cc-1b37085d2e87", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/docker_solution.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\universal_logger.py": [{"sourceToolCallRequestId": "8975763a-633d-42c2-9241-d4e6c33026f8", "timestamp": 1748239182320, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "7625be1c-3301-487f-90d2-0539884508de", "timestamp": 1748239566504, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "e60e4128-85c0-4970-bf5b-201da1f7c3eb", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "f152ebe3-3a85-4093-b80a-9b988277ebbf", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "2e4b996a-14c6-404e-bff8-b5978f342742", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "47c5314e-a915-4e79-819c-ccefb304d52c", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "99a5a2f1-f533-4d23-9f4b-2aa825124732", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "89b76791-9a95-46e6-8536-1cd475874587", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "6d8d7263-ede3-4c81-885a-a874fa312506", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "10aa6862-bf99-440e-a8d4-15d4bd3e098d", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "7bb96a19-bdbb-46e6-b490-d505e9e4217e", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "c8f60714-3cd6-431d-b39d-f095480b9aa7", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "e8f2f167-80c7-4dfe-ab80-399a3a3feac6", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "b185d222-cd56-4aa6-af68-a5501caf46c3", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}, {"sourceToolCallRequestId": "ad35c1a4-bc93-49f4-be6f-1813ff946ef9", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\logging_examples.py": [{"sourceToolCallRequestId": "a7e0a7ec-91f8-4b7d-97ed-0ef88d4d7a7b", "timestamp": 1748239214787, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "919a6878-65a4-4aed-a3ac-11779815e7e1", "timestamp": 1748239575318, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "e0fbddc4-5d99-4855-9179-198e90a5389d", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "3591eafe-b993-4c34-9ac6-1221d6178b63", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "6b96b22a-299c-40fb-aa55-467ae52f8a99", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "9704993c-89d5-4c0c-8f18-0454a24f4655", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "d979adb0-3c83-486f-8f24-914a3f26deb6", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "5ba00232-d017-467d-b947-09d1193ec14a", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "f89535aa-d7b3-4f03-b549-63e310b5dee2", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "f7a06d60-5c0e-45de-b803-8208cf2e232d", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "e838178c-2115-4c35-8c7f-26ae9f93862a", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "13b80756-6724-4bd5-b737-b6d00cce4a28", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "35b787ab-7579-49f4-97c6-db95a8b43b1e", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "438ab63e-e100-4bc1-bf52-ebda31ddb43b", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}, {"sourceToolCallRequestId": "8260e250-846a-458f-bb14-b4c4073f9aa9", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/logging_examples.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\integrate_logging.py": [{"sourceToolCallRequestId": "bf10332f-b664-4b85-af77-f462e5f19ed9", "timestamp": 1748239281088, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "f704cafc-dd08-4e54-8cba-8cdf9908f8b0", "timestamp": 1748239570245, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "0ed0ea71-b0b4-477b-8679-2fc257a9ca2f", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "4fb1540f-6397-4084-9d49-9fc8484a1a53", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "85116e8e-d7f8-4676-95d1-1a1b46f2a533", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "21ca8647-46f1-4b2e-bbfb-dd93dbd8b304", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "18ebd693-cddf-4e44-980f-eab99b2dbb7d", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "8d7cfc49-1415-40b0-b185-d020bd4a3788", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "c1439300-1b17-4a76-84ff-4d8332708f71", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "1e6f7dfe-3a53-4a99-8a05-6f74a755be71", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "fa3102bd-32cd-4930-8326-485f2b670e1f", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "d461fc6a-5f76-4db1-891c-e786b33a9ef6", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "6e998095-23fb-4866-84dc-d2124befe743", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "56e7ec45-80b4-4c8e-92c7-719e665b0e36", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}, {"sourceToolCallRequestId": "fafd016e-8e98-43b9-90ca-8668644a4a6e", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/integrate_logging.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\auto_logger.py": [{"sourceToolCallRequestId": "a658bc07-082f-4d1f-9dc7-51b710d5a920", "timestamp": 1748239413122, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/auto_logger.py"}}}, {"sourceToolCallRequestId": "815ca5a1-67ae-4c73-80e5-22a47dd703db", "timestamp": 1748239647996, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "278679cc-90e2-46ce-9315-46c41ff1de1d", "timestamp": 1748239680196, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "1710652b-f1c7-4520-9518-a49c5398bb49", "timestamp": 1748239693660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "c82ac7a1-2947-4490-9fff-d41d533d21c7", "timestamp": 1748239708077, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "5d23c748-fac8-4585-b6ba-5f985f3a249b", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "e4ec4e3c-2c0d-495c-b47d-1f94957ddc9d", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "f6268f80-6b38-46a0-9af4-e7817637d78a", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "c3190d0f-1796-460a-b0c1-2fc685116df7", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "30279d40-4e5d-4789-92a2-f1f32d264ada", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "80d62fab-31c8-49ad-b481-20b81de37a78", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "a699bba0-cb21-45ed-9f92-d89270da44ae", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "58118f20-8b54-4a92-93d5-6793b9307b21", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "ea731260-70a0-4780-923e-19162a526982", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "0c4dd9b7-9203-4c68-99e4-fb49f180debf", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "b9ba4e4c-7e33-4512-a86e-945a586a2ab4", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "17e17b56-619a-4884-9baa-6107aaaa9e26", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}, {"sourceToolCallRequestId": "fd63ce17-6633-4c1a-a34c-31ba585f60f5", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\auto_logger.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\run_logged.sh": [{"sourceToolCallRequestId": "d5f27dbf-5383-40c4-8539-3fca12df20d5", "timestamp": 1748239427592, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/run_logged.sh"}}}, {"sourceToolCallRequestId": "f0da8303-99e7-4878-8c74-727074196a1c", "timestamp": 1748239874308, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "b9930748-fbf1-4469-a9c9-a4b8d2c22a21", "timestamp": 1748239894378, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "3f268463-1329-41f5-b6cf-e5072cbd83b8", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "890b7304-5ebf-440c-8124-4008c7c06f2c", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "e206af0a-f8cd-423f-804f-7c293b5dd839", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "48111f41-4747-4821-bfa3-458dddb92107", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "2822be4d-6287-4222-b156-c55f39e6b468", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "e3047ad7-792a-4f82-8d19-50b53de2b0a6", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "6231793e-31c7-4428-a1c4-c9ac237e997f", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "5790de0e-9f91-41a2-9657-ae4fc8fea87c", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "33b07d5d-a380-497c-9554-25ee64937b76", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "d0ea2d15-e63d-4ada-8cb9-cf0b3af60468", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "0e52ae44-c81a-4ffc-a247-f89f5d0f2c2b", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "52a69a66-26be-4137-b95e-64155457284d", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "1c9cd362-9c0c-4a1c-bc11-7c29d9daafb9", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}, {"sourceToolCallRequestId": "d2365be5-b7fa-4d6e-96c2-12d56a68842b", "timestamp": 1748279132535, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\run_logged.sh"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\log_viewer.py": [{"sourceToolCallRequestId": "5fc70a26-547f-41ea-a177-db5c9a277798", "timestamp": 1748239454357, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/log_viewer.py"}}}, {"sourceToolCallRequestId": "0a28efeb-e000-4716-8f19-98cbcf280951", "timestamp": 1748239721033, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "aeea272f-7a11-46a9-8dd2-8c68e999abd4", "timestamp": 1748239734768, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "df879eff-9acf-4e5e-a15c-cbb46d9d8a49", "timestamp": 1748239747853, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "39e2e20e-b12e-4a09-83f2-714855d87714", "timestamp": 1748239761485, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "5a34c381-d769-4580-8b6a-60547161f92e", "timestamp": 1748239774213, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "ba7e8e5c-708a-42bc-bf69-16a2b20e7caa", "timestamp": 1748239787494, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "b56a15ae-e850-4dc9-8415-616a27a0df8c", "timestamp": 1748239801111, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "401765c7-fec9-4acb-a258-e929f82f49a7", "timestamp": 1748239814933, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "589caedf-eb08-4ed1-b862-4e80fe89fe6a", "timestamp": 1748239831927, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "231c2ee0-b52d-4c1f-b134-403623469c3f", "timestamp": 1748239846148, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "6560e718-3853-4e2e-8481-1ad6882d2ca4", "timestamp": 1748239859335, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "aeee563f-0a2a-4cfa-9cbc-e0c466cff953", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "eee87be0-be14-4154-83e9-0c9e82ca648d", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "4660b663-2b6f-4954-8be0-9927394549ed", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "07c706de-ba56-43e3-9718-dbccbc4b5d3e", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "617b9f1c-bd37-46ed-ac69-aa70484401e7", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "c3318199-3036-4acc-908f-3cfcef3cf822", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "2698d28c-695f-456c-8d25-7ae643ac71f9", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "6889f506-ed73-40d2-bd6f-e2127f8a6e62", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "5fd79f13-5a6e-42fe-ab25-ae2f76dc77b0", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "122d72db-4909-4f6c-b76c-7dc9f6de709d", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "f54f75bd-5f00-4243-afc1-02d9e42f9960", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "a8ddc659-9ac4-42a1-bc5a-6c644fdffa9e", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "96ad986d-c68a-462a-9e00-7c6681a97d77", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "dce1eb86-2082-4153-b25b-656e060facd9", "timestamp": 1748279132535, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "6f01ae58-c497-4f70-a865-55b93559fd7a", "timestamp": 1748279132776, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "06602db7-ff0e-499a-ba15-120969dbf49d", "timestamp": 1748279133020, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "7bfd61d8-3b4a-49e5-a20f-bb46c245be04", "timestamp": 1748279133506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "b732d0d6-4e1c-4505-95b0-07f8102734d2", "timestamp": 1748279133732, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "24b4a59f-4e65-4d74-9b0c-337bc1635020", "timestamp": 1748279133970, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "d40c97e5-b55d-4dfe-9b2a-4f8df68a4187", "timestamp": 1748279134233, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "9a2d412c-ef62-4e1d-acbd-5890c8fdbfd3", "timestamp": 1748279134716, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "1c1cb620-fcf4-42a6-9f9a-d1296c297070", "timestamp": 1748279135107, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "7ee5c441-53d5-49f5-982f-502f90dac303", "timestamp": 1748279135391, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}, {"sourceToolCallRequestId": "9d3e2e76-72e8-427f-863c-05aba6cc52c6", "timestamp": 1748279135686, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\log_viewer.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\setup_logging.py": [{"sourceToolCallRequestId": "b55287d2-ea6d-4f41-ae52-6a8e33a8acab", "timestamp": 1748239488409, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/setup_logging.py"}}}, {"sourceToolCallRequestId": "cff85f4c-c6c7-458d-889b-dd4eea3d0aee", "timestamp": 1748239908998, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "fb0af236-37a1-4d28-aeb1-064e544550d1", "timestamp": 1748239923497, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "1b5e4f19-9f73-446e-9e4e-6ff5765677da", "timestamp": 1748239949135, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "db7161e4-9758-4c02-a23a-e013c9f8e178", "timestamp": 1748239971964, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "0b75a730-8961-4a91-9a09-ad1647f86089", "timestamp": 1748239986328, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "eccab418-5c39-4f05-819f-90371bb083b9", "timestamp": 1748240000419, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "64ebbf50-fd4f-430e-aa9a-d8010a8a1e2d", "timestamp": 1748240013070, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "8b5935b6-e6ac-48fe-b28e-c0d091761508", "timestamp": 1748240026749, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "b0a74561-caa0-4b1c-ac90-210d7551c48d", "timestamp": 1748240042050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "1a0dc7aa-1a77-4624-ae32-c20ecc2814b0", "timestamp": 1748240058206, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "ac87a786-9ffe-48e2-86e7-bacff366207d", "timestamp": 1748240078841, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "dd4f4b09-d48b-4ac3-b9c0-a653b11ca5d6", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "c142094e-149d-44ae-8f20-a819be072bff", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "a01ebc4e-ccf5-4d00-8df2-0ef4afdfafbf", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "cdfbc090-0241-490a-bc85-f99e2b8885e4", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "5c1371e7-8169-40c2-88ff-839fab582807", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "4946114b-5a4d-4e10-bb6e-790a60586eb2", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "776732a2-e8a0-45f7-8142-4d718ee75b86", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "2dbe175a-e3a0-43a9-b9de-a955b324e48b", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "0522c413-e49b-42e5-82de-024216626b92", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "944d2f1b-f853-4a00-a8aa-0341f18049c6", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "02c7653e-5171-4fee-a866-56365b8aee86", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "8e6ac935-5719-4093-890f-d7d0f519cb3f", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "ae96cef5-fb07-4dca-8993-3c429c117f78", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "d0429768-b028-4dcf-a9a4-83ddf52122cc", "timestamp": 1748279132535, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "df79a148-a16d-4573-a5aa-42743b6950fd", "timestamp": 1748279132776, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "a6d35cab-cb38-4889-a573-2e9779eca413", "timestamp": 1748279133020, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "65dcc297-4a58-40f1-ae8e-07cfae894985", "timestamp": 1748279133506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "6770b78e-93f7-4bf0-bfd8-372382bb7e15", "timestamp": 1748279133732, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "194e6894-636c-458f-94b1-5f0f7e4707f1", "timestamp": 1748279133970, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "0cd2de7f-cc5f-417e-b533-ef89ec590f22", "timestamp": 1748279134233, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "bb8511c7-7b88-4370-b32a-3a8d6b0a5da1", "timestamp": 1748279134716, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "ddbb05ca-2c8e-44c4-8ecf-a4d34b3bbd05", "timestamp": 1748279135107, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "d21e4f4f-0348-4368-a588-640c780d6f50", "timestamp": 1748279135391, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}, {"sourceToolCallRequestId": "32397afc-3907-4a19-a972-cdadb59cfca1", "timestamp": 1748279135686, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\remove_icons.py": [{"sourceToolCallRequestId": "f11b8177-f2cd-4e8a-8f25-eb4903662a15", "timestamp": 1748240394134, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "e3a3ddb1-7b9b-470f-a431-500faf4246f7", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "3051f3cd-a1b2-40df-bd0e-99c8d672e0f1", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "393616c1-902a-416a-8df9-5f9a9674b3eb", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "ebf11354-5b4f-48be-adbb-44bc2520678f", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "2604ba31-75a5-4702-8c15-168b9f1a2f89", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "bad5f961-4005-4ad9-82cf-9efd98d539b0", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "a17e17f1-6088-40c1-8e86-51caf6fd5a54", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "6ca04dd8-1084-43b1-9d9e-a1567e60377a", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "7529be93-3862-41ae-a4e1-f29cabd3fc35", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "4543b4ab-e13c-4a2b-9669-fb359cd0b577", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "ecb9cf8b-1650-45f4-a5f6-5b2a6d22e471", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "18feaa6e-1042-4e54-b060-83e9c41a1e08", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "639f4959-f38f-4d52-bac2-61fe443a0f81", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "70e45fc4-e2b7-4007-99e4-f9220a8e3696", "timestamp": 1748279132535, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "dda697c4-737e-4f52-9fff-1523c84ef83d", "timestamp": 1748279132776, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "1a84a712-3818-444e-83b8-2b6501ced837", "timestamp": 1748279133020, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "65363e74-81e3-48d8-9e14-6289e8b7f750", "timestamp": 1748279133506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "e551fe02-40df-4ea4-a38b-869caf6e46e3", "timestamp": 1748279133732, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "769f156b-ddcc-4419-85df-faee63a36d10", "timestamp": 1748279133970, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "e9863c1a-ba87-4802-a6ac-07cabb5fee8a", "timestamp": 1748279134233, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "02ccea8d-7b3c-450d-b095-da9a4b62beed", "timestamp": 1748279134716, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "dc84e84e-d367-4f41-b934-6e16532456ca", "timestamp": 1748279135107, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "85fc0682-3cbb-41a8-92aa-155b2ddf41e9", "timestamp": 1748279135391, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}, {"sourceToolCallRequestId": "e6daba3e-4d46-414c-a0de-ccea07009577", "timestamp": 1748279135686, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\remove_icons.py"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\CREATED_FILES_SUMMARY.md": [{"sourceToolCallRequestId": "15a14319-1f84-4db5-8b7d-1e8b99842459", "timestamp": 1748241055598, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "9a7a4e87-0ed6-41c5-a60f-29634032b54e", "timestamp": 1748266937810, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "c2c4461b-3b58-4eea-a12a-6647e92d3c1e", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "1ccc67d0-24f3-4ad4-9ff6-54068edf6e82", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "6d6fcdfe-c958-4e2a-8339-87aa516c3bb1", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "466623ee-e0e7-4fea-b708-b0be6225ad84", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "c83576e7-9b16-4561-a7cd-b4930ba6a118", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "53de7019-fb25-4081-ada8-a905b52392b9", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "4c9df591-89d6-4ddb-98ab-ac8df44e5839", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "da0bbb35-2757-4e1e-9f7d-7da482eca880", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "2180967c-c630-4ae2-ad8d-233cfeb7f451", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "3429c37c-0e38-4f29-b55e-da3bf407117b", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "b328eda7-85d1-4ed6-8110-72647c54057b", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "c2bfe769-9eef-428c-a21a-1042c3393683", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "4deeb701-9e09-4071-bcba-b7bf1eb9e29f", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "c607880d-4991-42c2-9cdc-be1c82a6330e", "timestamp": 1748279132535, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "bdbe2fed-1923-46ec-97a7-1afb0411bfff", "timestamp": 1748279132776, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "a9456a16-3707-40ba-ac2a-078496bff0c9", "timestamp": 1748279133020, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "393f342e-eb3e-41ec-a824-b907cf27e733", "timestamp": 1748279133506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "d2a95913-e942-49f5-978d-56465a4c439d", "timestamp": 1748279133732, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "0049267e-fb24-4e31-b247-1b0bc0e0d3b8", "timestamp": 1748279133970, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "8c7b35a3-80ae-4d33-9723-dd7aa1aa7242", "timestamp": 1748279134233, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "00ce3bb9-3487-4fc5-8f4f-509ad4b73667", "timestamp": 1748279134716, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "cbc7d86f-f742-4ca2-a060-03a9e66117f9", "timestamp": 1748279135107, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "04f0975b-0af8-46f1-9e9d-275387e64361", "timestamp": 1748279135391, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}, {"sourceToolCallRequestId": "ceb3e34c-d9a0-48d1-8fa3-25dc7076d759", "timestamp": 1748279135686, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CREATED_FILES_SUMMARY.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md": [{"sourceToolCallRequestId": "6a48a6e0-04b8-4537-aa35-f71d2c5450dc", "timestamp": 1748272303743, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "7ecc2d62-d5a7-4c3f-8ae7-3b3a1c1be5e8", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "e6236f26-af10-4a2a-a312-08fcd893f888", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "845c4ee6-5a9f-4818-8043-a0aa9f18db65", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "e2faffd6-06cb-4d1f-91fe-f13f6147ec08", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "184544cb-d1b5-48e5-b389-e85cc8752fa4", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "26564b4d-da3e-4e38-92cc-ba7de404701e", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "330b6660-f3fb-4755-818c-381657b376d0", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "fc1bc359-bf0a-48e5-837c-3c6222a86845", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "bf4c84df-8ce6-4650-a0b5-663ed39fd7cc", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "1c91ee35-e3c7-4670-84c3-48db8e6377c2", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "70cfc994-6d65-419b-8062-1aa4af8b0836", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "b6f6af18-eedf-436e-858c-5aba7576c2bc", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "a1a30dc8-7cef-45d2-8d90-ec0e047e9bf9", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "69c6e264-5be5-4a33-8775-00c4755af<PERSON>da", "timestamp": 1748279132535, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "943a72c7-3718-40dd-8a25-5128f247f4f7", "timestamp": 1748279132776, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "e559f37d-60fe-4112-b521-1fdc22365f64", "timestamp": 1748279133020, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "2e7ecb59-88c5-4f0a-9754-0a22ca3b3a38", "timestamp": 1748279133506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "6dd3b784-a3eb-4919-a365-594c817a5b54", "timestamp": 1748279133732, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "cbbc62fe-a4df-4b74-aa9b-0efd3792e0d1", "timestamp": 1748279133970, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "cc2e7231-129d-4c38-b3fa-8bc5c8f18771", "timestamp": 1748279134233, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "847fbbac-d9ee-41ac-bc3e-b09e5198ab61", "timestamp": 1748279134716, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "958e2180-9bfb-42c0-ac3f-66d2327e8003", "timestamp": 1748279135107, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "3cdb1278-71b9-41b3-b8bc-e03b8ea78ea2", "timestamp": 1748279135391, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}, {"sourceToolCallRequestId": "5cd8e76b-5719-4b85-8e96-8546c9e39db5", "timestamp": 1748279135686, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md": [{"sourceToolCallRequestId": "d87dea1b-a7e5-4dff-b8cf-e94fe76ff3e2", "timestamp": 1748273253084, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "a91dcd18-b0f3-4584-81cb-04aa7deaf474", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "36b1e4ff-d0c8-4092-9744-dd29166351a1", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "4772f25e-4d5a-4384-96ee-eb3d4d915306", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "c33e4905-6a55-4504-bc60-bca9123fd308", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "ec09f04b-aac0-4dac-9c62-94d527404dce", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "266c4adb-5674-459a-9807-f3426a58e757", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "a4416cc4-cb26-41f4-a3f9-72bfa0593af1", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "0a60a116-3737-4d89-9ffb-96b2e22a8e55", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "410d5412-97ae-47de-8b95-a0e2ffc384b6", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "10d3035d-010f-4317-9775-8c70eb203d6a", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "0da55baa-b08b-40ad-a31d-b768fb5aa823", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "d8c4092d-630b-4b23-a561-46f82014137c", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "8f93531e-fe7b-4c52-b018-73a623638a7f", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "34fa87f3-90b9-4ad8-884a-ca4e7647516d", "timestamp": 1748279132535, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "32ad7eea-d263-4bf0-9c2a-75bcce7b0a9e", "timestamp": 1748279132776, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "aca3417c-8bde-4722-becd-416cfa31ba37", "timestamp": 1748279133020, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "a0289712-ad9a-418f-a0c8-a4f72cab6538", "timestamp": 1748279133506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "b5162a4a-e7d4-419e-98ea-637613aa3043", "timestamp": 1748279133732, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "77ae8a69-0b62-4ff3-b16b-421011dc9d3d", "timestamp": 1748279133970, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "5cf1e572-ab42-4e96-a21a-4c473db84c66", "timestamp": 1748279134233, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "58afc3f0-e7d2-4a8e-bbf4-3887b73eec3e", "timestamp": 1748279134716, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "3c5fae6d-b8f0-4fa4-85cf-fd004065e2ce", "timestamp": 1748279135107, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "e80e4b69-0740-4026-b210-b5ca963ddcd2", "timestamp": 1748279135391, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}, {"sourceToolCallRequestId": "88693aa9-611a-411b-84ec-582b6103be10", "timestamp": 1748279135686, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md": [{"sourceToolCallRequestId": "7a07bbb9-9f85-473f-a0e1-4f7ff4b1d57f", "timestamp": 1748275994809, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "95033afe-aecc-4828-b8cb-82111c97e74b", "timestamp": 1748278979097, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "f9550e16-a5a4-4810-868a-061dd543851e", "timestamp": 1748278989660, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "4edef5a7-c357-4a91-9a39-184e8236113d", "timestamp": 1748278992341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "a42bf736-a23b-46f4-9955-549b3f8728f4", "timestamp": 1748278992576, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "625472ef-03ef-4f19-9e94-b4c0e1e61864", "timestamp": 1748278993050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "17393b84-e3f9-4e05-bf54-5a1f06827dab", "timestamp": 1748278993412, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "f98a6af9-1404-446b-9e61-64e85d1b95a1", "timestamp": 1748278993665, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "f7558a6f-1831-452d-8570-810f8f3e983b", "timestamp": 1748278993917, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "b1384339-3fcb-4249-852b-359d430f5208", "timestamp": 1748279126993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "64d088bd-bb56-43a8-b7ce-7de3f6e731a3", "timestamp": 1748279127540, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "5e84c57c-3df6-454e-900b-bdb498fa0a0d", "timestamp": 1748279127930, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "a2917a3d-6f09-45dc-b067-80cd52e0ba99", "timestamp": 1748279128506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "7b815b1d-cb2f-48e6-a085-e22e10df78de", "timestamp": 1748279128819, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "dbc48585-3412-40d4-b00f-eeb6a01e82bb", "timestamp": 1748279132535, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "6edd93e5-f056-4670-9f17-4c74412b8ea9", "timestamp": 1748279132776, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "9507faa1-1ad6-4cf0-ab28-a8f4a2e5c10d", "timestamp": 1748279133020, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "416d795f-85dd-4452-8f2e-fc84dbc06cfd", "timestamp": 1748279133506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "8d6b3fd0-0dc9-4de8-a996-7e27b9e08c3e", "timestamp": 1748279133732, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "04ca9f12-5836-46a5-a2b9-459ddb4ebdef", "timestamp": 1748279133970, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "76e6bf7e-4f8c-41ff-83fb-4cf6fa6ce9a8", "timestamp": 1748279134233, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "7d869bf7-0fe1-4564-9d75-24b9ab5b25d2", "timestamp": 1748279134716, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "e3581e32-474f-4d0a-9cb2-7e4134330281", "timestamp": 1748279135107, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "7992a533-0de9-4bfb-8cc8-98676d02214c", "timestamp": 1748279135391, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "026c7307-7724-4277-aa1a-b53aa832437e", "timestamp": 1748279135686, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\CONTEXTUAL_LOSS_ANALYSIS.md": [{"sourceToolCallRequestId": "14810d96-ec15-4e4e-aa26-4f92d76634d1", "timestamp": 1748352607210, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/CONTEXTUAL_LOSS_ANALYSIS.md"}}}, {"sourceToolCallRequestId": "cb63f569-a99f-4802-a62b-a5b670b84373", "timestamp": 1748358537870, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\CONTEXTUAL_LOSS_ANALYSIS.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\PAPER_CONTEXTUAL_LOSS_SPECIFICATION.md": [{"sourceToolCallRequestId": "b8defa06-11fb-4b2f-9b03-0d535bb98dae", "timestamp": 1748353459429, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/PAPER_CONTEXTUAL_LOSS_SPECIFICATION.md"}}}, {"sourceToolCallRequestId": "c60cb16e-5375-4bfa-b362-a12cbc2e8008", "timestamp": 1748358587400, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/PAPER_CONTEXTUAL_LOSS_SPECIFICATION.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\NONLOCALNET_ANALYSIS_AND_SOLUTION.md": [{"sourceToolCallRequestId": "09bf400e-634f-44ff-b407-77bf417d578b", "timestamp": 1748356528483, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/NONLOCALNET_ANALYSIS_AND_SOLUTION.md"}}}, {"sourceToolCallRequestId": "c60cb16e-5375-4bfa-b362-a12cbc2e8008", "timestamp": 1748358587180, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/NONLOCALNET_ANALYSIS_AND_SOLUTION.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md": [{"sourceToolCallRequestId": "23ee6496-31c7-4f28-89ea-f45880cd2a31", "timestamp": 1748358838023, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}, {"sourceToolCallRequestId": "695edafb-5316-4eb2-b96e-d2c4db482712", "timestamp": 1748358960275, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}, {"sourceToolCallRequestId": "da268eab-10c2-41fe-957d-c1a0f025d9dd", "timestamp": 1748358984055, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}, {"sourceToolCallRequestId": "453d8960-fa77-44e9-9922-eb2fd1c89d5e", "timestamp": 1748359011052, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}, {"sourceToolCallRequestId": "b19b967e-c119-4f48-8f03-52b175d02cb2", "timestamp": 1748359041110, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}, {"sourceToolCallRequestId": "ed6a5b53-5a83-4a9e-8940-ecd5b70746b3", "timestamp": 1748359358924, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}, {"sourceToolCallRequestId": "5b04c8d7-4c7a-41f0-869c-c38da02d42a3", "timestamp": 1748359387341, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}, {"sourceToolCallRequestId": "0a1ae709-0688-4f1a-a04e-8f662b4cb862", "timestamp": 1748359410504, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}, {"sourceToolCallRequestId": "471bfc80-0d25-49d7-8fbc-b77c6382487e", "timestamp": 1748359428902, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}, {"sourceToolCallRequestId": "9ddc2200-19e9-44cc-9f5d-9140469abbfa", "timestamp": 1748359450079, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}, {"sourceToolCallRequestId": "1ff788bf-b0c5-4d74-83de-2291b102ae33", "timestamp": 1748359475249, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"}}}]}, "metadata": {"checkpointDocumentIds": ["************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ERRORS_AND_ISSUES.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\BUG_FIXES.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\modules\\attention.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\modules\\attention_dcn_control.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\modules\\encoders\\modules.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\utils_image.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ldm\\modules\\image_degradation\\bsrgan.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\test_environment.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\test_components.py", "************************************:.", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\comprehensive_test_suite.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\run_full_tests.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\quick_test.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\TESTING_GUIDE.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\debug_test.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\safe_test.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\simple_debug.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\test_safe.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\working_test.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\minimal_test.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\cpu_test.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\diagnose_segfault.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\ultra_minimal_test.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\bypass_test.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\docker_solution.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\universal_logger.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\logging_examples.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\integrate_logging.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\auto_logger.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\run_logged.sh", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\log_viewer.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\setup_logging.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\remove_icons.py", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\CREATED_FILES_SUMMARY.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\COMPLETE_PARAMETERS_GUIDE.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\DISABLE_SELF_ATTN_DEEP_EXPLANATION.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\PYTHON_TEST_EXECUTION_ANALYSIS.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\CONTEXTUAL_LOSS_ANALYSIS.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\PAPER_CONTEXTUAL_LOSS_SPECIFICATION.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\NONLOCALNET_ANALYSIS_AND_SOLUTION.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori\\THREAD_SUMMARY_CRITICAL_ISSUES.md"], "size": 18137743, "checkpointCount": 770, "lastModified": 1748359709762}}