{"id": "shard-************************************", "checkpoints": {"************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\summary.md": [{"sourceToolCallRequestId": "9e8f4fc8-7643-43a8-90ef-b5956bd0c80b", "timestamp": 1747517267515, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "summary.md"}}}, {"sourceToolCallRequestId": "a8972de8-ae41-400f-956d-231cec1d1105", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "summary.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\cldm\\note.md": [{"sourceToolCallRequestId": "61f280c9-2a21-47e9-a269-54aa93fbc144", "timestamp": 1747517622613, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "cldm/note.md"}}}, {"sourceToolCallRequestId": "a8f42c63-4460-4510-801d-e434ac2c880b", "timestamp": 1747548914955, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "cldm\\note.md"}}}, {"sourceToolCallRequestId": "512ceea6-cfb0-4f81-a4b0-abd20658d019", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "cldm\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\note.md": [{"sourceToolCallRequestId": "f6ec7356-ebdf-4a0e-afcb-41c09fe33e88", "timestamp": 1747517655757, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/note.md"}}}, {"sourceToolCallRequestId": "4aee148b-00ce-414b-8563-f8dbb2790784", "timestamp": 1747548964497, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\note.md"}}}, {"sourceToolCallRequestId": "cffaf910-72b4-4f9e-b8bc-e0e1f42b11cd", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\canny\\note.md": [{"sourceToolCallRequestId": "0c515845-df45-4c18-8ab4-79b95d9408b3", "timestamp": 1747517679728, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/canny/note.md"}}}, {"sourceToolCallRequestId": "9aae4170-a99e-4e8a-9954-1ce1505c32cd", "timestamp": 1747550137512, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\canny\\note.md"}}}, {"sourceToolCallRequestId": "56f77923-7c81-42d2-b835-1e14900edbe4", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\canny\\note.md"}}}, {"sourceToolCallRequestId": "6fa88e8e-0208-4554-97e0-32b1eb0a0430", "timestamp": 1747551358641, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\canny\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\midas\\note.md": [{"sourceToolCallRequestId": "4c63788b-3ae0-45fb-965e-37e4301d4cfd", "timestamp": 1747517707478, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/midas/note.md"}}}, {"sourceToolCallRequestId": "ca839030-2fbf-42dd-9e90-5abf10246c29", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/midas/note.md"}}}, {"sourceToolCallRequestId": "7586686a-82b3-47ff-bc94-2dcb3a61e53e", "timestamp": 1747551556436, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\midas\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\openpose\\note.md": [{"sourceToolCallRequestId": "9cb65966-8353-48ca-962c-655d7bd30369", "timestamp": 1747517740301, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/openpose/note.md"}}}, {"sourceToolCallRequestId": "69c78c44-126c-46aa-b728-1d1290e9da79", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/openpose/note.md"}}}, {"sourceToolCallRequestId": "49692995-848a-4fa1-b68a-dc27de64efa4", "timestamp": 1747551637797, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\openpose\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\note.md": [{"sourceToolCallRequestId": "72e11d3a-65e3-4f6e-ba8e-c6f9a3bcce6b", "timestamp": 1747517773059, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "note.md"}}}, {"sourceToolCallRequestId": "6274c1bc-3cf8-4139-b94a-78b4a5123db6", "timestamp": 1747548880867, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "note.md"}}}, {"sourceToolCallRequestId": "8c6b60c8-091f-4e3b-8c19-68b3f3d6457f", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "note.md"}}}, {"sourceToolCallRequestId": "43e8caee-7cd8-48f4-9943-83b3bdf6fe5b", "timestamp": 1747550301221, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\gradio_canny2image.py.note.md": [{"sourceToolCallRequestId": "23d80843-dadb-419f-a972-ce75e01bb2e1", "timestamp": 1747517806186, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "gradio_canny2image.py.note.md"}}}, {"sourceToolCallRequestId": "dc7c6e05-299f-4f2e-88e2-13587fe305b4", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "gradio_canny2image.py.note.md"}}}, {"sourceToolCallRequestId": "4c66ecc6-2c8d-499f-a885-a109d1edffc5", "timestamp": 1747550905784, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "gradio_canny2image.py.note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\docs\\train.note.md": [{"sourceToolCallRequestId": "4702020e-87a5-4f03-87a2-2f3024cc2ca3", "timestamp": 1747517838052, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "docs/train.note.md"}}}, {"sourceToolCallRequestId": "52e13492-054b-4f3c-a77f-ce1816cdf339", "timestamp": 1747549046958, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "docs\\train.note.md"}}}, {"sourceToolCallRequestId": "0f743979-9e5b-4812-9e62-ca06bdf348f4", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "docs\\train.note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\mlsd\\note.md": [{"sourceToolCallRequestId": "041fbbe9-7670-465f-902c-88b82f7a78bc", "timestamp": 1747520324867, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/mlsd/note.md"}}}, {"sourceToolCallRequestId": "f37c51ae-5fc3-4e2e-8d9f-9d5615a56ac1", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/mlsd/note.md"}}}, {"sourceToolCallRequestId": "c5c560d5-8d41-4799-9c8f-4bd2e052a730", "timestamp": 1747551597200, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\mlsd\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\hed\\note.md": [{"sourceToolCallRequestId": "f3a1ef02-0fb4-45d3-bd87-0d1b47aa6bd7", "timestamp": 1747520350241, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/hed/note.md"}}}, {"sourceToolCallRequestId": "633b934c-a5a3-447a-8027-5d4809f59b0e", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/hed/note.md"}}}, {"sourceToolCallRequestId": "18c0c320-0ea9-4345-a908-074551d0f220", "timestamp": 1747551518177, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\hed\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\gradio_normal2image.py.note.md": [{"sourceToolCallRequestId": "9e42888d-4709-441c-ac67-ad11d3da979a", "timestamp": 1747520385178, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "gradio_normal2image.py.note.md"}}}, {"sourceToolCallRequestId": "ca6a44d0-c41e-4d81-a485-79acaaf76c97", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "gradio_normal2image.py.note.md"}}}, {"sourceToolCallRequestId": "32831367-4ae5-46ed-a2ff-7528deed2e91", "timestamp": 1747550961532, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "gradio_normal2image.py.note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\cldm\\ddim_hacked.py.note.md": [{"sourceToolCallRequestId": "9d81e435-1fb7-4ea7-baa6-0bc00f0ad46a", "timestamp": 1747520416960, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "cldm/ddim_hacked.py.note.md"}}}, {"sourceToolCallRequestId": "41f4523b-2961-43f1-9e80-4507b903a23c", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "cldm/ddim_hacked.py.note.md"}}}, {"sourceToolCallRequestId": "4603be5b-ade0-4f5f-ac48-06554f16f3ea", "timestamp": 1747551010959, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "cldm\\ddim_hacked.py.note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\cldm\\logger.py.note.md": [{"sourceToolCallRequestId": "0eafe9e8-64f0-4c12-a54a-9eee2b5dbbaf", "timestamp": 1747520445307, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "cldm/logger.py.note.md"}}}, {"sourceToolCallRequestId": "18f8897a-3182-4114-830c-bd9e383ea318", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "cldm/logger.py.note.md"}}}, {"sourceToolCallRequestId": "c63cd8e5-2d8c-4281-a1c0-d9cb4d9443a2", "timestamp": 1747551046757, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "cldm\\logger.py.note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\docs\\low_vram.note.md": [{"sourceToolCallRequestId": "a1e9a0c2-6cfe-48f3-a056-f7dc46c707e0", "timestamp": 1747520477477, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "docs/low_vram.note.md"}}}, {"sourceToolCallRequestId": "8d7bc98c-8ead-459c-84dd-97ae37f92f90", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "docs/low_vram.note.md"}}}, {"sourceToolCallRequestId": "09b319eb-d49a-46eb-b8c1-9e52acad0522", "timestamp": 1747551097599, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "docs\\low_vram.note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\guess_mode.note.md": [{"sourceToolCallRequestId": "75ba0f21-1205-4c3c-b775-321fcd3bfd21", "timestamp": 1747520509877, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "guess_mode.note.md"}}}, {"sourceToolCallRequestId": "3179e67e-9d20-4362-8b06-336f8e4565db", "timestamp": 1747549080538, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "guess_mode.note.md"}}}, {"sourceToolCallRequestId": "c39ea40a-c199-40df-b20c-d512e81aa59d", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "guess_mode.note.md"}}}, {"sourceToolCallRequestId": "baadc3b2-4e59-4ae1-9ee5-6732dc0be698", "timestamp": 1748013564829, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\guess_mode.note.md"}}}, {"sourceToolCallRequestId": "5317a2e5-81ef-49f0-9433-68545d35d655", "timestamp": 1748013874362, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\guess_mode.note.md"}}}, {"sourceToolCallRequestId": "ee17e1ee-e8ca-41c2-8bcb-efa31d662d4e", "timestamp": 1748013897318, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\guess_mode.note.md"}}}, {"sourceToolCallRequestId": "379d4c50-935e-4ba3-b987-0f77fec8b498", "timestamp": 1748013923014, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\guess_mode.note.md"}}}, {"sourceToolCallRequestId": "ce386aa3-fc90-4589-9dde-fa5dd623f818", "timestamp": 1748013969754, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\guess_mode.note.md"}}}, {"sourceToolCallRequestId": "5304c7bf-8168-4ee9-b866-577d64d51f30", "timestamp": 1748014009797, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\guess_mode.note.md"}}}, {"sourceToolCallRequestId": "e426640b-6366-4025-acb0-1a83013bfb46", "timestamp": 1748015390570, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\guess_mode.note.md"}}}, {"sourceToolCallRequestId": "d74f8985-f7bd-49c2-8099-e7d64fc20485", "timestamp": 1748018219574, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\guess_mode.note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\note.md": [{"sourceToolCallRequestId": "a4f33b15-7784-4430-8040-2a64096c8ed1", "timestamp": 1747542534801, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/note.md"}}}, {"sourceToolCallRequestId": "513ca252-ce5f-437e-a17b-5e096bdd9961", "timestamp": 1747549007378, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\note.md"}}}, {"sourceToolCallRequestId": "2b1cda57-db25-447c-b9a2-243050178dd0", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\uniformer\\note.md": [{"sourceToolCallRequestId": "bd1f3248-cb30-4ea7-b6f5-b548feba1df2", "timestamp": 1747542566906, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/uniformer/note.md"}}}, {"sourceToolCallRequestId": "2c963ff8-4173-4004-aab1-f79a7b26d740", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/uniformer/note.md"}}}, {"sourceToolCallRequestId": "c1a4896c-1cda-439c-b0f8-d658d71a5bc1", "timestamp": 1747551682271, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\uniformer\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\models\\note.md": [{"sourceToolCallRequestId": "dda7a572-a084-4e45-9956-cb1aba625c8b", "timestamp": 1747542594458, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "models/note.md"}}}, {"sourceToolCallRequestId": "9cd025b5-bed5-4c5c-96c6-96df2ffd7b61", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "models/note.md"}}}, {"sourceToolCallRequestId": "1652bad7-1513-416b-90ad-57fd6b9721ac", "timestamp": 1747550330310, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "models\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\test_imgs\\note.md": [{"sourceToolCallRequestId": "60e336aa-2d04-46eb-9ea4-3a1bd82db26a", "timestamp": 1747542619907, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "test_imgs/note.md"}}}, {"sourceToolCallRequestId": "8744118c-343c-45b4-988f-623120e28f2c", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "test_imgs/note.md"}}}, {"sourceToolCallRequestId": "078be224-fa9e-4035-9892-dc4a14eba931", "timestamp": 1747550351391, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "test_imgs\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\models\\note.md": [{"sourceToolCallRequestId": "d7f9c949-6f90-49b0-be65-0609caa5fce8", "timestamp": 1747542652025, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/models/note.md"}}}, {"sourceToolCallRequestId": "a19327fe-d9e3-4f08-bc9b-ccd777a566f2", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/models/note.md"}}}, {"sourceToolCallRequestId": "b9a40832-1ea6-40b8-b4af-c01336656973", "timestamp": 1747551260111, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "7c03eb15-2803-4eb5-9263-37dd32bfea6a", "timestamp": 1748018952645, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "f0ebea15-c5c6-4b25-924e-57e6ed96dbd1", "timestamp": 1748019010334, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "4e26d380-d400-447c-a38f-ee6c2c6604a1", "timestamp": 1748019042970, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "21286057-c7e6-4d5b-9707-c91c3eec8dcc", "timestamp": 1748019080471, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "6c79e32f-92c0-421b-8130-1c03ad1f36c1", "timestamp": 1748020708906, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "462785fa-2a7f-4dfa-bdfb-5da5b2780f41", "timestamp": 1748022308380, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "a63f3c28-fa55-436b-94c0-073535608658", "timestamp": 1748022345194, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "c454fe04-20e4-429b-afee-bdce023bbc95", "timestamp": 1748022365977, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "cdebaad3-7158-4441-99a5-5476955fb5c1", "timestamp": 1748022713982, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "354e84e4-6bc1-4025-b00f-7a41bb7eb482", "timestamp": 1748022735445, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "d46d148b-1dc0-41ac-89af-146596272d5e", "timestamp": 1748022757402, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "e213928a-b55b-43ac-8533-206faaf100fe", "timestamp": 1748022775768, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "ece7b3fe-32bc-4db6-a57c-7fe9e29ff8af", "timestamp": 1748022787016, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "7999ef3b-6881-4556-8e6d-73527fe8a401", "timestamp": 1748022802039, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "29d4723e-866d-4398-978f-2667290a8f3e", "timestamp": 1748022821322, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "cf19f7b9-a2d4-4509-b536-905ea512232e", "timestamp": 1748022837857, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "8f60b916-8247-4d6e-bca3-f7125dac3b08", "timestamp": 1748023192703, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "3025e1fb-4954-4c09-a020-b767658ad698", "timestamp": 1748023561525, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "f7aa9cfa-dae3-4d26-ad72-c5d375f02871", "timestamp": 1748026837028, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "601f0d46-f953-4ca1-b243-2a5d46d261c4", "timestamp": 1748026864901, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "f318c8c6-2589-4fa2-995f-f371ed8cb88b", "timestamp": 1748026886305, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "01e046fd-ea46-481c-aa96-4e84643b4211", "timestamp": 1748026924305, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "bfa7e8c6-bcbe-4baf-a931-33151a85e3b3", "timestamp": 1748026970306, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "c99466b7-643c-45a8-af81-3b4f4ff2efe2", "timestamp": 1748026993468, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "d38e1de1-a28b-4679-8962-e8d6213d37c2", "timestamp": 1748027013417, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "912b24b0-72f1-4a8c-bb40-1fa186a8dc64", "timestamp": 1748027047464, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "6170b7d0-846b-4759-80c4-5524087e3f70", "timestamp": 1748027095594, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "6131d48d-8637-4e54-9d01-78f34c8614bc", "timestamp": 1748027119825, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "1aaeb935-01fa-48e2-95b3-f5248d78b428", "timestamp": 1748027621012, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "77c4609a-da0f-4e90-a90a-4eb6d3bd8fe2", "timestamp": 1748027639509, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "ddf81191-d5e4-4c42-9b0b-e2be1ffff4fa", "timestamp": 1748027660266, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "ba491f2a-0c13-4883-bb0f-a88f746d6e5b", "timestamp": 1748027677993, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "77039332-a241-4040-b2cf-8b9a83743447", "timestamp": 1748027694116, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "c03e2c9f-7f64-4089-b123-b2b31d0f5f53", "timestamp": 1748027707957, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "80203754-5306-4210-92aa-1354e0ee101a", "timestamp": 1748027722832, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}, {"sourceToolCallRequestId": "b54d9308-0376-4e14-88d4-79985148bd15", "timestamp": 1748027736829, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\note.md": [{"sourceToolCallRequestId": "a3489146-cc66-45ca-87fe-e753d9272efe", "timestamp": 1747542684502, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/note.md"}}}, {"sourceToolCallRequestId": "e267d208-8853-4244-b139-4831c169aec6", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/note.md"}}}, {"sourceToolCallRequestId": "d148c4ef-265e-43ac-b985-ce6ec9c5540c", "timestamp": 1747551300796, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\note.md"}}}, {"sourceToolCallRequestId": "9c73da0a-8f99-4b59-bf54-82dfcde75901", "timestamp": 1748075889903, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\note.md"}}}, {"sourceToolCallRequestId": "13aadcba-4894-4396-8f1f-f7181dad93ae", "timestamp": 1748075911506, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\note.md"}}}, {"sourceToolCallRequestId": "76d080e1-2bb0-4afd-966f-90a533de1754", "timestamp": 1748075939333, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\note.md"}}}, {"sourceToolCallRequestId": "44b2ed9f-ca22-4dbb-9eec-2a9dc1b94938", "timestamp": 1748075967568, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\note.md"}}}, {"sourceToolCallRequestId": "7ec719a2-b84f-440b-9f06-7ebc5dbc2cd4", "timestamp": 1748075991574, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\note.md"}}}, {"sourceToolCallRequestId": "14edc283-d570-4ff2-bf90-4e7b13ae3df2", "timestamp": 1748076020715, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\note.md"}}}, {"sourceToolCallRequestId": "53a1b755-abd5-4e2e-b0c5-821d12acdfd5", "timestamp": 1748076060089, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\note.md"}}}, {"sourceToolCallRequestId": "c490696e-6c57-4df9-b9f8-4f6a31d43eea", "timestamp": 1748076078003, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\data\\note.md": [{"sourceToolCallRequestId": "4d4fee9e-bdfe-46b4-87ae-bfb209d3447f", "timestamp": 1747542797380, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/data/note.md"}}}, {"sourceToolCallRequestId": "d1627f32-481f-4336-8c78-fcff177511cc", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/data/note.md"}}}, {"sourceToolCallRequestId": "b498b3cb-d36f-450b-ac64-d60d69cb87ec", "timestamp": 1747551207592, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\data\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\models\\diffusion\\note.md": [{"sourceToolCallRequestId": "8b499e15-9fbb-40c5-98e3-4ecc561a80cd", "timestamp": 1747542837879, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/models/diffusion/note.md"}}}, {"sourceToolCallRequestId": "9352d61c-df02-454a-b869-1587f7dae487", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/models/diffusion/note.md"}}}, {"sourceToolCallRequestId": "f4e6a11f-4fa0-418f-8575-cce6c5dae062", "timestamp": 1747551907898, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\models\\diffusion\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\diffusionmodules\\note.md": [{"sourceToolCallRequestId": "c16bf819-8426-426b-b78f-e7b9afab031e", "timestamp": 1747542868973, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/diffusionmodules/note.md"}}}, {"sourceToolCallRequestId": "02f7f3fb-60eb-40a2-b4dd-67e81f452403", "timestamp": 1747550003207, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\diffusionmodules\\note.md"}}}, {"sourceToolCallRequestId": "1d289c67-d5bb-418a-9ff0-08796b9b09b0", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\diffusionmodules\\note.md"}}}, {"sourceToolCallRequestId": "dc3b7c3f-bcdd-4d60-8d10-26160f408ac5", "timestamp": 1747552136893, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\diffusionmodules\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\encoders\\note.md": [{"sourceToolCallRequestId": "08be10bc-1303-4d8c-bf34-e95d63545e62", "timestamp": 1747542900727, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/encoders/note.md"}}}, {"sourceToolCallRequestId": "082af5de-6f5d-4988-9326-8740512ad8e3", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/encoders/note.md"}}}, {"sourceToolCallRequestId": "8bf8692b-c5f4-478c-9e0b-7f459939c031", "timestamp": 1747552221703, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\encoders\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\docs\\note.md": [{"sourceToolCallRequestId": "f70d0a8c-f2ad-43b2-8475-878b3a4e5c78", "timestamp": 1747542929324, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "docs/note.md"}}}, {"sourceToolCallRequestId": "e06f0310-3932-48ef-b33b-9113ad9e0646", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "docs/note.md"}}}, {"sourceToolCallRequestId": "54910859-012d-4939-b1ac-0b5cd43389dc", "timestamp": 1747550371515, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "docs\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\font\\note.md": [{"sourceToolCallRequestId": "04a738e3-02b1-4fb0-ab85-86c07915bee9", "timestamp": 1747542953638, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "font/note.md"}}}, {"sourceToolCallRequestId": "dccb31c5-e2a7-4fa6-b7c5-6e2d66ccab69", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "font/note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\github_page\\note.md": [{"sourceToolCallRequestId": "5108e071-da1a-41cf-bee6-781c7e436956", "timestamp": 1747542978723, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "github_page/note.md"}}}, {"sourceToolCallRequestId": "c28f653e-a15d-47f6-b139-8fd42d86fbb1", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "github_page/note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\ckpts\\note.md": [{"sourceToolCallRequestId": "6b25caac-0dae-486d-9b67-ae557c3df1aa", "timestamp": 1747543006420, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/ckpts/note.md"}}}, {"sourceToolCallRequestId": "c2d75dc4-93c4-4d07-8c3c-b22a1b938031", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/ckpts/note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\distributions\\note.md": [{"sourceToolCallRequestId": "f38af149-8daa-4812-93c8-a3f7535e6acf", "timestamp": 1747543096987, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/distributions/note.md"}}}, {"sourceToolCallRequestId": "37af1beb-4850-4f1f-ab3f-c96054a253f3", "timestamp": 1747550051094, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\distributions\\note.md"}}}, {"sourceToolCallRequestId": "cdbec2db-9484-4e98-b4a7-6bcbf102872c", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\distributions\\note.md"}}}, {"sourceToolCallRequestId": "5f6f00a8-956a-4ef3-ac62-833f59b1455e", "timestamp": 1747552178479, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\distributions\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\image_degradation\\note.md": [{"sourceToolCallRequestId": "ed3f093b-c579-4120-9d3b-8fdcfa078cb4", "timestamp": 1747543126872, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/image_degradation/note.md"}}}, {"sourceToolCallRequestId": "da0b3873-6cb6-4c1e-8c9a-0f2d7c73938e", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/image_degradation/note.md"}}}, {"sourceToolCallRequestId": "9e7485e3-0a96-4f03-9bd5-dc3a41506b0f", "timestamp": 1747552261837, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\image_degradation\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\midas\\note.md": [{"sourceToolCallRequestId": "457b9765-7ba2-4846-9aed-48d2b51c4b7a", "timestamp": 1747543159834, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/midas/note.md"}}}, {"sourceToolCallRequestId": "254f765f-970c-428d-ba2d-7f8f8c87fe1a", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/midas/note.md"}}}, {"sourceToolCallRequestId": "3078c4d7-becf-45e5-8dbd-98f9736e09e0", "timestamp": 1747552307477, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\midas\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\models\\diffusion\\dpm_solver\\note.md": [{"sourceToolCallRequestId": "a86fd897-7856-4eab-817f-02fde432b461", "timestamp": 1747543194187, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/models/diffusion/dpm_solver/note.md"}}}, {"sourceToolCallRequestId": "fd662841-086b-44a6-b974-8af3483eaec9", "timestamp": 1747550098337, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\models\\diffusion\\dpm_solver\\note.md"}}}, {"sourceToolCallRequestId": "52ecafad-4322-4e74-87f3-1ce5ec4a1b28", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\models\\diffusion\\dpm_solver\\note.md"}}}, {"sourceToolCallRequestId": "b3b5cd1f-dffa-4ab9-b04c-687e493c4edc", "timestamp": 1747552511976, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\models\\diffusion\\dpm_solver\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\midas\\midas\\note.md": [{"sourceToolCallRequestId": "8cccb724-306c-432c-a3df-02410bfb15a5", "timestamp": 1747543233296, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/midas/midas/note.md"}}}, {"sourceToolCallRequestId": "ead1c769-eabd-4886-9b84-73a6b7a85b0f", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/midas/midas/note.md"}}}, {"sourceToolCallRequestId": "5627e804-1125-4a38-81b6-ba8999008827", "timestamp": 1747551950511, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\midas\\midas\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\mlsd\\models\\note.md": [{"sourceToolCallRequestId": "b3912eb6-893b-43b6-9153-9267ba3648a3", "timestamp": 1747543272011, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/mlsd/models/note.md"}}}, {"sourceToolCallRequestId": "3cd1fac4-8a1e-4e60-ae5a-50c5e1bd149c", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator/mlsd/models/note.md"}}}, {"sourceToolCallRequestId": "d2a1c61e-3be5-4bda-b701-d585876f8703", "timestamp": 1747551996201, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "annotator\\mlsd\\models\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\image_degradation\\utils\\note.md": [{"sourceToolCallRequestId": "f10de2a1-cfdf-491d-93d5-4e751d38d1a0", "timestamp": 1747543312768, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/image_degradation/utils/note.md"}}}, {"sourceToolCallRequestId": "fc34731d-bc39-4166-b4c0-3003c8f5bc41", "timestamp": 1747550156267, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm/modules/image_degradation/utils/note.md"}}}, {"sourceToolCallRequestId": "83996d68-2439-48c0-829d-31ee15ad1fe0", "timestamp": 1747552571523, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet", "relPath": "ldm\\modules\\image_degradation\\utils\\note.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\note_vi.md": [{"sourceToolCallRequestId": "eaa50870-689d-430f-9862-a85a99bf7fe9", "timestamp": 1747681860889, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\models\\note_vi.md": [{"sourceToolCallRequestId": "b33f1a0a-5131-43a4-a110-f93e17a855fe", "timestamp": 1747681932480, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/models/note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\test_imgs\\note_vi.md": [{"sourceToolCallRequestId": "6a76a1d8-743f-4609-91be-d455b27568ac", "timestamp": 1747682008975, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/test_imgs/note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\docs\\note_vi.md": [{"sourceToolCallRequestId": "5fc40850-fdb9-4f0a-b41e-c506f5520947", "timestamp": 1747682079618, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/docs/note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\diffusionmodules\\note_vi.md": [{"sourceToolCallRequestId": "7b1775d1-d5a8-4684-9ab1-4e31387d1ce5", "timestamp": 1747682176658, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/ldm/modules/diffusionmodules/note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\distributions\\note_vi.md": [{"sourceToolCallRequestId": "2c90430b-835a-496f-8fb8-d0b437221981", "timestamp": 1747682259381, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/ldm/modules/distributions/note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\encoders\\note_vi.md": [{"sourceToolCallRequestId": "67ed13a7-7851-4047-8b53-7988076485a2", "timestamp": 1747682349479, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/ldm/modules/encoders/note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\image_degradation\\note_vi.md": [{"sourceToolCallRequestId": "73b79807-f64b-4ea4-a649-9a0b0c5912c2", "timestamp": 1747682438053, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/ldm/modules/image_degradation/note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\midas\\note_vi.md": [{"sourceToolCallRequestId": "bd9f542e-44f2-49a7-ab6b-e2440a72b748", "timestamp": 1747682539048, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/ldm/modules/midas/note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\models\\diffusion\\dpm_solver\\note_vi.md": [{"sourceToolCallRequestId": "a3401817-951e-4990-adf8-2b46f500eeeb", "timestamp": 1747682635050, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/ldm/models/diffusion/dpm_solver/note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\image_degradation\\utils\\note_vi.md": [{"sourceToolCallRequestId": "1ca92efa-40f7-42f7-89ef-6871951a98bc", "timestamp": 1747682716534, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/ldm/modules/image_degradation/utils/note_vi.md"}}}, {"sourceToolCallRequestId": "a91aee44-cd3c-4585-a65a-3930d5f919bc", "timestamp": 1747682771933, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\image_degradation\\utils\\note_vi.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\setup_controlnet.sh": [{"sourceToolCallRequestId": "03088f1d-6b66-4031-ac77-711fbdf76573", "timestamp": 1747691204678, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "setup_controlnet.sh"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\run_controlnet.sh": [{"sourceToolCallRequestId": "dc972f93-03e7-481a-8768-7fa3d5fd813b", "timestamp": 1747691232456, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "run_controlnet.sh"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\train_controlnet.sh": [{"sourceToolCallRequestId": "db811353-d247-4f0d-ba3c-9407b2c8bf1c", "timestamp": 1747691272624, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "train_controlnet.sh"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\controlnet_setup_readme.md": [{"sourceToolCallRequestId": "0fd3790e-3f9c-4812-81f8-3142a50b38bb", "timestamp": 1747691314831, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "controlnet_setup_readme.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\setup_controlnet_corrected.sh": [{"sourceToolCallRequestId": "5e219ed8-84a2-409e-b3cf-ae04d6d9e5e3", "timestamp": 1747693827670, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "setup_controlnet_corrected.sh"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\run_controlnet_corrected.sh": [{"sourceToolCallRequestId": "7a9701d9-4e03-48c7-a3da-e8464de239f5", "timestamp": 1747693864453, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "run_controlnet_corrected.sh"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\train_controlnet_corrected.sh": [{"sourceToolCallRequestId": "0362460f-c9e2-4ef1-886e-5cfa3f5a8335", "timestamp": 1747693904918, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "train_controlnet_corrected.sh"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\controlnet_setup_readme_corrected.md": [{"sourceToolCallRequestId": "27663262-5476-4786-a94b-d62a3f8eecbf", "timestamp": 1747693930552, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "controlnet_setup_readme_corrected.md"}}}], "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\components_reference.md": [{"sourceToolCallRequestId": "27f347a8-3ee3-4e20-8b90-646f9f1a18c4", "timestamp": 1748165118245, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ControlNet/ldm/modules/components_reference.md"}}}, {"sourceToolCallRequestId": "1e3f5ba6-0a8a-49fe-9d1f-aa8779a8ea1c", "timestamp": 1748165150991, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}, {"sourceToolCallRequestId": "e5e2eaa5-1d3a-4b5a-b7af-8f69f71c9cc1", "timestamp": 1748165670513, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}, {"sourceToolCallRequestId": "9581d13e-36e4-45cf-b3ef-65d588b08cd6", "timestamp": 1748165690049, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}, {"sourceToolCallRequestId": "44768501-0e90-4ae3-88e7-8b39e7ae373a", "timestamp": 1748165707668, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}, {"sourceToolCallRequestId": "25e1343f-31a1-4b21-b201-6603afefd13b", "timestamp": 1748165738236, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}, {"sourceToolCallRequestId": "2b78fcb1-da31-487f-ba23-1618438d7754", "timestamp": 1748165764084, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}, {"sourceToolCallRequestId": "6125994f-4ed2-4efa-a9f4-45efb711799d", "timestamp": 1748165784510, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}, {"sourceToolCallRequestId": "9d56b949-98cf-44ee-81e5-c314e1b74245", "timestamp": 1748165802860, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}, {"sourceToolCallRequestId": "b0103b6b-ec0d-4cc5-aa37-3e2044a1923c", "timestamp": 1748165819604, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}, {"sourceToolCallRequestId": "7c064f6b-b3da-4760-be0c-774d2d894010", "timestamp": 1748165838892, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}, {"sourceToolCallRequestId": "a9587584-00d9-460e-bb92-4856a4a5efc2", "timestamp": 1748165862261, "conversationId": "************************************", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ControlNet\\ldm\\modules\\components_reference.md"}}}]}, "metadata": {"checkpointDocumentIds": ["************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\summary.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\cldm\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\canny\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\midas\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\openpose\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\gradio_canny2image.py.note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\docs\\train.note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\mlsd\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\hed\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\gradio_normal2image.py.note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\cldm\\ddim_hacked.py.note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\cldm\\logger.py.note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\docs\\low_vram.note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\guess_mode.note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\uniformer\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\models\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\test_imgs\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\models\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\data\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\models\\diffusion\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\diffusionmodules\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\encoders\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\docs\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\font\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\github_page\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\ckpts\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\distributions\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\image_degradation\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\midas\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\models\\diffusion\\dpm_solver\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\midas\\midas\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\annotator\\mlsd\\models\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\image_degradation\\utils\\note.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\models\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\test_imgs\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\docs\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\diffusionmodules\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\distributions\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\encoders\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\image_degradation\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\midas\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\models\\diffusion\\dpm_solver\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\image_degradation\\utils\\note_vi.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\setup_controlnet.sh", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\run_controlnet.sh", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\train_controlnet.sh", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\controlnet_setup_readme.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\setup_controlnet_corrected.sh", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\run_controlnet_corrected.sh", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\train_controlnet_corrected.sh", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\controlnet_setup_readme_corrected.md", "************************************:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ControlNet\\ldm\\modules\\components_reference.md"], "size": 4112824, "checkpointCount": 196, "lastModified": 1748165862338}}