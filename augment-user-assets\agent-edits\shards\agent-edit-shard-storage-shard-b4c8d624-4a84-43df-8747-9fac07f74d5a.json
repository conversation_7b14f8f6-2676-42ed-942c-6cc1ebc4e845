{"id": "shard-b4c8d624-4a84-43df-8747-9fac07f74d5a", "checkpoints": {"b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\README.md": [{"sourceToolCallRequestId": "cf6ec7ed-cd9d-469f-9ed5-8512901b7dbf", "timestamp": 1748175202309, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "README.md"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\requirements.txt": [{"sourceToolCallRequestId": "e2427567-585e-4eea-82e1-7f96493ec188", "timestamp": 1748175208019, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "requirements.txt"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\01_theory\\fine_tuning_explained.md": [{"sourceToolCallRequestId": "3e8d0eab-22fb-4570-a912-0b12bfce97e0", "timestamp": 1748175228480, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "01_theory/fine_tuning_explained.md"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\01_theory\\theory_demo.py": [{"sourceToolCallRequestId": "2f011976-7c47-42b1-b729-803d79f97ac0", "timestamp": 1748175250159, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "01_theory/theory_demo.py"}}}, {"sourceToolCallRequestId": "c58ff432-de86-4608-ac1d-afd8039e10f3", "timestamp": 1748194383563, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "01_theory\\theory_demo.py"}}}, {"sourceToolCallRequestId": "2d8e7a08-7aed-43ba-b121-85f119332ee2", "timestamp": 1748194424234, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "01_theory\\theory_demo.py"}}}, {"sourceToolCallRequestId": "1bf50be5-1618-4347-a385-b714f155f547", "timestamp": 1748453203050, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "01_theory\\theory_demo.py"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\02_basic_image\\basic_finetuning.py": [{"sourceToolCallRequestId": "663f1dc7-db23-49d8-a0a7-82fa7ffdfbda", "timestamp": 1748175275793, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "02_basic_image/basic_finetuning.py"}}}, {"sourceToolCallRequestId": "5f80fc3f-3527-455a-9cfd-efe0e6f2d8ac", "timestamp": 1748194359470, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "02_basic_image\\basic_finetuning.py"}}}, {"sourceToolCallRequestId": "057effde-c39b-40b8-a4a0-0c06e86fdefc", "timestamp": 1748453203050, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "02_basic_image\\basic_finetuning.py"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\03_text_classification\\bert_finetuning.py": [{"sourceToolCallRequestId": "a57e1d6a-37f7-4bef-ba1c-6e903f425de1", "timestamp": 1748175309052, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "03_text_classification/bert_finetuning.py"}}}, {"sourceToolCallRequestId": "03a20544-a80f-447e-a0e0-cb775773b8e5", "timestamp": 1748194454589, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "03_text_classification\\bert_finetuning.py"}}}, {"sourceToolCallRequestId": "52ca0d39-83b8-411d-9858-b0646fede5db", "timestamp": 1748198087809, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "03_text_classification\\bert_finetuning.py"}}}, {"sourceToolCallRequestId": "cf6bd56d-17eb-4536-99be-d3d08aba537a", "timestamp": 1748198424883, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "03_text_classification\\bert_finetuning.py"}}}, {"sourceToolCallRequestId": "8a2fd527-3ebe-41cc-bcfd-082e1ae0e341", "timestamp": 1748453203050, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "03_text_classification\\bert_finetuning.py"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\04_advanced\\lora_finetuning.py": [{"sourceToolCallRequestId": "fc569734-8944-4925-868c-33e8d6bb9378", "timestamp": 1748175352226, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "04_advanced/lora_finetuning.py"}}}, {"sourceToolCallRequestId": "10edaffd-2e14-4e95-8414-b5aabb3b2d5f", "timestamp": 1748194476219, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "04_advanced\\lora_finetuning.py"}}}, {"sourceToolCallRequestId": "7ccfd33c-6e2b-4ed3-a400-287847af29be", "timestamp": 1748194533422, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "04_advanced\\lora_finetuning.py"}}}, {"sourceToolCallRequestId": "8997aa61-2339-4b64-b593-1f2b9696c620", "timestamp": 1748453203050, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "04_advanced\\lora_finetuning.py"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\05_llm_finetuning\\llm_finetuning.py": [{"sourceToolCallRequestId": "84152b64-8a43-40ca-be7d-a5b1eb998b71", "timestamp": 1748175392973, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "05_llm_finetuning/llm_finetuning.py"}}}, {"sourceToolCallRequestId": "3f18e727-d2ab-4ee6-b56f-b952a4571b22", "timestamp": 1748194562320, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "05_llm_finetuning\\llm_finetuning.py"}}}, {"sourceToolCallRequestId": "cb4f5cfa-3232-4bf3-bb2b-11c60be27fb0", "timestamp": 1748453203050, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "05_llm_finetuning\\llm_finetuning.py"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\06_projects\\project_ideas.md": [{"sourceToolCallRequestId": "f69c6a96-001e-4a38-b164-6f01bbdc7cb5", "timestamp": 1748175411964, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "06_projects/project_ideas.md"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\QUICK_START_GUIDE.md": [{"sourceToolCallRequestId": "602b37c1-0db5-4f87-a65e-bbaae096de1e", "timestamp": 1748175435289, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "QUICK_START_GUIDE.md"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\06_projects\\custom_image_classifier.py": [{"sourceToolCallRequestId": "e91ea4a7-5c75-4188-baa6-b4de22d0620e", "timestamp": 1748175476349, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "06_projects/custom_image_classifier.py"}}}, {"sourceToolCallRequestId": "9793f981-2641-4821-804f-ff2ec462842a", "timestamp": 1748194587462, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "06_projects\\custom_image_classifier.py"}}}, {"sourceToolCallRequestId": "8562d6ce-d457-4730-9103-b71c4441dedb", "timestamp": 1748194636212, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "06_projects\\custom_image_classifier.py"}}}, {"sourceToolCallRequestId": "4cf17686-de72-4837-a314-544edc52a218", "timestamp": 1748453203050, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "06_projects\\custom_image_classifier.py"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\finetune_cloned_project.py": [{"sourceToolCallRequestId": "0906d28e-1e70-4bc2-a840-80c4408b0e6f", "timestamp": 1748207030855, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "finetune_cloned_project.py"}}}, {"sourceToolCallRequestId": "52d8b014-03e3-4498-a0ae-4e1f5c23204a", "timestamp": 1748453203050, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "finetune_cloned_project.py"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\inspect_cloned_project.py": [{"sourceToolCallRequestId": "27aaef00-0102-4239-b520-ca7c4270d176", "timestamp": 1748207137983, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "inspect_cloned_project.py"}}}, {"sourceToolCallRequestId": "0acd61ce-eb8d-40f5-bc92-ba4fcaffae36", "timestamp": 1748453203050, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "inspect_cloned_project.py"}}}], "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\real_world_examples.md": [{"sourceToolCallRequestId": "c429c081-d57a-43c3-9ee4-0854e479a3c9", "timestamp": 1748207170824, "conversationId": "b4c8d624-4a84-43df-8747-9fac07f74d5a", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\trial\\python\\dummy", "relPath": "real_world_examples.md"}}}]}, "metadata": {"checkpointDocumentIds": ["b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\README.md", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\requirements.txt", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\01_theory\\fine_tuning_explained.md", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\01_theory\\theory_demo.py", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\02_basic_image\\basic_finetuning.py", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\03_text_classification\\bert_finetuning.py", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\04_advanced\\lora_finetuning.py", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\05_llm_finetuning\\llm_finetuning.py", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\06_projects\\project_ideas.md", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\QUICK_START_GUIDE.md", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\06_projects\\custom_image_classifier.py", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\finetune_cloned_project.py", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\inspect_cloned_project.py", "b4c8d624-4a84-43df-8747-9fac07f74d5a:c:\\Users\\<USER>\\Documents\\trial\\python\\dummy\\real_world_examples.md"], "size": 532516, "checkpointCount": 33, "lastModified": 1748453203050}}