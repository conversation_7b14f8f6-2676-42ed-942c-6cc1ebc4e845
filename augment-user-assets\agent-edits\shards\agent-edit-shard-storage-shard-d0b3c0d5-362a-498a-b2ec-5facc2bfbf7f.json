{"id": "shard-d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "checkpoints": {"d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\summary.md": [{"sourceToolCallRequestId": "6b4c15a1-924d-40a8-95a0-52e5cfcf50d3", "timestamp": 1747752818587, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "summary.md"}}}, {"sourceToolCallRequestId": "b6b74aff-b7cc-48dd-b195-56e208cf6ab7", "timestamp": 1747752844590, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "f7e10de6-26e2-4d2a-b780-fdfd849a57f9", "timestamp": 1747752869494, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "28809e19-b918-428e-91ed-e6b0d79f31f5", "timestamp": 1747753063413, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "debaa4f0-8c0d-4e42-8d2e-b044f866ffc3", "timestamp": 1747753082114, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "84b24344-566d-478d-afb0-be642210e0cb", "timestamp": 1747753102824, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "d5c11c40-7b1f-4dc3-b38b-7a54cc5832a0", "timestamp": 1747753123420, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "d15b5090-e866-42fd-a19d-e722d8f21b14", "timestamp": 1747753138771, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "9b964441-33e8-4d65-98b3-ee7c24595fc2", "timestamp": 1747753155045, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "0b462053-8c14-47ed-a029-9d9426499825", "timestamp": 1747753192767, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "8a1c67de-39e4-4746-a8f3-7e54f2e49eb0", "timestamp": 1747753358030, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "35f995a8-bb43-4d4e-8aa1-3937594c03f8", "timestamp": 1747753377300, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "a8e9b726-6674-410f-8544-67a38f43acce", "timestamp": 1747753388292, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "045d3fdb-f71f-4fc4-875f-eb0115ca7ebb", "timestamp": 1747753401079, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "647c5df0-152a-45e8-980b-d59ff29d42da", "timestamp": 1747753412338, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}, {"sourceToolCallRequestId": "ddc0bd05-a150-4675-87a2-669f625bf7ec", "timestamp": 1747753431252, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\summary.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\main.py": [{"sourceToolCallRequestId": "30ecba53-6af2-4ebe-a43d-6abe68b35279", "timestamp": 0, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "main.py"}}}, {"sourceToolCallRequestId": "a8e1dbd1-a502-49ee-baa9-3b14de398808", "timestamp": 1747755986327, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\main.py"}}}, {"sourceToolCallRequestId": "2ac91a28-979d-4ef7-927e-ce7ed9e05934", "timestamp": 1747755997231, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\main.py"}}}, {"sourceToolCallRequestId": "84c78cc8-7971-4ef6-8a0d-182f7d4eeb9f", "timestamp": 1747756011736, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\main.py"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\wandb_integration.md": [{"sourceToolCallRequestId": "05356373-7a0e-4f32-8c6a-969b891c7d66", "timestamp": 1747756041699, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "wandb_integration.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\vqgan_explanation.md": [{"sourceToolCallRequestId": "03d0fcb7-17e8-4025-89a7-9c61abb9e7aa", "timestamp": 1747760004245, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "vqgan_explanation.md"}}}, {"sourceToolCallRequestId": "112ca281-7ce6-4af5-b1a8-faae4ef8a76d", "timestamp": 1747777080414, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\vqgan_explanation.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\config_summary.md": [{"sourceToolCallRequestId": "82aabca8-25dc-4fa9-84cc-9ad8fab43465", "timestamp": 1747761575731, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "config_summary.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\configs\\config_summary.md": [{"sourceToolCallRequestId": "e73faafb-9f92-4348-9a56-1c5a17614048", "timestamp": 0, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "configs\\config_summary.md"}}}, {"sourceToolCallRequestId": "fe538ace-e504-4d04-aed8-6cbefe04abd3", "timestamp": 1747772349129, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\configs\\config_summary.md"}}}, {"sourceToolCallRequestId": "29e5a41c-68d0-4d66-bafc-b35bc63c047a", "timestamp": 1747772378799, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\configs\\config_summary.md"}}}, {"sourceToolCallRequestId": "3c0a96dd-d5e5-40c4-8077-dcbbe843bb09", "timestamp": 1747777141386, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\configs\\config_summary.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\cond_transformer_explanation.md": [{"sourceToolCallRequestId": "24902fa5-02a6-4eb7-b5e4-281c62b760a3", "timestamp": 1747772794100, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "cond_transformer_explanation.md"}}}, {"sourceToolCallRequestId": "c4db2e7b-26fe-4aad-a46a-4a0be542f2bc", "timestamp": 1747777105670, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\taming-transformers\\cond_transformer_explanation.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\diffusionmodules_model_explanation.md": [{"sourceToolCallRequestId": "c21d5085-ee10-4259-815d-289aadeb8fcc", "timestamp": 1747778162752, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "diffusionmodules_model_explanation.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\quantize_explanation.md": [{"sourceToolCallRequestId": "032c1d0a-f576-40b0-a142-c8e725eb2d30", "timestamp": 1747778701702, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "quantize_explanation.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\mingpt_explanation.md": [{"sourceToolCallRequestId": "96f5f854-1b1f-46d5-9b44-14edaa466382", "timestamp": 1747779330672, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "mingpt_explanation.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\permuter_explanation.md": [{"sourceToolCallRequestId": "466dbdfa-612d-4e68-a23f-ad27e897b83f", "timestamp": 1747779597812, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "permuter_explanation.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\losses_explanation.md": [{"sourceToolCallRequestId": "363cc1b1-b7d4-441e-9c8d-2ff228c74153", "timestamp": 1747780346038, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "losses_explanation.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\discriminator_explanation.md": [{"sourceToolCallRequestId": "cfa0965f-1c0a-41be-b70c-34aeb40b8552", "timestamp": 1747780551449, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "discriminator_explanation.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\lr_scheduler_explanation.md": [{"sourceToolCallRequestId": "b8289a72-d8bc-490b-a2d3-bac6fbc4ed1c", "timestamp": 1747780700118, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "lr_scheduler_explanation.md"}}}], "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\main_explanation.md": [{"sourceToolCallRequestId": "f3b0cce2-eae5-47c9-a427-181a71fa99c2", "timestamp": 1747780958817, "conversationId": "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f", "documentMetadata": {"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers", "relPath": "main_explanation.md"}}}]}, "metadata": {"checkpointDocumentIds": ["d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\summary.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\main.py", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\wandb_integration.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\vqgan_explanation.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\config_summary.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\configs\\config_summary.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\cond_transformer_explanation.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\diffusionmodules_model_explanation.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\quantize_explanation.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\mingpt_explanation.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\permuter_explanation.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\losses_explanation.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\discriminator_explanation.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\lr_scheduler_explanation.md", "d0b3c0d5-362a-498a-b2ec-5facc2bfbf7f:c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\taming-transformers\\main_explanation.md"], "size": 1057066, "checkpointCount": 38, "lastModified": 1747780958827}}