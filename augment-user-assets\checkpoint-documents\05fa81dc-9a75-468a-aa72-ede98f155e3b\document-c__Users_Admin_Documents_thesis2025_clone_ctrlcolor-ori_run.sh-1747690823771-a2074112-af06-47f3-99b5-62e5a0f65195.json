{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori", "relPath": "run.sh"}, "modifiedCode": "#!/bin/bash\n\n# CtrlColor Run Script\n# This script activates the environment and runs the CtrlColor demo\n\nset -e  # Exit on error\n\necho \"===== Running CtrlColor Demo =====\"\n\n# Check if conda is installed\nif ! command -v conda &> /dev/null; then\n    echo \"Conda is not installed. Please install Miniconda or Anaconda first.\"\n    echo \"Visit: https://docs.conda.io/en/latest/miniconda.html\"\n    exit 1\nfi\n\n# Check if the environment exists\nif ! conda env list | grep -q \"CtrlColor\"; then\n    echo \"CtrlColor environment not found. Please run setup.sh first.\"\n    exit 1\nfi\n\n# Check if model files exist\nif [ ! -d \"./pretrained_models\" ] || [ -z \"$(ls -A ./pretrained_models)\" ]; then\n    echo \"Model files not found. Please run setup.sh first to download the required models.\"\n    exit 1\nfi\n\n# Activate the environment\necho \"Activating conda environment...\"\neval \"$(conda shell.bash hook)\"\nconda activate CtrlColor\n\n# Run the demo\necho \"Starting CtrlColor demo...\"\npython test.py\n\necho \"===== Demo Started =====\"\necho \"The interactive interface should be available in your web browser.\"\necho \"If it doesn't open automatically, check the terminal output for the URL.\"\n"}