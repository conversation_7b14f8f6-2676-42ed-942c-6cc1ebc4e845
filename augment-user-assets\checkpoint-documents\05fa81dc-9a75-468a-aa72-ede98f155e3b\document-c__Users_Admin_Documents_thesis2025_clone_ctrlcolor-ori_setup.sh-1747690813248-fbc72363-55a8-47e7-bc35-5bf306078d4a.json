{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori", "relPath": "setup.sh"}, "modifiedCode": "#!/bin/bash\n\n# CtrlColor Setup Script\n# This script sets up the CtrlColor environment and downloads required models\n\nset -e  # Exit on error\n\necho \"===== Setting up CtrlColor Environment =====\"\n\n# Check if conda is installed\nif ! command -v conda &> /dev/null; then\n    echo \"Conda is not installed. Please install Miniconda or Anaconda first.\"\n    echo \"Visit: https://docs.conda.io/en/latest/miniconda.html\"\n    exit 1\nfi\n\n# Check if git is installed\nif ! command -v git &> /dev/null; then\n    echo \"Git is not installed. Please install git first.\"\n    exit 1\nfi\n\n# Create conda environment from yaml file\necho \"Creating conda environment from CtrlColor_environ.yaml...\"\nconda env create -f CtrlColor_environ.yaml\n\n# Activate the environment\necho \"Activating conda environment...\"\neval \"$(conda shell.bash hook)\"\nconda activate CtrlColor\n\n# Create directory for pretrained models if it doesn't exist\nif [ ! -d \"./pretrained_models\" ]; then\n    echo \"Creating directory for pretrained models...\"\n    mkdir -p ./pretrained_models\nfi\n\n# Download model checkpoints\necho \"Downloading model checkpoints...\"\necho \"This may take some time depending on your internet connection...\"\n\n# Check if gdown is installed, if not install it\nif ! python -c \"import gdown\" &> /dev/null; then\n    echo \"Installing gdown for Google Drive downloads...\"\n    pip install gdown\nfi\n\n# Download colorization model checkpoint\necho \"Downloading colorization model checkpoint...\"\ngdown --folder https://drive.google.com/drive/folders/1lgqstNwrMCzymowRsbGM-4hk0-7L-eOT -O ./pretrained_models\n\necho \"===== Setup Complete =====\"\necho \"To run the CtrlColor demo:\"\necho \"1. Activate the conda environment: conda activate CtrlColor\"\necho \"2. Run the demo: python test.py\"\necho \"The interactive interface will be available in your web browser.\"\n"}