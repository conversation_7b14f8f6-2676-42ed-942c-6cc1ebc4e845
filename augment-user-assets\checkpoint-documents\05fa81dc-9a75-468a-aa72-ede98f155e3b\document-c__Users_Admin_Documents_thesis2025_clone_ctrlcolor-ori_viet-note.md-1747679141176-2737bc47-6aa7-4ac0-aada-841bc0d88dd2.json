{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori", "relPath": "viet-note.md"}, "modifiedCode": "# CtrlColor: Tô màu hình ảnh tương tác dựa trên mô hình khuếch tán đa phương thức\n\n## Tổng quan\n\nCtrlColor là một framework tô màu hình ảnh tiên tiến tận dụng các mô hình khuếch tán để cung cấp khả năng tô màu có thể kiểm soát cao và đa phương thức. <PERSON>ệ thống cho phép người dùng tô màu tương tác cho hình ảnh đen trắng thông qua nhiều cơ chế điều khiển khác nhau, bao gồm tô màu theo vùng và chỉnh sửa lặp lại.\n\n## Kiến trúc kỹ thuật\n\n### C<PERSON><PERSON> thành phần cốt lõi\n\n1. **Nền tảng mô hình khuếch tán**\n   - Dựa trên kiến trúc Stable Diffusion\n   - Triển khai mô hình khuếch tán tiềm ẩn có điều kiện (ControlLDM)\n   - Sử dụng UNet làm xương sống với cơ chế chú ý chéo (cross-attention)\n   - Tích hợp Self-Attention Guidance (SAG) để cải thiện chất lượng\n\n2. **Cơ chế điều khiển**\n   - ControlNet để hướng dẫn tô màu\n   - Điều khiển theo vùng thông qua mặt nạ (masking)\n   - Điều kiện gợi ý văn bản thông qua nhúng CLIP\n   - Khả năng chỉnh sửa lặp lại\n\n3. **VAE biến dạng**\n   - Bộ tự mã biến phân biến dạng được hướng dẫn bởi nội dung\n   - Sử dụng các tích chập biến dạng có điều biến để căn chỉnh cấu trúc\n   - Bảo toàn chi tiết cấu trúc trong khi cho phép thao tác màu sắc\n   - Giảm tràn màu giữa các vùng\n\n4. **Kiến trúc ControlNet**\n   - Lấy hình ảnh đen trắng làm hướng dẫn cấu trúc\n   - Xử lý đầu vào thông qua bộ mã hóa chuyên biệt\n   - Cung cấp kết nối bỏ qua (skip connections) đến UNet chính\n   - Tích hợp với quá trình khuếch tán thông qua các khối được điều khiển\n\n## Nền tảng toán học\n\n### Quá trình khuếch tán\n\nQuá trình khuếch tán tuân theo các phương trình khuếch tán thuận và ngược tiêu chuẩn:\n\n1. **Khuếch tán thuận**: Dần dần thêm nhiễu vào hình ảnh theo lịch trình phương sai:\n   ```\n   q(x_t | x_{t-1}) = N(x_t; sqrt(1-β_t)x_{t-1}, β_t I)\n   ```\n   trong đó β_t là tham số lịch trình nhiễu tại bước thời gian t.\n\n2. **Khuếch tán ngược**: Học cách dự đoán thành phần nhiễu để dần dần khử nhiễu hình ảnh:\n   ```\n   p_θ(x_{t-1} | x_t) = N(x_{t-1}; μ_θ(x_t, t), Σ_θ(x_t, t))\n   ```\n   trong đó μ_θ và Σ_θ được học bởi mạng nơ-ron.\n\n### Tạo có điều kiện\n\nMô hình kết hợp nhiều tín hiệu điều kiện:\n\n1. **Điều kiện văn bản**: Sử dụng nhúng văn bản CLIP thông qua cơ chế chú ý chéo:\n   ```\n   Attention(Q, K, V) = softmax(QK^T/√d)V\n   ```\n   trong đó Q được lấy từ các đặc trưng UNet và K, V từ nhúng văn bản.\n\n2. **Điều kiện không gian**: Sử dụng hình ảnh đen trắng làm hướng dẫn cấu trúc thông qua ControlNet.\n\n3. **Điều khiển vùng**: Triển khai khuếch tán có mặt nạ cho việc chỉnh sửa cục bộ:\n   ```\n   x_masked = mask * x_original + (1-mask) * x_edited\n   ```\n\n### Hướng dẫn chú ý tự thân (SAG)\n\nMô hình sử dụng SAG để cải thiện chất lượng hình ảnh được tạo ra:\n```\nx_{t-1} = x_{t-1} + λ * (Attention(x_t) - Attention(x_t|c))\n```\ntrong đó λ là tham số tỷ lệ SAG.\n\n## Chi tiết triển khai\n\n### Kiến trúc mô hình\n\n1. **ControlNet**\n   - Lấy hình ảnh đen trắng làm đầu vào\n   - Cung cấp điều kiện không gian thông qua kết nối bỏ qua\n   - Sửa đổi xương sống UNet để kết hợp tín hiệu điều khiển\n\n2. **UNet với cơ chế chú ý chéo**\n   - Xương sống cho mô hình khuếch tán\n   - Kết hợp nhúng văn bản thông qua cơ chế chú ý chéo\n   - Được sửa đổi để chấp nhận tín hiệu điều khiển bổ sung\n\n3. **VAE biến dạng**\n   - Kiến trúc mã hóa-giải mã với tích chập biến dạng\n   - Bảo toàn chi tiết cấu trúc từ đầu vào đen trắng\n   - Giảm chảy màu giữa các vùng\n\n### Tham số chính\n\n- **Bước khuếch tán**: Kiểm soát chất lượng và tốc độ tạo (mặc định: 20)\n- **Cường độ điều khiển**: Xác định mức độ ảnh hưởng của tín hiệu điều khiển đến đầu ra (mặc định: 1.0)\n- **Tỷ lệ hướng dẫn**: Kiểm soát sự tuân thủ với gợi ý văn bản (mặc định: 7.0)\n- **Tỷ lệ SAG**: Kiểm soát ảnh hưởng của hướng dẫn chú ý tự thân (mặc định: 0.05)\n\n## Giao diện tương tác\n\nHệ thống cung cấp giao diện người dùng dựa trên Gradio với các tính năng sau:\n\n1. **Điều khiển đầu vào**\n   - Tải lên hình ảnh đen trắng hoặc màu\n   - Vẽ các nét màu cho tô màu theo vùng\n   - Cung cấp gợi ý văn bản để hướng dẫn phong cách\n\n2. **Tùy chọn xử lý**\n   - Thay đổi theo màu của nét vẽ\n   - Chế độ chỉnh sửa lặp lại\n   - Bật/tắt VAE biến dạng để giảm tràn màu\n\n3. **Tham số nâng cao**\n   - Số lượng mẫu để tạo\n   - Độ phân giải hình ảnh\n   - Số bước khuếch tán\n   - Tỷ lệ hướng dẫn\n   - Kiểm soát hạt giống ngẫu nhiên\n\n## Quy trình xử lý dữ liệu\n\n1. **Xử lý đầu vào**\n   - Chuyển đổi hình ảnh màu sang không gian màu LAB\n   - Trích xuất kênh L cho biểu diễn đen trắng\n   - Xử lý nét vẽ của người dùng để tạo mặt nạ\n\n2. **Tạo mặt nạ**\n   - Tạo mặt nạ nhị phân từ nét vẽ của người dùng\n   - Áp dụng các phép toán hình thái học để tạo biên sạch\n   - Kết hợp mặt nạ với hình ảnh đầu vào\n\n3. **Quá trình khuếch tán**\n   - Mã hóa hình ảnh có mặt nạ vào không gian tiềm ẩn\n   - Áp dụng lấy mẫu khuếch tán có điều kiện\n   - Giải mã kết quả trở lại không gian điểm ảnh\n\n4. **Thao tác không gian màu**\n   - Kết hợp kênh L từ hình ảnh gốc với kênh a, b từ hình ảnh được tạo ra\n   - Chuyển đổi trở lại RGB cho đầu ra cuối cùng\n"}