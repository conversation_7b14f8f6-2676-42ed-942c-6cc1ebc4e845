{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\viet-note.md"}, "originalCode": "# CtrlColor: Tô màu hình ảnh tương tác dựa trên mô hình khuếch tán đa phương thức\n\n## Tổng quan\n\nCtrlColor là một framework tô màu hình ảnh tiên tiến tận dụng các mô hình khuếch tán để cung cấp khả năng tô màu có thể kiểm soát cao và đa phương thức. <PERSON>ệ thống cho phép người dùng tô màu tương tác cho hình ảnh đen trắng thông qua nhiều cơ chế điều khiển khác nhau, bao gồm tô màu theo vùng và chỉnh sửa lặp lại.\n\n## Kiến trúc kỹ thuật\n\n### C<PERSON><PERSON> thành phần cốt lõi\n\n1. **Nền tảng mô hình khuếch tán**\n   - Dựa trên kiến trúc Stable Diffusion\n   - Triển khai mô hình khuếch tán tiềm ẩn có điều kiện (ControlLDM)\n   - Sử dụng UNet làm xương sống với cơ chế chú ý chéo (cross-attention)\n   - Tích hợp Self-Attention Guidance (SAG) để cải thiện chất lượng\n\n2. **Cơ chế điều khiển**\n   - ControlNet để hướng dẫn tô màu\n   - Điều khiển theo vùng thông qua mặt nạ (masking)\n   - Điều kiện gợi ý văn bản thông qua nhúng CLIP\n   - Khả năng chỉnh sửa lặp lại\n\n3. **VAE biến dạng**\n   - Bộ tự mã biến phân biến dạng được hướng dẫn bởi nội dung\n   - Sử dụng các tích chập biến dạng có điều biến để căn chỉnh cấu trúc\n   - Bảo toàn chi tiết cấu trúc trong khi cho phép thao tác màu sắc\n   - Giảm tràn màu giữa các vùng\n\n4. **Kiến trúc ControlNet**\n   - Lấy hình ảnh đen trắng làm hướng dẫn cấu trúc\n   - Xử lý đầu vào thông qua bộ mã hóa chuyên biệt\n   - Cung cấp kết nối bỏ qua (skip connections) đến UNet chính\n   - Tích hợp với quá trình khuếch tán thông qua các khối được điều khiển\n\n## Nền tảng toán học\n\n### Quá trình khuếch tán\n\nQuá trình khuếch tán tuân theo các phương trình khuếch tán thuận và ngược tiêu chuẩn:\n\n1. **Khuếch tán thuận**: Dần dần thêm nhiễu vào hình ảnh theo lịch trình phương sai:\n   ```\n   q(x_t | x_{t-1}) = N(x_t; sqrt(1-β_t)x_{t-1}, β_t I)\n   ```\n   trong đó β_t là tham số lịch trình nhiễu tại bước thời gian t.\n\n2. **Khuếch tán ngược**: Học cách dự đoán thành phần nhiễu để dần dần khử nhiễu hình ảnh:\n   ```\n   p_θ(x_{t-1} | x_t) = N(x_{t-1}; μ_θ(x_t, t), Σ_θ(x_t, t))\n   ```\n   trong đó μ_θ và Σ_θ được học bởi mạng nơ-ron.\n\n### Tạo có điều kiện\n\nMô hình kết hợp nhiều tín hiệu điều kiện:\n\n1. **Điều kiện văn bản**: Sử dụng nhúng văn bản CLIP thông qua cơ chế chú ý chéo:\n   ```\n   Attention(Q, K, V) = softmax(QK^T/√d)V\n   ```\n   trong đó Q được lấy từ các đặc trưng UNet và K, V từ nhúng văn bản.\n\n2. **Điều kiện không gian**: Sử dụng hình ảnh đen trắng làm hướng dẫn cấu trúc thông qua ControlNet.\n\n3. **Điều khiển vùng**: Triển khai khuếch tán có mặt nạ cho việc chỉnh sửa cục bộ:\n   ```\n   x_masked = mask * x_original + (1-mask) * x_edited\n   ```\n\n### Hướng dẫn chú ý tự thân (SAG)\n\nMô hình sử dụng SAG để cải thiện chất lượng hình ảnh được tạo ra:\n```\nx_{t-1} = x_{t-1} + λ * (Attention(x_t) - Attention(x_t|c))\n```\ntrong đó λ là tham số tỷ lệ SAG.\n\n## Chi tiết triển khai\n\n### Kiến trúc mô hình\n\n1. **ControlNet**\n   - Lấy hình ảnh đen trắng làm đầu vào\n   - Cung cấp điều kiện không gian thông qua kết nối bỏ qua\n   - Sửa đổi xương sống UNet để kết hợp tín hiệu điều khiển\n\n2. **UNet với cơ chế chú ý chéo**\n   - Xương sống cho mô hình khuếch tán\n   - Kết hợp nhúng văn bản thông qua cơ chế chú ý chéo\n   - Được sửa đổi để chấp nhận tín hiệu điều khiển bổ sung\n\n3. **VAE biến dạng**\n   - Kiến trúc mã hóa-giải mã với tích chập biến dạng\n   - Bảo toàn chi tiết cấu trúc từ đầu vào đen trắng\n   - Giảm chảy màu giữa các vùng\n\n### Tham số chính\n\n- **Bước khuếch tán**: Kiểm soát chất lượng và tốc độ tạo (mặc định: 20)\n- **Cường độ điều khiển**: Xác định mức độ ảnh hưởng của tín hiệu điều khiển đến đầu ra (mặc định: 1.0)\n- **Tỷ lệ hướng dẫn**: Kiểm soát sự tuân thủ với gợi ý văn bản (mặc định: 7.0)\n- **Tỷ lệ SAG**: Kiểm soát ảnh hưởng của hướng dẫn chú ý tự thân (mặc định: 0.05)\n\n## Giao diện tương tác\n\nHệ thống cung cấp giao diện người dùng dựa trên Gradio với các tính năng sau:\n\n1. **Điều khiển đầu vào**\n   - Tải lên hình ảnh đen trắng hoặc màu\n   - Vẽ các nét màu cho tô màu theo vùng\n   - Cung cấp gợi ý văn bản để hướng dẫn phong cách\n\n2. **Tùy chọn xử lý**\n   - Thay đổi theo màu của nét vẽ\n   - Chế độ chỉnh sửa lặp lại\n   - Bật/tắt VAE biến dạng để giảm tràn màu\n\n3. **Tham số nâng cao**\n   - Số lượng mẫu để tạo\n   - Độ phân giải hình ảnh\n   - Số bước khuếch tán\n   - Tỷ lệ hướng dẫn\n   - Kiểm soát hạt giống ngẫu nhiên\n\n## Quy trình xử lý dữ liệu\n\n1. **Xử lý đầu vào**\n   - Chuyển đổi hình ảnh màu sang không gian màu LAB\n   - Trích xuất kênh L cho biểu diễn đen trắng\n   - Xử lý nét vẽ của người dùng để tạo mặt nạ\n\n2. **Tạo mặt nạ**\n   - Tạo mặt nạ nhị phân từ nét vẽ của người dùng\n   - Áp dụng các phép toán hình thái học để tạo biên sạch\n   - Kết hợp mặt nạ với hình ảnh đầu vào\n\n3. **Quá trình khuếch tán**\n   - Mã hóa hình ảnh có mặt nạ vào không gian tiềm ẩn\n   - Áp dụng lấy mẫu khuếch tán có điều kiện\n   - Giải mã kết quả trở lại không gian điểm ảnh\n\n4. **Thao tác không gian màu**\n   - Kết hợp kênh L từ hình ảnh gốc với kênh a, b từ hình ảnh được tạo ra\n   - Chuyển đổi trở lại RGB cho đầu ra cuối cùng\n", "modifiedCode": "# CtrlColor: Tô màu hình ảnh tương tác dựa trên mô hình khuếch tán đa phương thức\n\n## Tổng quan\n\nCtrlColor là một framework tô màu hình ảnh tiên tiến tận dụng các mô hình khuếch tán để cung cấp khả năng tô màu có thể kiểm soát cao và đa phương thức. <PERSON>ệ thống cho phép người dùng tô màu tương tác cho hình ảnh đen trắng thông qua nhiều cơ chế điều khiển khác nhau, bao gồm tô màu theo vùng và chỉnh sửa lặp lại.\n\n## Kiến trúc kỹ thuật\n\n### C<PERSON><PERSON> thành phần cốt lõi\n\n1. **Nền tảng mô hình khuếch tán**\n   - Dựa trên kiến trúc Stable Diffusion\n   - Triển khai mô hình khuếch tán tiềm ẩn có điều kiện (ControlLDM)\n   - Sử dụng UNet làm xương sống với cơ chế chú ý chéo (cross-attention)\n   - Tích hợp Self-Attention Guidance (SAG) để cải thiện chất lượng\n\n2. **Cơ chế điều khiển**\n   - ControlNet để hướng dẫn tô màu\n   - Điều khiển theo vùng thông qua mặt nạ (masking)\n   - Điều kiện gợi ý văn bản thông qua nhúng CLIP\n   - Khả năng chỉnh sửa lặp lại\n\n3. **VAE biến dạng**\n   - Bộ tự mã biến phân biến dạng được hướng dẫn bởi nội dung\n   - Sử dụng các tích chập biến dạng có điều biến để căn chỉnh cấu trúc\n   - Bảo toàn chi tiết cấu trúc trong khi cho phép thao tác màu sắc\n   - Giảm tràn màu giữa các vùng\n\n4. **Kiến trúc ControlNet**\n   - Lấy hình ảnh đen trắng làm hướng dẫn cấu trúc\n   - Xử lý đầu vào thông qua bộ mã hóa chuyên biệt\n   - Cung cấp kết nối bỏ qua (skip connections) đến UNet chính\n   - Tích hợp với quá trình khuếch tán thông qua các khối được điều khiển\n\n## Nền tảng toán học\n\n### Quá trình khuếch tán\n\nQuá trình khuếch tán tuân theo các phương trình khuếch tán thuận và ngược tiêu chuẩn:\n\n1. **Khuếch tán thuận**: Dần dần thêm nhiễu vào hình ảnh theo lịch trình phương sai:\n   ```\n   q(x_t | x_{t-1}) = N(x_t; sqrt(1-β_t)x_{t-1}, β_t I)\n   ```\n   trong đó β_t là tham số lịch trình nhiễu tại bước thời gian t.\n\n2. **Khuếch tán ngược**: Học cách dự đoán thành phần nhiễu để dần dần khử nhiễu hình ảnh:\n   ```\n   p_θ(x_{t-1} | x_t) = N(x_{t-1}; μ_θ(x_t, t), Σ_θ(x_t, t))\n   ```\n   trong đó μ_θ và Σ_θ được học bởi mạng nơ-ron.\n\n### Tạo có điều kiện\n\nMô hình kết hợp nhiều tín hiệu điều kiện:\n\n1. **Điều kiện văn bản**: Sử dụng nhúng văn bản CLIP thông qua cơ chế chú ý chéo:\n   ```\n   Attention(Q, K, V) = softmax(QK^T/√d)V\n   ```\n   trong đó Q được lấy từ các đặc trưng UNet và K, V từ nhúng văn bản.\n\n2. **Điều kiện không gian**: Sử dụng hình ảnh đen trắng làm hướng dẫn cấu trúc thông qua ControlNet.\n\n3. **Điều khiển vùng**: Triển khai khuếch tán có mặt nạ cho việc chỉnh sửa cục bộ:\n   ```\n   x_masked = mask * x_original + (1-mask) * x_edited\n   ```\n\n### Hướng dẫn chú ý tự thân (SAG)\n\nMô hình sử dụng SAG để cải thiện chất lượng hình ảnh được tạo ra:\n```\nx_{t-1} = x_{t-1} + λ * (Attention(x_t) - Attention(x_t|c))\n```\ntrong đó λ là tham số tỷ lệ SAG.\n\n## Chi tiết triển khai\n\n### Kiến trúc mô hình\n\n1. **ControlNet**\n   - Lấy hình ảnh đen trắng làm đầu vào\n   - Cung cấp điều kiện không gian thông qua kết nối bỏ qua\n   - Sửa đổi xương sống UNet để kết hợp tín hiệu điều khiển\n\n2. **UNet với cơ chế chú ý chéo**\n   - Xương sống cho mô hình khuếch tán\n   - Kết hợp nhúng văn bản thông qua cơ chế chú ý chéo\n   - Được sửa đổi để chấp nhận tín hiệu điều khiển bổ sung\n\n3. **VAE biến dạng**\n   - Kiến trúc mã hóa-giải mã với tích chập biến dạng\n   - Bảo toàn chi tiết cấu trúc từ đầu vào đen trắng\n   - Giảm chảy màu giữa các vùng\n\n### Tham số chính\n\n- **Bước khuếch tán**: Kiểm soát chất lượng và tốc độ tạo (mặc định: 20)\n- **Cường độ điều khiển**: Xác định mức độ ảnh hưởng của tín hiệu điều khiển đến đầu ra (mặc định: 1.0)\n- **Tỷ lệ hướng dẫn**: Kiểm soát sự tuân thủ với gợi ý văn bản (mặc định: 7.0)\n- **Tỷ lệ SAG**: Kiểm soát ảnh hưởng của hướng dẫn chú ý tự thân (mặc định: 0.05)\n\n## Giao diện tương tác\n\nHệ thống cung cấp giao diện người dùng dựa trên Gradio với các tính năng sau:\n\n1. **Điều khiển đầu vào**\n   - Tải lên hình ảnh đen trắng hoặc màu\n   - Vẽ các nét màu cho tô màu theo vùng\n   - Cung cấp gợi ý văn bản để hướng dẫn phong cách\n\n2. **Tùy chọn xử lý**\n   - Thay đổi theo màu của nét vẽ\n   - Chế độ chỉnh sửa lặp lại\n   - Bật/tắt VAE biến dạng để giảm tràn màu\n\n3. **Tham số nâng cao**\n   - Số lượng mẫu để tạo\n   - Độ phân giải hình ảnh\n   - Số bước khuếch tán\n   - Tỷ lệ hướng dẫn\n   - Kiểm soát hạt giống ngẫu nhiên\n\n## Quy trình xử lý dữ liệu\n\n1. **Xử lý đầu vào**\n   - Chuyển đổi hình ảnh màu sang không gian màu LAB\n   - Trích xuất kênh L cho biểu diễn đen trắng\n   - Xử lý nét vẽ của người dùng để tạo mặt nạ\n\n2. **Tạo mặt nạ**\n   - Tạo mặt nạ nhị phân từ nét vẽ của người dùng\n   - Áp dụng các phép toán hình thái học để tạo biên sạch\n   - Kết hợp mặt nạ với hình ảnh đầu vào\n\n3. **Quá trình khuếch tán**\n   - Mã hóa hình ảnh có mặt nạ vào không gian tiềm ẩn\n   - Áp dụng lấy mẫu khuếch tán có điều kiện\n   - Giải mã kết quả trở lại không gian điểm ảnh\n\n4. **Thao tác không gian màu**\n   - Kết hợp kênh L từ hình ảnh gốc với kênh a, b từ hình ảnh được tạo ra\n   - Chuyển đổi trở lại RGB cho đầu ra cuối cùng\n\n## Đổi mới kỹ thuật\n\n1. **VAE biến dạng được hướng dẫn bởi nội dung**\n   - Bảo toàn chi tiết cấu trúc trong khi cho phép tô màu linh hoạt\n   - Giảm chảy màu giữa các vùng\n\n2. **Điều khiển theo vùng**\n   - Cho phép kiểm soát chính xác trên các khu vực cụ thể\n   - Hỗ trợ chỉnh sửa lặp lại trong khi duy trì tính nhất quán\n\n3. **Điều kiện đa phương thức**\n   - Kết hợp gợi ý văn bản, nét vẽ của người dùng và hướng dẫn cấu trúc\n   - Cho phép các phong cách tô màu đa dạng\n\n## Cấu trúc mã nguồn\n\nMã nguồn được tổ chức thành một số mô-đun chính:\n\n1. **cldm/** - Chứa triển khai ControlNet và định nghĩa mô hình\n   - `cldm.py` - Định nghĩa các lớp ControlNet và ControlLDM\n   - `model.py` - Cung cấp tiện ích để tải mô hình và điểm kiểm tra\n   - `ddim_haced_sag_step.py` - Triển khai bộ lấy mẫu DDIM với SAG\n\n2. **ldm/** - Chứa các thành phần mô hình khuếch tán tiềm ẩn cốt lõi\n   - `models/diffusion/` - Triển khai các quá trình khuếch tán (DDPM, DDIM)\n   - `models/autoencoder.py` - Triển khai VAE tiêu chuẩn\n   - `models/autoencoder_train.py` - Triển khai VAE biến dạng\n   - `modules/diffusionmodules/` - Các khối xây dựng UNet và khuếch tán cốt lõi\n   - `modules/attention.py` - Cơ chế chú ý cho mô hình khuếch tán\n   - `modules/attention_dcn_control.py` - Chú ý tích chập biến dạng\n\n3. **taming/** - Chứa các thành phần từ kiến trúc VQGAN\n   - `modules/vqvae/` - Các thành phần lượng tử hóa vector\n   - `modules/discriminator/` - Các thành phần phân biệt GAN\n   - `modules/losses/` - Các hàm mất mát cho việc huấn luyện\n\n4. **test.py** - Điểm vào chính cho giao diện demo Gradio\n\n## Điểm nổi bật trong triển khai\n\n### Lấy mẫu khuếch tán với SAG\n\nBộ lấy mẫu DDIM được mở rộng với Self-Attention Guidance (SAG) để cải thiện chất lượng tạo. Triển khai trong `ddim_haced_sag_step.py` bao gồm một cách tiếp cận tinh vi cho SAG:\n\n```python\ndef p_sample_ddim(self, x, mask, masked_image_latents, c, t, index, repeat_noise=False, use_original_steps=False,\n                  quantize_denoised=False, temperature=1., noise_dropout=0., score_corrector=None,\n                  corrector_kwargs=None, unconditional_guidance_scale=1., sag_scale=0.75, sag_enable=True,\n                  noise=None, unconditional_conditioning=None, dynamic_threshold=None):\n    # Hướng dẫn phi phân loại tiêu chuẩn\n    if unconditional_conditioning is None or unconditional_guidance_scale == 1.:\n        model_output = self.model.apply_model(x, mask, masked_image_latents, t, c)\n    else:\n        model_t = self.model.apply_model(x, mask, masked_image_latents, t, c)\n        model_uncond = self.model.apply_model(x, mask, masked_image_latents, t, unconditional_conditioning)\n        model_output = model_uncond + unconditional_guidance_scale * (model_t - model_uncond)\n\n    # Triển khai SAG\n    if sag_enable == True:\n        # Trích xuất bản đồ chú ý từ mô hình\n        uncond_attn, cond_attn = self.model.model.diffusion_model.middle_block[1].transformer_blocks[0].attn1.attention_probs.chunk(2)\n\n        # Làm suy giảm tiềm ẩn dựa trên chú ý tự thân\n        map_size = self.model.model.diffusion_model.middle_block[1].map_size\n        degraded_latents = self.sag_masking(\n            pred_x0, model_output, x, uncond_attn, map_size, t, eps=noise\n        )\n\n        # Áp dụng mô hình cho tiềm ẩn đã suy giảm\n        if unconditional_conditioning is None or unconditional_guidance_scale == 1.:\n            degraded_model_output = self.model.apply_model(degraded_latents, mask, masked_image_latents, t, c)\n        else:\n            degraded_model_t = self.model.apply_model(degraded_latents, mask, masked_image_latents, t, c)\n            degraded_model_uncond = self.model.apply_model(degraded_latents, mask, masked_image_latents, t, unconditional_conditioning)\n            degraded_model_output = degraded_model_uncond + unconditional_guidance_scale * (degraded_model_t - degraded_model_uncond)\n\n        # Áp dụng hiệu chỉnh SAG\n        model_output += sag_scale * (model_output - degraded_model_output)\n```\n\nHàm `sag_masking` triển khai một phần quan trọng của phương pháp SAG:\n\n```python\ndef sag_masking(self, original_latents, model_output, x, attn_map, map_size, t, eps):\n    # Trích xuất kích thước bản đồ chú ý\n    bh, hw1, hw2 = attn_map.shape\n    b, latent_channel, latent_h, latent_w = original_latents.shape\n    h = 4  # kích thước đầu chú ý\n\n    # Định hình lại bản đồ chú ý\n    attn_map = attn_map.reshape(b, h, hw1, hw2)\n\n    # Tạo mặt nạ chú ý nơi tổng chú ý > 1.0\n    attn_mask = attn_map.mean(1, keepdim=False).sum(1, keepdim=False) > 1.0\n    attn_mask = (\n        attn_mask.reshape(b, map_size[0], map_size[1])\n        .unsqueeze(1)\n        .repeat(1, latent_channel, 1, 1)\n        .type(attn_map.dtype)\n    )\n\n    # Thay đổi kích thước mặt nạ để phù hợp với kích thước tiềm ẩn\n    attn_mask = F.interpolate(attn_mask, (latent_h, latent_w))\n\n    # Áp dụng làm mờ Gaussian để tạo tiềm ẩn suy giảm\n    degraded_latents = gaussian_blur_2d(original_latents, kernel_size=9, sigma=1.0)\n\n    # Kết hợp tiềm ẩn gốc và suy giảm dựa trên mặt nạ chú ý\n    degraded_latents = degraded_latents * attn_mask + original_latents * (1 - attn_mask)\n\n    return degraded_latents\n```\n\n### VAE biến dạng và tích chập biến dạng\n\nVAE biến dạng mở rộng VAE tiêu chuẩn với giải mã được hướng dẫn bởi nội dung, sử dụng hình ảnh đen trắng làm hướng dẫn cấu trúc:\n\n```python\ndef decode(self, z, gray_content_z):\n    z = self.post_quant_conv(z)\n    gray_content_z = self.post_quant_conv(gray_content_z)\n    dec = self.decoder(z, gray_content_z)\n    return dec\n```\n\nBộ giải mã sử dụng tích chập biến dạng để căn chỉnh màu sắc được tạo ra với nội dung cấu trúc từ hình ảnh đen trắng. Triển khai trong `attention_dcn_control.py` cho thấy cách thức hoạt động của các tích chập biến dạng này:\n\n```python\nclass ModulatedDeformConvPack(ModulatedDeformConv):\n    \"\"\"\n    Một đóng gói tích chập biến dạng có điều biến hoạt động như các lớp tích chập thông thường.\n    \"\"\"\n    def __init__(self, *args, **kwargs):\n        super(ModulatedDeformConvPack, self).__init__(*args, **kwargs)\n\n        self.conv_offset = nn.Conv2d(\n            self.in_channels,\n            self.deformable_groups * 3 * self.kernel_size[0] * self.kernel_size[1],\n            kernel_size=self.kernel_size,\n            stride=_pair(self.stride),\n            padding=_pair(self.padding),\n            dilation=_pair(self.dilation),\n            bias=True)\n        self.init_weights()\n\n    def init_weights(self):\n        super(ModulatedDeformConvPack, self).init_weights()\n        if hasattr(self, 'conv_offset'):\n            self.conv_offset.weight.data.zero_()\n            self.conv_offset.bias.data.zero_()\n\n    def forward(self, x):\n        # Tạo offset và mặt nạ\n        out = self.conv_offset(x)\n        o1, o2, mask = torch.chunk(out, 3, dim=1)\n        offset = torch.cat((o1, o2), dim=1)\n        mask = torch.sigmoid(mask)\n\n        # Áp dụng tích chập biến dạng\n        return torchvision.ops.deform_conv2d(x, offset, self.weight, self.bias, self.stride, self.padding,\n                                            self.dilation, mask)\n```\n\nTriển khai này cho phép các nhân tích chập được điều chỉnh động dựa trên nội dung đầu vào, cho phép mô hình căn chỉnh tốt hơn thông tin màu sắc với chi tiết cấu trúc. Các tích chập biến dạng được tích hợp vào các khối biến đổi không gian:\n\n```python\nclass SpatialTransformer_dcn(nn.Module):\n    def __init__(self, in_channels, n_heads, d_head, depth=1, dropout=0., context_dim=None,\n                 disable_self_attn=False, use_linear=False, use_checkpoint=True):\n        # ... mã khởi tạo ...\n\n        # Lớp tích chập biến dạng\n        self.dcn_cnn = ModulatedDeformConvPack(inner_dim,\n                                      inner_dim,\n                                      kernel_size=3,\n                                      stride=1,\n                                      padding=1)\n\n    def forward(self, x, context=None, dcn_guide=None):\n        # ... xử lý biến đổi ...\n\n        # Áp dụng tích chập biến dạng\n        x = self.dcn_cnn(x)\n\n        # ... xử lý cuối cùng ...\n        return x + x_in\n```\n\nKiến trúc này cho phép mô hình duy trì độ trung thực cấu trúc trong khi tạo ra các biến thể màu sắc đa dạng, giảm chảy màu giữa các vùng và bảo toàn các chi tiết tinh tế.\n"}