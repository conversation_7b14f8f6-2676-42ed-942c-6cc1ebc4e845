{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\viet-note.md"}, "originalCode": "# CtrlColor: Tô màu hình ảnh tương tác dựa trên mô hình khuếch tán đa phương thức\n\n## Tổng quan\n\nCtrlColor là một framework tô màu hình ảnh tiên tiến tận dụng các mô hình khuếch tán để cung cấp khả năng tô màu có thể kiểm soát cao và đa phương thức. <PERSON>ệ thống cho phép người dùng tô màu tương tác cho hình ảnh đen trắng thông qua nhiều cơ chế điều khiển khác nhau, bao gồm tô màu theo vùng và chỉnh sửa lặp lại.\n\n## Kiến trúc kỹ thuật\n\n### C<PERSON><PERSON> thành phần cốt lõi\n\n1. **Nền tảng mô hình khuếch tán**\n   - Dựa trên kiến trúc Stable Diffusion\n   - Triển khai mô hình khuếch tán tiềm ẩn có điều kiện (ControlLDM)\n   - Sử dụng UNet làm xương sống với cơ chế chú ý chéo (cross-attention)\n   - Tích hợp Self-Attention Guidance (SAG) để cải thiện chất lượng\n\n2. **Cơ chế điều khiển**\n   - ControlNet để hướng dẫn tô màu\n   - Điều khiển theo vùng thông qua mặt nạ (masking)\n   - Điều kiện gợi ý văn bản thông qua nhúng CLIP\n   - Khả năng chỉnh sửa lặp lại\n\n3. **VAE biến dạng**\n   - Bộ tự mã biến phân biến dạng được hướng dẫn bởi nội dung\n   - Sử dụng các tích chập biến dạng có điều biến để căn chỉnh cấu trúc\n   - Bảo toàn chi tiết cấu trúc trong khi cho phép thao tác màu sắc\n   - Giảm tràn màu giữa các vùng\n\n4. **Kiến trúc ControlNet**\n   - Lấy hình ảnh đen trắng làm hướng dẫn cấu trúc\n   - Xử lý đầu vào thông qua bộ mã hóa chuyên biệt\n   - Cung cấp kết nối bỏ qua (skip connections) đến UNet chính\n   - Tích hợp với quá trình khuếch tán thông qua các khối được điều khiển\n\n## Nền tảng toán học\n\n### Quá trình khuếch tán\n\nQuá trình khuếch tán tuân theo các phương trình khuếch tán thuận và ngược tiêu chuẩn:\n\n1. **Khuếch tán thuận**: Dần dần thêm nhiễu vào hình ảnh theo lịch trình phương sai:\n   $$q(x_t | x_{t-1}) = \\mathcal{N}(x_t; \\sqrt{1-\\beta_t}x_{t-1}, \\beta_t \\mathbf{I})$$\n   trong đó $\\beta_t$ là tham số lịch trình nhiễu tại bước thời gian $t$.\n\n2. **Khuếch tán ngược**: Học cách dự đoán thành phần nhiễu để dần dần khử nhiễu hình ảnh:\n   $$p_\\theta(x_{t-1} | x_t) = \\mathcal{N}(x_{t-1}; \\mu_\\theta(x_t, t), \\Sigma_\\theta(x_t, t))$$\n   trong đó $\\mu_\\theta$ và $\\Sigma_\\theta$ được học bởi mạng nơ-ron.\n\n### Tạo có điều kiện\n\nMô hình kết hợp nhiều tín hiệu điều kiện:\n\n1. **Điều kiện văn bản**: Sử dụng nhúng văn bản CLIP thông qua cơ chế chú ý chéo:\n   $$\\text{Attention}(Q, K, V) = \\text{softmax}\\left(\\frac{QK^T}{\\sqrt{d}}\\right)V$$\n   trong đó $Q$ được lấy từ các đặc trưng UNet và $K, V$ từ nhúng văn bản.\n\n2. **Điều kiện không gian**: Sử dụng hình ảnh đen trắng làm hướng dẫn cấu trúc thông qua ControlNet.\n\n3. **Điều khiển vùng**: Triển khai khuếch tán có mặt nạ cho việc chỉnh sửa cục bộ:\n   $$x_{\\text{masked}} = \\text{mask} \\cdot x_{\\text{original}} + (1-\\text{mask}) \\cdot x_{\\text{edited}}$$\n\n### Hướng dẫn chú ý tự thân (SAG)\n\nMô hình sử dụng SAG để cải thiện chất lượng hình ảnh được tạo ra:\n```\nx_{t-1} = x_{t-1} + λ * (Attention(x_t) - Attention(x_t|c))\n```\ntrong đó λ là tham số tỷ lệ SAG.\n\n## Chi tiết triển khai\n\n### Kiến trúc mô hình\n\n1. **ControlNet**\n   - Lấy hình ảnh đen trắng làm đầu vào\n   - Cung cấp điều kiện không gian thông qua kết nối bỏ qua\n   - Sửa đổi xương sống UNet để kết hợp tín hiệu điều khiển\n\n2. **UNet với cơ chế chú ý chéo**\n   - Xương sống cho mô hình khuếch tán\n   - Kết hợp nhúng văn bản thông qua cơ chế chú ý chéo\n   - Được sửa đổi để chấp nhận tín hiệu điều khiển bổ sung\n\n3. **VAE biến dạng**\n   - Kiến trúc mã hóa-giải mã với tích chập biến dạng\n   - Bảo toàn chi tiết cấu trúc từ đầu vào đen trắng\n   - Giảm chảy màu giữa các vùng\n\n### Tham số chính\n\n- **Bước khuếch tán**: Kiểm soát chất lượng và tốc độ tạo (mặc định: 20)\n- **Cường độ điều khiển**: Xác định mức độ ảnh hưởng của tín hiệu điều khiển đến đầu ra (mặc định: 1.0)\n- **Tỷ lệ hướng dẫn**: Kiểm soát sự tuân thủ với gợi ý văn bản (mặc định: 7.0)\n- **Tỷ lệ SAG**: Kiểm soát ảnh hưởng của hướng dẫn chú ý tự thân (mặc định: 0.05)\n\n## Giao diện tương tác\n\nHệ thống cung cấp giao diện người dùng dựa trên Gradio với các tính năng sau:\n\n1. **Điều khiển đầu vào**\n   - Tải lên hình ảnh đen trắng hoặc màu\n   - Vẽ các nét màu cho tô màu theo vùng\n   - Cung cấp gợi ý văn bản để hướng dẫn phong cách\n\n2. **Tùy chọn xử lý**\n   - Thay đổi theo màu của nét vẽ\n   - Chế độ chỉnh sửa lặp lại\n   - Bật/tắt VAE biến dạng để giảm tràn màu\n\n3. **Tham số nâng cao**\n   - Số lượng mẫu để tạo\n   - Độ phân giải hình ảnh\n   - Số bước khuếch tán\n   - Tỷ lệ hướng dẫn\n   - Kiểm soát hạt giống ngẫu nhiên\n\n## Quy trình xử lý dữ liệu\n\n1. **Xử lý đầu vào**\n   - Chuyển đổi hình ảnh màu sang không gian màu LAB\n   - Trích xuất kênh L cho biểu diễn đen trắng\n   - Xử lý nét vẽ của người dùng để tạo mặt nạ\n\n2. **Tạo mặt nạ**\n   - Tạo mặt nạ nhị phân từ nét vẽ của người dùng\n   - Áp dụng các phép toán hình thái học để tạo biên sạch\n   - Kết hợp mặt nạ với hình ảnh đầu vào\n\n3. **Quá trình khuếch tán**\n   - Mã hóa hình ảnh có mặt nạ vào không gian tiềm ẩn\n   - Áp dụng lấy mẫu khuếch tán có điều kiện\n   - Giải mã kết quả trở lại không gian điểm ảnh\n\n4. **Thao tác không gian màu**\n   - Kết hợp kênh L từ hình ảnh gốc với kênh a, b từ hình ảnh được tạo ra\n   - Chuyển đổi trở lại RGB cho đầu ra cuối cùng\n\n## Đổi mới kỹ thuật\n\n1. **VAE biến dạng được hướng dẫn bởi nội dung**\n   - Bảo toàn chi tiết cấu trúc trong khi cho phép tô màu linh hoạt\n   - Giảm chảy màu giữa các vùng\n\n2. **Điều khiển theo vùng**\n   - Cho phép kiểm soát chính xác trên các khu vực cụ thể\n   - Hỗ trợ chỉnh sửa lặp lại trong khi duy trì tính nhất quán\n\n3. **Điều kiện đa phương thức**\n   - Kết hợp gợi ý văn bản, nét vẽ của người dùng và hướng dẫn cấu trúc\n   - Cho phép các phong cách tô màu đa dạng\n\n## Cấu trúc mã nguồn\n\nMã nguồn được tổ chức thành một số mô-đun chính:\n\n1. **cldm/** - Chứa triển khai ControlNet và định nghĩa mô hình\n   - `cldm.py` - Định nghĩa các lớp ControlNet và ControlLDM\n   - `model.py` - Cung cấp tiện ích để tải mô hình và điểm kiểm tra\n   - `ddim_haced_sag_step.py` - Triển khai bộ lấy mẫu DDIM với SAG\n\n2. **ldm/** - Chứa các thành phần mô hình khuếch tán tiềm ẩn cốt lõi\n   - `models/diffusion/` - Triển khai các quá trình khuếch tán (DDPM, DDIM)\n   - `models/autoencoder.py` - Triển khai VAE tiêu chuẩn\n   - `models/autoencoder_train.py` - Triển khai VAE biến dạng\n   - `modules/diffusionmodules/` - Các khối xây dựng UNet và khuếch tán cốt lõi\n   - `modules/attention.py` - Cơ chế chú ý cho mô hình khuếch tán\n   - `modules/attention_dcn_control.py` - Chú ý tích chập biến dạng\n\n3. **taming/** - Chứa các thành phần từ kiến trúc VQGAN\n   - `modules/vqvae/` - Các thành phần lượng tử hóa vector\n   - `modules/discriminator/` - Các thành phần phân biệt GAN\n   - `modules/losses/` - Các hàm mất mát cho việc huấn luyện\n\n4. **test.py** - Điểm vào chính cho giao diện demo Gradio\n\n## Điểm nổi bật trong triển khai\n\n### Lấy mẫu khuếch tán với SAG\n\nBộ lấy mẫu DDIM được mở rộng với Self-Attention Guidance (SAG) để cải thiện chất lượng tạo. Triển khai trong `ddim_haced_sag_step.py` bao gồm một cách tiếp cận tinh vi cho SAG:\n\n```python\ndef p_sample_ddim(self, x, mask, masked_image_latents, c, t, index, repeat_noise=False, use_original_steps=False,\n                  quantize_denoised=False, temperature=1., noise_dropout=0., score_corrector=None,\n                  corrector_kwargs=None, unconditional_guidance_scale=1., sag_scale=0.75, sag_enable=True,\n                  noise=None, unconditional_conditioning=None, dynamic_threshold=None):\n    # Hướng dẫn phi phân loại tiêu chuẩn\n    if unconditional_conditioning is None or unconditional_guidance_scale == 1.:\n        model_output = self.model.apply_model(x, mask, masked_image_latents, t, c)\n    else:\n        model_t = self.model.apply_model(x, mask, masked_image_latents, t, c)\n        model_uncond = self.model.apply_model(x, mask, masked_image_latents, t, unconditional_conditioning)\n        model_output = model_uncond + unconditional_guidance_scale * (model_t - model_uncond)\n\n    # Triển khai SAG\n    if sag_enable == True:\n        # Trích xuất bản đồ chú ý từ mô hình\n        uncond_attn, cond_attn = self.model.model.diffusion_model.middle_block[1].transformer_blocks[0].attn1.attention_probs.chunk(2)\n\n        # Làm suy giảm tiềm ẩn dựa trên chú ý tự thân\n        map_size = self.model.model.diffusion_model.middle_block[1].map_size\n        degraded_latents = self.sag_masking(\n            pred_x0, model_output, x, uncond_attn, map_size, t, eps=noise\n        )\n\n        # Áp dụng mô hình cho tiềm ẩn đã suy giảm\n        if unconditional_conditioning is None or unconditional_guidance_scale == 1.:\n            degraded_model_output = self.model.apply_model(degraded_latents, mask, masked_image_latents, t, c)\n        else:\n            degraded_model_t = self.model.apply_model(degraded_latents, mask, masked_image_latents, t, c)\n            degraded_model_uncond = self.model.apply_model(degraded_latents, mask, masked_image_latents, t, unconditional_conditioning)\n            degraded_model_output = degraded_model_uncond + unconditional_guidance_scale * (degraded_model_t - degraded_model_uncond)\n\n        # Áp dụng hiệu chỉnh SAG\n        model_output += sag_scale * (model_output - degraded_model_output)\n```\n\nHàm `sag_masking` triển khai một phần quan trọng của phương pháp SAG:\n\n```python\ndef sag_masking(self, original_latents, model_output, x, attn_map, map_size, t, eps):\n    # Trích xuất kích thước bản đồ chú ý\n    bh, hw1, hw2 = attn_map.shape\n    b, latent_channel, latent_h, latent_w = original_latents.shape\n    h = 4  # kích thước đầu chú ý\n\n    # Định hình lại bản đồ chú ý\n    attn_map = attn_map.reshape(b, h, hw1, hw2)\n\n    # Tạo mặt nạ chú ý nơi tổng chú ý > 1.0\n    attn_mask = attn_map.mean(1, keepdim=False).sum(1, keepdim=False) > 1.0\n    attn_mask = (\n        attn_mask.reshape(b, map_size[0], map_size[1])\n        .unsqueeze(1)\n        .repeat(1, latent_channel, 1, 1)\n        .type(attn_map.dtype)\n    )\n\n    # Thay đổi kích thước mặt nạ để phù hợp với kích thước tiềm ẩn\n    attn_mask = F.interpolate(attn_mask, (latent_h, latent_w))\n\n    # Áp dụng làm mờ Gaussian để tạo tiềm ẩn suy giảm\n    degraded_latents = gaussian_blur_2d(original_latents, kernel_size=9, sigma=1.0)\n\n    # Kết hợp tiềm ẩn gốc và suy giảm dựa trên mặt nạ chú ý\n    degraded_latents = degraded_latents * attn_mask + original_latents * (1 - attn_mask)\n\n    return degraded_latents\n```\n\n### VAE biến dạng và tích chập biến dạng\n\nVAE biến dạng mở rộng VAE tiêu chuẩn với giải mã được hướng dẫn bởi nội dung, sử dụng hình ảnh đen trắng làm hướng dẫn cấu trúc:\n\n```python\ndef decode(self, z, gray_content_z):\n    z = self.post_quant_conv(z)\n    gray_content_z = self.post_quant_conv(gray_content_z)\n    dec = self.decoder(z, gray_content_z)\n    return dec\n```\n\nBộ giải mã sử dụng tích chập biến dạng để căn chỉnh màu sắc được tạo ra với nội dung cấu trúc từ hình ảnh đen trắng. Triển khai trong `attention_dcn_control.py` cho thấy cách thức hoạt động của các tích chập biến dạng này:\n\n```python\nclass ModulatedDeformConvPack(ModulatedDeformConv):\n    \"\"\"\n    Một đóng gói tích chập biến dạng có điều biến hoạt động như các lớp tích chập thông thường.\n    \"\"\"\n    def __init__(self, *args, **kwargs):\n        super(ModulatedDeformConvPack, self).__init__(*args, **kwargs)\n\n        self.conv_offset = nn.Conv2d(\n            self.in_channels,\n            self.deformable_groups * 3 * self.kernel_size[0] * self.kernel_size[1],\n            kernel_size=self.kernel_size,\n            stride=_pair(self.stride),\n            padding=_pair(self.padding),\n            dilation=_pair(self.dilation),\n            bias=True)\n        self.init_weights()\n\n    def init_weights(self):\n        super(ModulatedDeformConvPack, self).init_weights()\n        if hasattr(self, 'conv_offset'):\n            self.conv_offset.weight.data.zero_()\n            self.conv_offset.bias.data.zero_()\n\n    def forward(self, x):\n        # Tạo offset và mặt nạ\n        out = self.conv_offset(x)\n        o1, o2, mask = torch.chunk(out, 3, dim=1)\n        offset = torch.cat((o1, o2), dim=1)\n        mask = torch.sigmoid(mask)\n\n        # Áp dụng tích chập biến dạng\n        return torchvision.ops.deform_conv2d(x, offset, self.weight, self.bias, self.stride, self.padding,\n                                            self.dilation, mask)\n```\n\nTriển khai này cho phép các nhân tích chập được điều chỉnh động dựa trên nội dung đầu vào, cho phép mô hình căn chỉnh tốt hơn thông tin màu sắc với chi tiết cấu trúc. Các tích chập biến dạng được tích hợp vào các khối biến đổi không gian:\n\n```python\nclass SpatialTransformer_dcn(nn.Module):\n    def __init__(self, in_channels, n_heads, d_head, depth=1, dropout=0., context_dim=None,\n                 disable_self_attn=False, use_linear=False, use_checkpoint=True):\n        # ... mã khởi tạo ...\n\n        # Lớp tích chập biến dạng\n        self.dcn_cnn = ModulatedDeformConvPack(inner_dim,\n                                      inner_dim,\n                                      kernel_size=3,\n                                      stride=1,\n                                      padding=1)\n\n    def forward(self, x, context=None, dcn_guide=None):\n        # ... xử lý biến đổi ...\n\n        # Áp dụng tích chập biến dạng\n        x = self.dcn_cnn(x)\n\n        # ... xử lý cuối cùng ...\n        return x + x_in\n```\n\nKiến trúc này cho phép mô hình duy trì độ trung thực cấu trúc trong khi tạo ra các biến thể màu sắc đa dạng, giảm chảy màu giữa các vùng và bảo toàn các chi tiết tinh tế.\n\n### Triển khai ControlNet\n\nKiến trúc ControlNet được triển khai trong `cldm/cldm.py` và đóng vai trò là xương sống cho hướng dẫn cấu trúc trong quá trình tô màu:\n\n```python\nclass ControlNet(nn.Module):\n    def __init__(self, image_size, in_channels, model_channels, hint_channels, num_res_blocks,\n                 attention_resolutions, dropout=0, channel_mult=(1, 2, 4, 8), conv_resample=True,\n                 dims=2, use_checkpoint=False, use_fp16=False, num_heads=-1, num_head_channels=-1,\n                 num_heads_upsample=-1, use_scale_shift_norm=False, resblock_updown=False,\n                 use_new_attention_order=False, use_spatial_transformer=False, transformer_depth=1,\n                 context_dim=None, n_embed=None, legacy=True, disable_self_attentions=None,\n                 num_attention_blocks=None, disable_middle_self_attn=False, use_linear_in_transformer=False):\n        super().__init__()\n        # ... mã khởi tạo ...\n\n        # Xử lý đầu vào cho gợi ý (hình ảnh đen trắng)\n        self.input_hint_block = TimestepEmbedSequential(\n            conv_nd(dims, hint_channels, 16, 3, padding=1),\n            nn.SiLU(),\n            conv_nd(dims, 16, 16, 3, padding=1),\n            nn.SiLU(),\n            conv_nd(dims, 16, 32, 3, padding=1, stride=2),\n            nn.SiLU(),\n            conv_nd(dims, 32, 32, 3, padding=1),\n            nn.SiLU(),\n            conv_nd(dims, 32, 96, 3, padding=1, stride=2),\n            nn.SiLU(),\n            conv_nd(dims, 96, 96, 3, padding=1),\n            nn.SiLU(),\n            conv_nd(dims, 96, 256, 3, padding=1, stride=2),\n            nn.SiLU(),\n            zero_module(conv_nd(dims, 256, model_channels, 3, padding=1))\n        )\n\n        # ... các thành phần mạng khác ...\n\n    def forward(self, x, hint, timesteps, context, **kwargs):\n        # Xử lý gợi ý (hình ảnh đen trắng)\n        guided_hint = self.input_hint_block(hint, emb, context)\n\n        # Xử lý đầu vào qua mạng với gợi ý\n        outs = []\n        h = x.type(self.dtype)\n\n        for module, zero_conv in zip(self.input_blocks, self.zero_convs):\n            if guided_hint is not None:\n                h = module(h, emb, context)\n                h += guided_hint  # Thêm đặc trưng gợi ý\n                guided_hint = None\n            else:\n                h = module(h, emb, context)\n            outs.append(zero_conv(h, emb, context))\n\n        # Xử lý qua khối giữa\n        h = self.middle_block(h, emb, context)\n        outs.append(self.middle_block_out(h, emb, context))\n\n        return outs\n```\n\nControlNet được tích hợp với mô hình khuếch tán chính thông qua lớp `ControlLDM`:\n\n```python\nclass ControlLDM(LatentDiffusion):\n    def __init__(self, control_stage_config, control_key, only_mid_control, *args, **kwargs):\n        super().__init__(*args, **kwargs)\n        self.control_model = instantiate_from_config(control_stage_config)\n        self.control_key = control_key\n        self.only_mid_control = only_mid_control\n        self.control_scales = [1.0] * 13\n\n    def apply_model(self, x_noisy, mask, masked_image_latents, t, cond, *args, **kwargs):\n        assert isinstance(cond, dict)\n        diffusion_model = self.model.diffusion_model\n\n        cond_txt = torch.cat(cond['c_crossattn'], 1)\n\n        if cond['c_concat'] is None:\n            # Không có tín hiệu điều khiển\n            eps = diffusion_model(x=x_noisy, timesteps=t, context=cond_txt, control=None,\n                                 only_mid_control=self.only_mid_control)\n        else:\n            # Lấy tín hiệu điều khiển từ ControlNet\n            control = self.control_model(x=x_noisy, hint=torch.cat(cond['c_concat'], 1),\n                                        timesteps=t, context=cond_txt)\n\n            # Áp dụng tỷ lệ điều khiển\n            control = [c * scale for c, scale in zip(control, self.control_scales)]\n\n            # Chuẩn bị mặt nạ và tiềm ẩn hình ảnh có mặt nạ\n            mask = torch.cat([mask] * x_noisy.shape[0])\n            masked_image_latents = torch.cat([masked_image_latents] * x_noisy.shape[0])\n\n            # Nối các đầu vào cho mô hình khuếch tán\n            x_noisy = torch.cat([x_noisy, mask, masked_image_latents], dim=1)\n\n            # Áp dụng mô hình khuếch tán với điều khiển\n            eps = diffusion_model(x=x_noisy, timesteps=t, context=cond_txt, control=control,\n                                 only_mid_control=self.only_mid_control)\n\n        return eps\n```\n\nTriển khai này cho phép mô hình sử dụng hiệu quả hình ảnh đen trắng làm hướng dẫn cấu trúc cho quá trình tô màu, đảm bảo rằng màu sắc được tạo ra phù hợp với cấu trúc hình ảnh gốc.\n\n### Điều khiển theo vùng\n\nĐiều khiển theo vùng được triển khai thông qua việc tạo mặt nạ:\n\n```python\ndef get_mask(input_image, hint_image):\n    mask = input_image.copy()\n    H, W, C = input_image.shape\n    for i in range(H):\n        for j in range(W):\n            if input_image[i,j,0] == hint_image[i,j,0]:\n                mask[i,j,:] = 255.\n            else:\n                mask[i,j,:] = 0.\n    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3,3))\n    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)\n    return mask\n```\n\n## Kết luận\n\nCtrlColor đại diện cho một bước tiến đáng kể trong lĩnh vực tô màu hình ảnh tương tác bằng cách tận dụng các mô hình khuếch tán với nhiều cơ chế điều khiển. Hệ thống cung cấp giao diện người dùng thân thiện cho việc tô màu chất lượng cao, có thể kiểm soát với các ứng dụng trong phục hồi ảnh, sáng tạo nghệ thuật và sản xuất phương tiện truyền thông.\n\nNhững đổi mới kỹ thuật trong mã nguồn này, đặc biệt là VAE biến dạng được hướng dẫn bởi nội dung và các cơ chế điều khiển theo vùng, cho thấy cách các mô hình khuếch tán có thể được điều chỉnh cho các tác vụ chỉnh sửa tương tác với độ chính xác và chất lượng cao. Việc tích hợp nhiều tín hiệu điều kiện (văn bản, cấu trúc, nét vẽ của người dùng) cho phép một hệ thống tô màu linh hoạt và mạnh mẽ cân bằng giữa kiểm soát của người dùng và sáng tạo do AI tạo ra.\n\n## Không gian màu LAB và tô màu\n\nMột khía cạnh kỹ thuật quan trọng của CtrlColor là việc sử dụng không gian màu LAB cho tô màu:\n\n### Không gian màu LAB\n\nKhông gian màu LAB bao gồm ba kênh:\n- **L**: Độ sáng (0-100), đại diện cho độ sáng của hình ảnh\n- **a**: Trục Xanh lá-Đỏ (-128 đến +127)\n- **b**: Trục Xanh dương-Vàng (-128 đến +127)\n\nKhông gian màu này đặc biệt phù hợp cho các tác vụ tô màu vì:\n\n1. **Đồng nhất về nhận thức**: LAB được thiết kế để xấp xỉ thị giác con người, làm cho nó đồng nhất hơn về mặt nhận thức so với RGB.\n\n2. **Tách biệt độ sáng và màu sắc**: Kênh L chứa tất cả thông tin cấu trúc, trong khi kênh a và b chỉ chứa thông tin màu sắc.\n\n### Triển khai trong CtrlColor\n\nCtrlColor tận dụng sự tách biệt này bằng cách:\n\n1. **Bảo toàn cấu trúc**: Kênh L từ hình ảnh đen trắng gốc được giữ nguyên, đảm bảo độ trung thực cấu trúc.\n\n2. **Tạo kênh màu**: Mô hình khuếch tán tập trung vào việc tạo ra kênh a và b.\n\n3. **Kết hợp kênh**: Hình ảnh tô màu cuối cùng được tạo ra bằng cách kết hợp kênh L gốc với kênh a và b được tạo ra:\n\n```python\n# Chuyển đổi đầu vào sang không gian màu LAB\ninput_image = cv2.cvtColor(input_image, cv2.COLOR_RGB2LAB)[:,:,0]\ninput_image = cv2.merge([input_image, input_image, input_image])\n\n# Sau khi tạo kết quả, kết hợp kênh L với kênh a,b được tạo ra\nresults_tmp = [cv2.cvtColor(np.array(i), cv2.COLOR_RGB2LAB) for i in results_ori]\nresults = [cv2.merge([input_image[:,:,0], tmp[:,:,1], tmp[:,:,2]]) for tmp in results_tmp]\nresults_mergeL = [cv2.cvtColor(np.asarray(i), cv2.COLOR_LAB2RGB) for i in results]\n```\n\nCách tiếp cận này đảm bảo rằng các chi tiết cấu trúc của hình ảnh gốc được bảo toàn hoàn hảo trong khi cho phép tự do sáng tạo trong tô màu.\n", "modifiedCode": "# CtrlColor: Tô màu hình ảnh tương tác dựa trên mô hình khuếch tán đa phương thức\n\n## Tổng quan\n\nCtrlColor là một framework tô màu hình ảnh tiên tiến tận dụng các mô hình khuếch tán để cung cấp khả năng tô màu có thể kiểm soát cao và đa phương thức. <PERSON>ệ thống cho phép người dùng tô màu tương tác cho hình ảnh đen trắng thông qua nhiều cơ chế điều khiển khác nhau, bao gồm tô màu theo vùng và chỉnh sửa lặp lại.\n\n## Kiến trúc kỹ thuật\n\n### C<PERSON><PERSON> thành phần cốt lõi\n\n1. **Nền tảng mô hình khuếch tán**\n   - Dựa trên kiến trúc Stable Diffusion\n   - Triển khai mô hình khuếch tán tiềm ẩn có điều kiện (ControlLDM)\n   - Sử dụng UNet làm xương sống với cơ chế chú ý chéo (cross-attention)\n   - Tích hợp Self-Attention Guidance (SAG) để cải thiện chất lượng\n\n2. **Cơ chế điều khiển**\n   - ControlNet để hướng dẫn tô màu\n   - Điều khiển theo vùng thông qua mặt nạ (masking)\n   - Điều kiện gợi ý văn bản thông qua nhúng CLIP\n   - Khả năng chỉnh sửa lặp lại\n\n3. **VAE biến dạng**\n   - Bộ tự mã biến phân biến dạng được hướng dẫn bởi nội dung\n   - Sử dụng các tích chập biến dạng có điều biến để căn chỉnh cấu trúc\n   - Bảo toàn chi tiết cấu trúc trong khi cho phép thao tác màu sắc\n   - Giảm tràn màu giữa các vùng\n\n4. **Kiến trúc ControlNet**\n   - Lấy hình ảnh đen trắng làm hướng dẫn cấu trúc\n   - Xử lý đầu vào thông qua bộ mã hóa chuyên biệt\n   - Cung cấp kết nối bỏ qua (skip connections) đến UNet chính\n   - Tích hợp với quá trình khuếch tán thông qua các khối được điều khiển\n\n## Nền tảng toán học\n\n### Quá trình khuếch tán\n\nQuá trình khuếch tán tuân theo các phương trình khuếch tán thuận và ngược tiêu chuẩn:\n\n1. **Khuếch tán thuận**: Dần dần thêm nhiễu vào hình ảnh theo lịch trình phương sai:\n   $$q(x_t | x_{t-1}) = \\mathcal{N}(x_t; \\sqrt{1-\\beta_t}x_{t-1}, \\beta_t \\mathbf{I})$$\n   trong đó $\\beta_t$ là tham số lịch trình nhiễu tại bước thời gian $t$.\n\n2. **Khuếch tán ngược**: Học cách dự đoán thành phần nhiễu để dần dần khử nhiễu hình ảnh:\n   $$p_\\theta(x_{t-1} | x_t) = \\mathcal{N}(x_{t-1}; \\mu_\\theta(x_t, t), \\Sigma_\\theta(x_t, t))$$\n   trong đó $\\mu_\\theta$ và $\\Sigma_\\theta$ được học bởi mạng nơ-ron.\n\n### Tạo có điều kiện\n\nMô hình kết hợp nhiều tín hiệu điều kiện:\n\n1. **Điều kiện văn bản**: Sử dụng nhúng văn bản CLIP thông qua cơ chế chú ý chéo:\n   $$\\text{Attention}(Q, K, V) = \\text{softmax}\\left(\\frac{QK^T}{\\sqrt{d}}\\right)V$$\n   trong đó $Q$ được lấy từ các đặc trưng UNet và $K, V$ từ nhúng văn bản.\n\n2. **Điều kiện không gian**: Sử dụng hình ảnh đen trắng làm hướng dẫn cấu trúc thông qua ControlNet.\n\n3. **Điều khiển vùng**: Triển khai khuếch tán có mặt nạ cho việc chỉnh sửa cục bộ:\n   $$x_{\\text{masked}} = \\text{mask} \\cdot x_{\\text{original}} + (1-\\text{mask}) \\cdot x_{\\text{edited}}$$\n\n### Hướng dẫn chú ý tự thân (SAG)\n\nMô hình sử dụng SAG để cải thiện chất lượng hình ảnh được tạo ra:\n$$x_{t-1} = x_{t-1} + \\lambda \\cdot (\\text{Attention}(x_t) - \\text{Attention}(x_t|c))$$\ntrong đó $\\lambda$ là tham số tỷ lệ SAG.\n\n## Chi tiết triển khai\n\n### Kiến trúc mô hình\n\n1. **ControlNet**\n   - Lấy hình ảnh đen trắng làm đầu vào\n   - Cung cấp điều kiện không gian thông qua kết nối bỏ qua\n   - Sửa đổi xương sống UNet để kết hợp tín hiệu điều khiển\n\n2. **UNet với cơ chế chú ý chéo**\n   - Xương sống cho mô hình khuếch tán\n   - Kết hợp nhúng văn bản thông qua cơ chế chú ý chéo\n   - Được sửa đổi để chấp nhận tín hiệu điều khiển bổ sung\n\n3. **VAE biến dạng**\n   - Kiến trúc mã hóa-giải mã với tích chập biến dạng\n   - Bảo toàn chi tiết cấu trúc từ đầu vào đen trắng\n   - Giảm chảy màu giữa các vùng\n\n### Tham số chính\n\n- **Bước khuếch tán**: Kiểm soát chất lượng và tốc độ tạo (mặc định: 20)\n- **Cường độ điều khiển**: Xác định mức độ ảnh hưởng của tín hiệu điều khiển đến đầu ra (mặc định: 1.0)\n- **Tỷ lệ hướng dẫn**: Kiểm soát sự tuân thủ với gợi ý văn bản (mặc định: 7.0)\n- **Tỷ lệ SAG**: Kiểm soát ảnh hưởng của hướng dẫn chú ý tự thân (mặc định: 0.05)\n\n## Giao diện tương tác\n\nHệ thống cung cấp giao diện người dùng dựa trên Gradio với các tính năng sau:\n\n1. **Điều khiển đầu vào**\n   - Tải lên hình ảnh đen trắng hoặc màu\n   - Vẽ các nét màu cho tô màu theo vùng\n   - Cung cấp gợi ý văn bản để hướng dẫn phong cách\n\n2. **Tùy chọn xử lý**\n   - Thay đổi theo màu của nét vẽ\n   - Chế độ chỉnh sửa lặp lại\n   - Bật/tắt VAE biến dạng để giảm tràn màu\n\n3. **Tham số nâng cao**\n   - Số lượng mẫu để tạo\n   - Độ phân giải hình ảnh\n   - Số bước khuếch tán\n   - Tỷ lệ hướng dẫn\n   - Kiểm soát hạt giống ngẫu nhiên\n\n## Quy trình xử lý dữ liệu\n\n1. **Xử lý đầu vào**\n   - Chuyển đổi hình ảnh màu sang không gian màu LAB\n   - Trích xuất kênh L cho biểu diễn đen trắng\n   - Xử lý nét vẽ của người dùng để tạo mặt nạ\n\n2. **Tạo mặt nạ**\n   - Tạo mặt nạ nhị phân từ nét vẽ của người dùng\n   - Áp dụng các phép toán hình thái học để tạo biên sạch\n   - Kết hợp mặt nạ với hình ảnh đầu vào\n\n3. **Quá trình khuếch tán**\n   - Mã hóa hình ảnh có mặt nạ vào không gian tiềm ẩn\n   - Áp dụng lấy mẫu khuếch tán có điều kiện\n   - Giải mã kết quả trở lại không gian điểm ảnh\n\n4. **Thao tác không gian màu**\n   - Kết hợp kênh L từ hình ảnh gốc với kênh a, b từ hình ảnh được tạo ra\n   - Chuyển đổi trở lại RGB cho đầu ra cuối cùng\n\n## Đổi mới kỹ thuật\n\n1. **VAE biến dạng được hướng dẫn bởi nội dung**\n   - Bảo toàn chi tiết cấu trúc trong khi cho phép tô màu linh hoạt\n   - Giảm chảy màu giữa các vùng\n\n2. **Điều khiển theo vùng**\n   - Cho phép kiểm soát chính xác trên các khu vực cụ thể\n   - Hỗ trợ chỉnh sửa lặp lại trong khi duy trì tính nhất quán\n\n3. **Điều kiện đa phương thức**\n   - Kết hợp gợi ý văn bản, nét vẽ của người dùng và hướng dẫn cấu trúc\n   - Cho phép các phong cách tô màu đa dạng\n\n## Cấu trúc mã nguồn\n\nMã nguồn được tổ chức thành một số mô-đun chính:\n\n1. **cldm/** - Chứa triển khai ControlNet và định nghĩa mô hình\n   - `cldm.py` - Định nghĩa các lớp ControlNet và ControlLDM\n   - `model.py` - Cung cấp tiện ích để tải mô hình và điểm kiểm tra\n   - `ddim_haced_sag_step.py` - Triển khai bộ lấy mẫu DDIM với SAG\n\n2. **ldm/** - Chứa các thành phần mô hình khuếch tán tiềm ẩn cốt lõi\n   - `models/diffusion/` - Triển khai các quá trình khuếch tán (DDPM, DDIM)\n   - `models/autoencoder.py` - Triển khai VAE tiêu chuẩn\n   - `models/autoencoder_train.py` - Triển khai VAE biến dạng\n   - `modules/diffusionmodules/` - Các khối xây dựng UNet và khuếch tán cốt lõi\n   - `modules/attention.py` - Cơ chế chú ý cho mô hình khuếch tán\n   - `modules/attention_dcn_control.py` - Chú ý tích chập biến dạng\n\n3. **taming/** - Chứa các thành phần từ kiến trúc VQGAN\n   - `modules/vqvae/` - Các thành phần lượng tử hóa vector\n   - `modules/discriminator/` - Các thành phần phân biệt GAN\n   - `modules/losses/` - Các hàm mất mát cho việc huấn luyện\n\n4. **test.py** - Điểm vào chính cho giao diện demo Gradio\n\n## Điểm nổi bật trong triển khai\n\n### Lấy mẫu khuếch tán với SAG\n\nBộ lấy mẫu DDIM được mở rộng với Self-Attention Guidance (SAG) để cải thiện chất lượng tạo. Triển khai trong `ddim_haced_sag_step.py` bao gồm một cách tiếp cận tinh vi cho SAG:\n\n```python\ndef p_sample_ddim(self, x, mask, masked_image_latents, c, t, index, repeat_noise=False, use_original_steps=False,\n                  quantize_denoised=False, temperature=1., noise_dropout=0., score_corrector=None,\n                  corrector_kwargs=None, unconditional_guidance_scale=1., sag_scale=0.75, sag_enable=True,\n                  noise=None, unconditional_conditioning=None, dynamic_threshold=None):\n    # Hướng dẫn phi phân loại tiêu chuẩn\n    if unconditional_conditioning is None or unconditional_guidance_scale == 1.:\n        model_output = self.model.apply_model(x, mask, masked_image_latents, t, c)\n    else:\n        model_t = self.model.apply_model(x, mask, masked_image_latents, t, c)\n        model_uncond = self.model.apply_model(x, mask, masked_image_latents, t, unconditional_conditioning)\n        model_output = model_uncond + unconditional_guidance_scale * (model_t - model_uncond)\n\n    # Triển khai SAG\n    if sag_enable == True:\n        # Trích xuất bản đồ chú ý từ mô hình\n        uncond_attn, cond_attn = self.model.model.diffusion_model.middle_block[1].transformer_blocks[0].attn1.attention_probs.chunk(2)\n\n        # Làm suy giảm tiềm ẩn dựa trên chú ý tự thân\n        map_size = self.model.model.diffusion_model.middle_block[1].map_size\n        degraded_latents = self.sag_masking(\n            pred_x0, model_output, x, uncond_attn, map_size, t, eps=noise\n        )\n\n        # Áp dụng mô hình cho tiềm ẩn đã suy giảm\n        if unconditional_conditioning is None or unconditional_guidance_scale == 1.:\n            degraded_model_output = self.model.apply_model(degraded_latents, mask, masked_image_latents, t, c)\n        else:\n            degraded_model_t = self.model.apply_model(degraded_latents, mask, masked_image_latents, t, c)\n            degraded_model_uncond = self.model.apply_model(degraded_latents, mask, masked_image_latents, t, unconditional_conditioning)\n            degraded_model_output = degraded_model_uncond + unconditional_guidance_scale * (degraded_model_t - degraded_model_uncond)\n\n        # Áp dụng hiệu chỉnh SAG\n        model_output += sag_scale * (model_output - degraded_model_output)\n```\n\nHàm `sag_masking` triển khai một phần quan trọng của phương pháp SAG:\n\n```python\ndef sag_masking(self, original_latents, model_output, x, attn_map, map_size, t, eps):\n    # Trích xuất kích thước bản đồ chú ý\n    bh, hw1, hw2 = attn_map.shape\n    b, latent_channel, latent_h, latent_w = original_latents.shape\n    h = 4  # kích thước đầu chú ý\n\n    # Định hình lại bản đồ chú ý\n    attn_map = attn_map.reshape(b, h, hw1, hw2)\n\n    # Tạo mặt nạ chú ý nơi tổng chú ý > 1.0\n    attn_mask = attn_map.mean(1, keepdim=False).sum(1, keepdim=False) > 1.0\n    attn_mask = (\n        attn_mask.reshape(b, map_size[0], map_size[1])\n        .unsqueeze(1)\n        .repeat(1, latent_channel, 1, 1)\n        .type(attn_map.dtype)\n    )\n\n    # Thay đổi kích thước mặt nạ để phù hợp với kích thước tiềm ẩn\n    attn_mask = F.interpolate(attn_mask, (latent_h, latent_w))\n\n    # Áp dụng làm mờ Gaussian để tạo tiềm ẩn suy giảm\n    degraded_latents = gaussian_blur_2d(original_latents, kernel_size=9, sigma=1.0)\n\n    # Kết hợp tiềm ẩn gốc và suy giảm dựa trên mặt nạ chú ý\n    degraded_latents = degraded_latents * attn_mask + original_latents * (1 - attn_mask)\n\n    return degraded_latents\n```\n\n### VAE biến dạng và tích chập biến dạng\n\nVAE biến dạng mở rộng VAE tiêu chuẩn với giải mã được hướng dẫn bởi nội dung, sử dụng hình ảnh đen trắng làm hướng dẫn cấu trúc:\n\n```python\ndef decode(self, z, gray_content_z):\n    z = self.post_quant_conv(z)\n    gray_content_z = self.post_quant_conv(gray_content_z)\n    dec = self.decoder(z, gray_content_z)\n    return dec\n```\n\nBộ giải mã sử dụng tích chập biến dạng để căn chỉnh màu sắc được tạo ra với nội dung cấu trúc từ hình ảnh đen trắng. Triển khai trong `attention_dcn_control.py` cho thấy cách thức hoạt động của các tích chập biến dạng này:\n\n```python\nclass ModulatedDeformConvPack(ModulatedDeformConv):\n    \"\"\"\n    Một đóng gói tích chập biến dạng có điều biến hoạt động như các lớp tích chập thông thường.\n    \"\"\"\n    def __init__(self, *args, **kwargs):\n        super(ModulatedDeformConvPack, self).__init__(*args, **kwargs)\n\n        self.conv_offset = nn.Conv2d(\n            self.in_channels,\n            self.deformable_groups * 3 * self.kernel_size[0] * self.kernel_size[1],\n            kernel_size=self.kernel_size,\n            stride=_pair(self.stride),\n            padding=_pair(self.padding),\n            dilation=_pair(self.dilation),\n            bias=True)\n        self.init_weights()\n\n    def init_weights(self):\n        super(ModulatedDeformConvPack, self).init_weights()\n        if hasattr(self, 'conv_offset'):\n            self.conv_offset.weight.data.zero_()\n            self.conv_offset.bias.data.zero_()\n\n    def forward(self, x):\n        # Tạo offset và mặt nạ\n        out = self.conv_offset(x)\n        o1, o2, mask = torch.chunk(out, 3, dim=1)\n        offset = torch.cat((o1, o2), dim=1)\n        mask = torch.sigmoid(mask)\n\n        # Áp dụng tích chập biến dạng\n        return torchvision.ops.deform_conv2d(x, offset, self.weight, self.bias, self.stride, self.padding,\n                                            self.dilation, mask)\n```\n\nTriển khai này cho phép các nhân tích chập được điều chỉnh động dựa trên nội dung đầu vào, cho phép mô hình căn chỉnh tốt hơn thông tin màu sắc với chi tiết cấu trúc. Các tích chập biến dạng được tích hợp vào các khối biến đổi không gian:\n\n```python\nclass SpatialTransformer_dcn(nn.Module):\n    def __init__(self, in_channels, n_heads, d_head, depth=1, dropout=0., context_dim=None,\n                 disable_self_attn=False, use_linear=False, use_checkpoint=True):\n        # ... mã khởi tạo ...\n\n        # Lớp tích chập biến dạng\n        self.dcn_cnn = ModulatedDeformConvPack(inner_dim,\n                                      inner_dim,\n                                      kernel_size=3,\n                                      stride=1,\n                                      padding=1)\n\n    def forward(self, x, context=None, dcn_guide=None):\n        # ... xử lý biến đổi ...\n\n        # Áp dụng tích chập biến dạng\n        x = self.dcn_cnn(x)\n\n        # ... xử lý cuối cùng ...\n        return x + x_in\n```\n\nKiến trúc này cho phép mô hình duy trì độ trung thực cấu trúc trong khi tạo ra các biến thể màu sắc đa dạng, giảm chảy màu giữa các vùng và bảo toàn các chi tiết tinh tế.\n\n### Triển khai ControlNet\n\nKiến trúc ControlNet được triển khai trong `cldm/cldm.py` và đóng vai trò là xương sống cho hướng dẫn cấu trúc trong quá trình tô màu:\n\n```python\nclass ControlNet(nn.Module):\n    def __init__(self, image_size, in_channels, model_channels, hint_channels, num_res_blocks,\n                 attention_resolutions, dropout=0, channel_mult=(1, 2, 4, 8), conv_resample=True,\n                 dims=2, use_checkpoint=False, use_fp16=False, num_heads=-1, num_head_channels=-1,\n                 num_heads_upsample=-1, use_scale_shift_norm=False, resblock_updown=False,\n                 use_new_attention_order=False, use_spatial_transformer=False, transformer_depth=1,\n                 context_dim=None, n_embed=None, legacy=True, disable_self_attentions=None,\n                 num_attention_blocks=None, disable_middle_self_attn=False, use_linear_in_transformer=False):\n        super().__init__()\n        # ... mã khởi tạo ...\n\n        # Xử lý đầu vào cho gợi ý (hình ảnh đen trắng)\n        self.input_hint_block = TimestepEmbedSequential(\n            conv_nd(dims, hint_channels, 16, 3, padding=1),\n            nn.SiLU(),\n            conv_nd(dims, 16, 16, 3, padding=1),\n            nn.SiLU(),\n            conv_nd(dims, 16, 32, 3, padding=1, stride=2),\n            nn.SiLU(),\n            conv_nd(dims, 32, 32, 3, padding=1),\n            nn.SiLU(),\n            conv_nd(dims, 32, 96, 3, padding=1, stride=2),\n            nn.SiLU(),\n            conv_nd(dims, 96, 96, 3, padding=1),\n            nn.SiLU(),\n            conv_nd(dims, 96, 256, 3, padding=1, stride=2),\n            nn.SiLU(),\n            zero_module(conv_nd(dims, 256, model_channels, 3, padding=1))\n        )\n\n        # ... các thành phần mạng khác ...\n\n    def forward(self, x, hint, timesteps, context, **kwargs):\n        # Xử lý gợi ý (hình ảnh đen trắng)\n        guided_hint = self.input_hint_block(hint, emb, context)\n\n        # Xử lý đầu vào qua mạng với gợi ý\n        outs = []\n        h = x.type(self.dtype)\n\n        for module, zero_conv in zip(self.input_blocks, self.zero_convs):\n            if guided_hint is not None:\n                h = module(h, emb, context)\n                h += guided_hint  # Thêm đặc trưng gợi ý\n                guided_hint = None\n            else:\n                h = module(h, emb, context)\n            outs.append(zero_conv(h, emb, context))\n\n        # Xử lý qua khối giữa\n        h = self.middle_block(h, emb, context)\n        outs.append(self.middle_block_out(h, emb, context))\n\n        return outs\n```\n\nControlNet được tích hợp với mô hình khuếch tán chính thông qua lớp `ControlLDM`:\n\n```python\nclass ControlLDM(LatentDiffusion):\n    def __init__(self, control_stage_config, control_key, only_mid_control, *args, **kwargs):\n        super().__init__(*args, **kwargs)\n        self.control_model = instantiate_from_config(control_stage_config)\n        self.control_key = control_key\n        self.only_mid_control = only_mid_control\n        self.control_scales = [1.0] * 13\n\n    def apply_model(self, x_noisy, mask, masked_image_latents, t, cond, *args, **kwargs):\n        assert isinstance(cond, dict)\n        diffusion_model = self.model.diffusion_model\n\n        cond_txt = torch.cat(cond['c_crossattn'], 1)\n\n        if cond['c_concat'] is None:\n            # Không có tín hiệu điều khiển\n            eps = diffusion_model(x=x_noisy, timesteps=t, context=cond_txt, control=None,\n                                 only_mid_control=self.only_mid_control)\n        else:\n            # Lấy tín hiệu điều khiển từ ControlNet\n            control = self.control_model(x=x_noisy, hint=torch.cat(cond['c_concat'], 1),\n                                        timesteps=t, context=cond_txt)\n\n            # Áp dụng tỷ lệ điều khiển\n            control = [c * scale for c, scale in zip(control, self.control_scales)]\n\n            # Chuẩn bị mặt nạ và tiềm ẩn hình ảnh có mặt nạ\n            mask = torch.cat([mask] * x_noisy.shape[0])\n            masked_image_latents = torch.cat([masked_image_latents] * x_noisy.shape[0])\n\n            # Nối các đầu vào cho mô hình khuếch tán\n            x_noisy = torch.cat([x_noisy, mask, masked_image_latents], dim=1)\n\n            # Áp dụng mô hình khuếch tán với điều khiển\n            eps = diffusion_model(x=x_noisy, timesteps=t, context=cond_txt, control=control,\n                                 only_mid_control=self.only_mid_control)\n\n        return eps\n```\n\nTriển khai này cho phép mô hình sử dụng hiệu quả hình ảnh đen trắng làm hướng dẫn cấu trúc cho quá trình tô màu, đảm bảo rằng màu sắc được tạo ra phù hợp với cấu trúc hình ảnh gốc.\n\n### Điều khiển theo vùng\n\nĐiều khiển theo vùng được triển khai thông qua việc tạo mặt nạ:\n\n```python\ndef get_mask(input_image, hint_image):\n    mask = input_image.copy()\n    H, W, C = input_image.shape\n    for i in range(H):\n        for j in range(W):\n            if input_image[i,j,0] == hint_image[i,j,0]:\n                mask[i,j,:] = 255.\n            else:\n                mask[i,j,:] = 0.\n    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3,3))\n    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)\n    return mask\n```\n\n## Kết luận\n\nCtrlColor đại diện cho một bước tiến đáng kể trong lĩnh vực tô màu hình ảnh tương tác bằng cách tận dụng các mô hình khuếch tán với nhiều cơ chế điều khiển. Hệ thống cung cấp giao diện người dùng thân thiện cho việc tô màu chất lượng cao, có thể kiểm soát với các ứng dụng trong phục hồi ảnh, sáng tạo nghệ thuật và sản xuất phương tiện truyền thông.\n\nNhững đổi mới kỹ thuật trong mã nguồn này, đặc biệt là VAE biến dạng được hướng dẫn bởi nội dung và các cơ chế điều khiển theo vùng, cho thấy cách các mô hình khuếch tán có thể được điều chỉnh cho các tác vụ chỉnh sửa tương tác với độ chính xác và chất lượng cao. Việc tích hợp nhiều tín hiệu điều kiện (văn bản, cấu trúc, nét vẽ của người dùng) cho phép một hệ thống tô màu linh hoạt và mạnh mẽ cân bằng giữa kiểm soát của người dùng và sáng tạo do AI tạo ra.\n\n## Không gian màu LAB và tô màu\n\nMột khía cạnh kỹ thuật quan trọng của CtrlColor là việc sử dụng không gian màu LAB cho tô màu:\n\n### Không gian màu LAB\n\nKhông gian màu LAB bao gồm ba kênh:\n- **L**: Độ sáng (0-100), đại diện cho độ sáng của hình ảnh\n- **a**: Trục Xanh lá-Đỏ (-128 đến +127)\n- **b**: Trục Xanh dương-Vàng (-128 đến +127)\n\nKhông gian màu này đặc biệt phù hợp cho các tác vụ tô màu vì:\n\n1. **Đồng nhất về nhận thức**: LAB được thiết kế để xấp xỉ thị giác con người, làm cho nó đồng nhất hơn về mặt nhận thức so với RGB.\n\n2. **Tách biệt độ sáng và màu sắc**: Kênh L chứa tất cả thông tin cấu trúc, trong khi kênh a và b chỉ chứa thông tin màu sắc.\n\n### Triển khai trong CtrlColor\n\nCtrlColor tận dụng sự tách biệt này bằng cách:\n\n1. **Bảo toàn cấu trúc**: Kênh L từ hình ảnh đen trắng gốc được giữ nguyên, đảm bảo độ trung thực cấu trúc.\n\n2. **Tạo kênh màu**: Mô hình khuếch tán tập trung vào việc tạo ra kênh a và b.\n\n3. **Kết hợp kênh**: Hình ảnh tô màu cuối cùng được tạo ra bằng cách kết hợp kênh L gốc với kênh a và b được tạo ra:\n\n```python\n# Chuyển đổi đầu vào sang không gian màu LAB\ninput_image = cv2.cvtColor(input_image, cv2.COLOR_RGB2LAB)[:,:,0]\ninput_image = cv2.merge([input_image, input_image, input_image])\n\n# Sau khi tạo kết quả, kết hợp kênh L với kênh a,b được tạo ra\nresults_tmp = [cv2.cvtColor(np.array(i), cv2.COLOR_RGB2LAB) for i in results_ori]\nresults = [cv2.merge([input_image[:,:,0], tmp[:,:,1], tmp[:,:,2]]) for tmp in results_tmp]\nresults_mergeL = [cv2.cvtColor(np.asarray(i), cv2.COLOR_LAB2RGB) for i in results]\n```\n\nCách tiếp cận này đảm bảo rằng các chi tiết cấu trúc của hình ảnh gốc được bảo toàn hoàn hảo trong khi cho phép tự do sáng tạo trong tô màu.\n"}