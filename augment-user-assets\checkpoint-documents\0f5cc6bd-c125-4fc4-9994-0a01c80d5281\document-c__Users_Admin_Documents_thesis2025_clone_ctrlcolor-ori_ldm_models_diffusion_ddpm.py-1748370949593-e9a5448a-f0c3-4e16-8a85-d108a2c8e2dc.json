{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\ldm\\models\\diffusion\\ddpm.py"}, "originalCode": "\"\"\"\r\nwild mixture of\r\nhttps://github.com/lucidrains/denoising-diffusion-pytorch/blob/7706bdfc6f527f58d33f84b7b522e61e6e3164b3/denoising_diffusion_pytorch/denoising_diffusion_pytorch.py\r\nhttps://github.com/openai/improved-diffusion/blob/e94489283bb876ac1477d5dd7709bbbd2d9902ce/improved_diffusion/gaussian_diffusion.py\r\nhttps://github.com/CompVis/taming-transformers\r\n-- merci\r\n\"\"\"\r\n\r\nimport itertools\r\nfrom contextlib import contextmanager, nullcontext\r\nfrom functools import partial\r\n\r\nimport numpy as np\r\nimport pytorch_lightning as pl\r\nimport torch\r\nimport torch.nn as nn\r\nfrom einops import rearrange, repeat\r\nfrom omegaconf import ListConfig\r\nfrom pytorch_lightning.utilities.distributed import rank_zero_only\r\nfrom torch.optim.lr_scheduler import LambdaLR\r\nfrom torchvision.utils import make_grid\r\nfrom tqdm import tqdm\r\n\r\nfrom ldm.models.autoencoder import AutoencoderKL, IdentityFirstStage\r\nfrom ldm.models.diffusion.ddim import DDIMSampler\r\nfrom ldm.modules.diffusionmodules.util import (\r\n    extract_into_tensor,\r\n    make_beta_schedule,\r\n    noise_like,\r\n)\r\nfrom ldm.modules.distributions.distributions import (\r\n    DiagonalGaussianDistribution,\r\n    normal_kl,\r\n)\r\nfrom ldm.modules.ema import LitEma\r\nfrom ldm.util import (\r\n    count_params,\r\n    default,\r\n    exists,\r\n    instantiate_from_config,\r\n    isimage,\r\n    ismap,\r\n    log_txt_as_img,\r\n    mean_flat,\r\n)\r\n\r\n__conditioning_keys__ = {\"concat\": \"c_concat\", \"crossattn\": \"c_crossattn\", \"adm\": \"y\"}\r\n\r\n\r\nclass Distance_Type:\r\n    \"\"\"Distance types for contextual loss computation.\"\"\"\r\n\r\n    L2_Distance = 0\r\n    L1_Distance = 1\r\n    Cosine_Distance = 2\r\n\r\n\r\nclass ContextualLoss(nn.Module):\r\n    \"\"\"\r\n    Sophisticated Contextual Loss implementation for CtrlColor.\r\n    Based on \"The Contextual Loss for Image Transformation with Non-Aligned Data\"\r\n\r\n    Configured for CtrlColor paper specifications:\r\n    - VGG19 layers 3 and 5 (conv_3_2, conv_5_2)\r\n    - Cosine distance\r\n    - h=0.01 bandwidth parameter\r\n    - Layer weights: w₃=2, w₅=8\r\n    \"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        layers_weights=None,\r\n        crop_quarter=False,\r\n        max_1d_size=100,\r\n        distance_type=Distance_Type.Cosine_Distance,\r\n        b=1.0,\r\n        h=0.01,\r\n    ):\r\n        super(ContextualLoss, self).__init__()\r\n\r\n        # Default to paper specifications if not provided\r\n        if layers_weights is None:\r\n            layers_weights = {\r\n                \"conv_3_2\": 2.0,  # φ³, w₃=2\r\n                \"conv_5_2\": 8.0,  # φ⁵, w₅=8\r\n            }\r\n\r\n        self.layers_weights = layers_weights\r\n        self.crop_quarter = crop_quarter\r\n        self.distance_type = distance_type\r\n        self.max_1d_size = max_1d_size\r\n        self.b = b\r\n        self.h = h  # Paper uses h=0.01\r\n\r\n        # VGG model will be set by instantiate_contextual_stage\r\n        self.vgg_model = None\r\n\r\n    def set_vgg_model(self, vgg_model):\r\n        \"\"\"Set the VGG model for feature extraction.\"\"\"\r\n        self.vgg_model = vgg_model\r\n\r\n    def forward(self, images, gt):\r\n        \"\"\"\r\n        Compute contextual loss between images and ground truth.\r\n\r\n        Args:\r\n            images: Input images tensor (B, C, H, W)\r\n            gt: Ground truth images tensor (B, C, H, W)\r\n\r\n        Returns:\r\n            Contextual loss value\r\n        \"\"\"\r\n        if self.vgg_model is None:\r\n            raise ValueError(\"VGG model not set. Call set_vgg_model() first.\")\r\n\r\n        device = images.device\r\n        loss = torch.zeros(1, device=device)\r\n\r\n        # Extract VGG features\r\n        vgg_images = self.vgg_model(images)\r\n        vgg_gt = self.vgg_model(gt)\r\n\r\n        # Compute contextual loss for each specified layer\r\n        for layer_name, weight in self.layers_weights.items():\r\n            if layer_name not in vgg_images or layer_name not in vgg_gt:\r\n                print(f\"Warning: Layer {layer_name} not found in VGG features\")\r\n                continue\r\n\r\n            feat_images = vgg_images[layer_name]\r\n            feat_gt = vgg_gt[layer_name]\r\n\r\n            N, C, H, W = feat_images.size()\r\n\r\n            # Apply random pooling if feature map is too large\r\n            if H * W > self.max_1d_size**2:\r\n                feat_images = self._random_pooling(\r\n                    feat_images, output_1d_size=self.max_1d_size\r\n                )\r\n                feat_gt = self._random_pooling(feat_gt, output_1d_size=self.max_1d_size)\r\n\r\n            # Compute contextual loss for this layer\r\n            layer_loss = self.calculate_CX_Loss(feat_images, feat_gt)\r\n            loss += layer_loss * weight\r\n\r\n        return loss\r\n\r\n    @staticmethod\r\n    def _random_sampling(tensor, n, indices):\r\n        \"\"\"Random sampling of tensor elements.\"\"\"\r\n        N, C, H, W = tensor.size()\r\n        S = H * W\r\n        tensor = tensor.view(N, C, S)\r\n        if indices is None:\r\n            indices = torch.randperm(S)[:n].contiguous().type_as(tensor).long()\r\n            indices = indices.view(1, 1, -1).expand(N, C, -1)\r\n        indices = ContextualLoss._move_to_current_device(indices)\r\n        res = torch.gather(tensor, index=indices, dim=-1)\r\n        return res, indices\r\n\r\n    @staticmethod\r\n    def _move_to_current_device(tensor):\r\n        \"\"\"Move tensor to current device.\"\"\"\r\n        if tensor.device.type == \"cuda\":\r\n            id = torch.cuda.current_device()\r\n            return tensor.cuda(id)\r\n        return tensor\r\n\r\n    @staticmethod\r\n    def _random_pooling(feats, output_1d_size=100):\r\n        \"\"\"Apply random pooling to reduce feature map size.\"\"\"\r\n        single_input = type(feats) is torch.Tensor\r\n\r\n        if single_input:\r\n            feats = [feats]\r\n\r\n        N, C, H, W = feats[0].size()\r\n        feats_sample, indices = ContextualLoss._random_sampling(\r\n            feats[0], output_1d_size**2, None\r\n        )\r\n        res = [feats_sample]\r\n\r\n        for i in range(1, len(feats)):\r\n            feats_sample, _ = ContextualLoss._random_sampling(feats[i], -1, indices)\r\n            res.append(feats_sample)\r\n\r\n        res = [\r\n            feats_sample.view(N, C, output_1d_size, output_1d_size)\r\n            for feats_sample in res\r\n        ]\r\n\r\n        if single_input:\r\n            return res[0]\r\n        return res\r\n\r\n    @staticmethod\r\n    def _create_using_L2(I_features, T_features):\r\n        \"\"\"Create distance matrix using L2 distance.\"\"\"\r\n        assert I_features.size() == T_features.size()\r\n        N, C, H, W = I_features.size()\r\n\r\n        Ivecs = I_features.view(N, C, -1)\r\n        Tvecs = T_features.view(N, C, -1)\r\n\r\n        square_I = torch.sum(Ivecs * Ivecs, dim=1, keepdim=False)\r\n        square_T = torch.sum(Tvecs * Tvecs, dim=1, keepdim=False)\r\n\r\n        raw_distance = []\r\n        for i in range(N):\r\n            Ivec, Tvec, s_I, s_T = (\r\n                Ivecs[i, ...],\r\n                Tvecs[i, ...],\r\n                square_I[i, ...],\r\n                square_T[i, ...],\r\n            )\r\n            AB = Ivec.permute(1, 0) @ Tvec\r\n            dist = s_I.view(-1, 1) + s_T.view(1, -1) - 2 * AB\r\n            raw_distance.append(dist.view(1, H, W, H * W))\r\n\r\n        raw_distance = torch.cat(raw_distance, dim=0)\r\n        raw_distance = torch.clamp(raw_distance, min=0.0)\r\n        return raw_distance\r\n\r\n    @staticmethod\r\n    def _create_using_L1(I_features, T_features):\r\n        \"\"\"Create distance matrix using L1 distance.\"\"\"\r\n        assert I_features.size() == T_features.size()\r\n        N, C, H, W = I_features.size()\r\n\r\n        Ivecs = I_features.view(N, C, -1)\r\n        Tvecs = T_features.view(N, C, -1)\r\n\r\n        raw_distance = []\r\n        for i in range(N):\r\n            Ivec, Tvec = Ivecs[i, ...], Tvecs[i, ...]\r\n            dist = torch.sum(\r\n                torch.abs(Ivec.view(C, -1, 1) - Tvec.view(C, 1, -1)),\r\n                dim=0,\r\n                keepdim=False,\r\n            )\r\n            raw_distance.append(dist.view(1, H, W, H * W))\r\n\r\n        raw_distance = torch.cat(raw_distance, dim=0)\r\n        return raw_distance\r\n\r\n    @staticmethod\r\n    def _centered_by_T(I, T):\r\n        \"\"\"Center features by T's mean.\"\"\"\r\n        mean_T = (\r\n            T.mean(dim=0, keepdim=True)\r\n            .mean(dim=2, keepdim=True)\r\n            .mean(dim=3, keepdim=True)\r\n        )\r\n        return I - mean_T, T - mean_T\r\n\r\n    @staticmethod\r\n    def _normalized_L2_channelwise(tensor):\r\n        \"\"\"Normalize tensor channelwise using L2 norm.\"\"\"\r\n        norms = tensor.norm(p=2, dim=1, keepdim=True)\r\n        return tensor / norms\r\n\r\n    @staticmethod\r\n    def _create_using_dotP(I_features, T_features):\r\n        \"\"\"Create distance matrix using cosine distance (dot product).\"\"\"\r\n        assert I_features.size() == T_features.size()\r\n        I_features, T_features = ContextualLoss._centered_by_T(I_features, T_features)\r\n        I_features = ContextualLoss._normalized_L2_channelwise(I_features)\r\n        T_features = ContextualLoss._normalized_L2_channelwise(T_features)\r\n\r\n        N, C, H, W = I_features.size()\r\n        cosine_dist = []\r\n        for i in range(N):\r\n            T_features_i = (\r\n                T_features[i].view(1, 1, C, H * W).permute(3, 2, 0, 1).contiguous()\r\n            )\r\n            I_features_i = I_features[i].unsqueeze(0)\r\n            dist = (\r\n                torch.nn.functional.conv2d(I_features_i, T_features_i)\r\n                .permute(0, 2, 3, 1)\r\n                .contiguous()\r\n            )\r\n            cosine_dist.append(dist)\r\n\r\n        cosine_dist = torch.cat(cosine_dist, dim=0)\r\n        cosine_dist = (1 - cosine_dist) / 2\r\n        cosine_dist = cosine_dist.clamp(min=0.0)\r\n        return cosine_dist\r\n\r\n    @staticmethod\r\n    def _calculate_relative_distance(raw_distance, epsilon=1e-5):\r\n        \"\"\"Normalize distances as in Eq. (2) of the paper.\"\"\"\r\n        div = torch.min(raw_distance, dim=-1, keepdim=True)[0]\r\n        relative_dist = raw_distance / (div + epsilon)\r\n        return relative_dist\r\n\r\n    def calculate_CX_Loss(self, I_features, T_features):\r\n        \"\"\"Calculate the contextual loss between I and T features.\"\"\"\r\n        I_features = ContextualLoss._move_to_current_device(I_features)\r\n        T_features = ContextualLoss._move_to_current_device(T_features)\r\n\r\n        # Check for NaN or Inf\r\n        if torch.sum(torch.isnan(I_features)) == torch.numel(I_features) or torch.sum(\r\n            torch.isinf(I_features)\r\n        ) == torch.numel(I_features):\r\n            raise ValueError(\"NaN or Inf in I_features\")\r\n        if torch.sum(torch.isnan(T_features)) == torch.numel(T_features) or torch.sum(\r\n            torch.isinf(T_features)\r\n        ) == torch.numel(T_features):\r\n            raise ValueError(\"NaN or Inf in T_features\")\r\n\r\n        # Compute raw distance based on distance type\r\n        if self.distance_type == Distance_Type.L1_Distance:\r\n            raw_distance = ContextualLoss._create_using_L1(I_features, T_features)\r\n        elif self.distance_type == Distance_Type.L2_Distance:\r\n            raw_distance = ContextualLoss._create_using_L2(I_features, T_features)\r\n        else:  # Cosine distance (paper default)\r\n            raw_distance = ContextualLoss._create_using_dotP(I_features, T_features)\r\n\r\n        # Calculate relative distance\r\n        relative_distance = ContextualLoss._calculate_relative_distance(raw_distance)\r\n        del raw_distance\r\n\r\n        # Apply exponential weighting\r\n        exp_distance = torch.exp((self.b - relative_distance) / self.h)\r\n        del relative_distance\r\n\r\n        # Calculate contextual similarity\r\n        contextual_sim = exp_distance / torch.sum(exp_distance, dim=-1, keepdim=True)\r\n        del exp_distance\r\n\r\n        # Calculate final loss\r\n        max_gt_sim = torch.max(torch.max(contextual_sim, dim=1)[0], dim=1)[0]\r\n        del contextual_sim\r\n        CS = torch.mean(max_gt_sim, dim=1)\r\n        CX_loss = torch.mean(-torch.log(CS))\r\n\r\n        if torch.isnan(CX_loss):\r\n            raise ValueError(\"NaN in computing CX_loss\")\r\n\r\n        return CX_loss\r\n\r\n\r\ndef disabled_train(self, mode=True):\r\n    \"\"\"Overwrite model.train with this function to make sure train/eval mode\r\n    does not change anymore.\"\"\"\r\n    return self\r\n\r\n\r\ndef uniform_on_device(r1, r2, shape, device):\r\n    return (r1 - r2) * torch.rand(*shape, device=device) + r2\r\n\r\n\r\ndef prepare_mask_latents(\r\n    mask,\r\n    masked_image,\r\n    batch_size,\r\n    height,\r\n    width,\r\n    dtype,\r\n    device,\r\n    generator,\r\n    do_classifier_free_guidance,\r\n    vae,\r\n    vae_scale_factor,\r\n):\r\n    # resize the mask to latents shape as we concatenate the mask to the latents\r\n    # we do that before converting to dtype to avoid breaking in case we're using cpu_offload\r\n    # and half precision\r\n    mask = torch.nn.functional.interpolate(\r\n        mask, size=(height // vae_scale_factor, width // vae_scale_factor)\r\n    )\r\n    mask = mask.to(device=device, dtype=dtype)\r\n\r\n    masked_image = masked_image.to(device=device, dtype=dtype)\r\n\r\n    # encode the mask image into latents space so we can concatenate it to the latents\r\n    if isinstance(generator, list):\r\n        masked_image_latents = [\r\n            vae.encode(masked_image[i : i + 1]).latent_dist.sample(\r\n                generator=generator[i]\r\n            )\r\n            for i in range(batch_size)\r\n        ]\r\n        masked_image_latents = torch.cat(masked_image_latents, dim=0)\r\n    else:\r\n        masked_image_latents = vae.encode(masked_image).latent_dist.sample(\r\n            generator=generator\r\n        )\r\n    masked_image_latents = vae.config.scaling_factor * masked_image_latents\r\n\r\n    # duplicate mask and masked_image_latents for each generation per prompt, using mps friendly method\r\n    if mask.shape[0] < batch_size:\r\n        if not batch_size % mask.shape[0] == 0:\r\n            raise ValueError(\r\n                \"The passed mask and the required batch size don't match. Masks are supposed to be duplicated to\"\r\n                f\" a total batch size of {batch_size}, but {mask.shape[0]} masks were passed. Make sure the number\"\r\n                \" of masks that you pass is divisible by the total requested batch size.\"\r\n            )\r\n        mask = mask.repeat(batch_size // mask.shape[0], 1, 1, 1)\r\n    if masked_image_latents.shape[0] < batch_size:\r\n        if not batch_size % masked_image_latents.shape[0] == 0:\r\n            raise ValueError(\r\n                \"The passed images and the required batch size don't match. Images are supposed to be duplicated\"\r\n                f\" to a total batch size of {batch_size}, but {masked_image_latents.shape[0]} images were passed.\"\r\n                \" Make sure the number of images that you pass is divisible by the total requested batch size.\"\r\n            )\r\n        masked_image_latents = masked_image_latents.repeat(\r\n            batch_size // masked_image_latents.shape[0], 1, 1, 1\r\n        )\r\n\r\n    mask = torch.cat([mask] * 2) if do_classifier_free_guidance else mask\r\n    masked_image_latents = (\r\n        torch.cat([masked_image_latents] * 2)\r\n        if do_classifier_free_guidance\r\n        else masked_image_latents\r\n    )\r\n\r\n    # aligning device to prevent device errors when concating it with the latent model input\r\n    masked_image_latents = masked_image_latents.to(device=device, dtype=dtype)\r\n    return mask, masked_image_latents\r\n\r\n\r\nclass DDPM(pl.LightningModule):\r\n    # classic DDPM with Gaussian diffusion, in image space\r\n    def __init__(\r\n        self,\r\n        unet_config,\r\n        timesteps=1000,\r\n        beta_schedule=\"linear\",\r\n        loss_type=\"l2\",\r\n        ckpt_path=None,\r\n        ignore_keys=[],\r\n        load_only_unet=False,\r\n        monitor=\"val/loss\",\r\n        use_ema=True,\r\n        first_stage_key=\"image\",\r\n        image_size=256,\r\n        channels=3,\r\n        log_every_t=100,\r\n        clip_denoised=True,\r\n        linear_start=1e-4,\r\n        linear_end=2e-2,\r\n        cosine_s=8e-3,\r\n        given_betas=None,\r\n        original_elbo_weight=0.0,\r\n        v_posterior=0.0,  # weight for choosing posterior variance as sigma = (1-v) * beta_tilde + v * beta\r\n        l_simple_weight=1.0,\r\n        conditioning_key=None,\r\n        parameterization=\"eps\",  # all assuming fixed variance schedules\r\n        scheduler_config=None,\r\n        use_positional_encodings=False,\r\n        learn_logvar=False,\r\n        logvar_init=0.0,\r\n        make_it_fit=False,\r\n        ucg_training=None,\r\n        reset_ema=False,\r\n        reset_num_ema_updates=False,\r\n    ):\r\n        super().__init__()\r\n        assert parameterization in [\"eps\", \"x0\", \"v\"], (\r\n            'currently only supporting \"eps\" and \"x0\" and \"v\"'\r\n        )\r\n        self.parameterization = parameterization\r\n        print(\r\n            f\"{self.__class__.__name__}: Running in {self.parameterization}-prediction mode\"\r\n        )\r\n        self.cond_stage_model = None\r\n        self.clip_denoised = clip_denoised\r\n        self.log_every_t = log_every_t\r\n        self.first_stage_key = first_stage_key\r\n        self.image_size = image_size  # try conv?\r\n        self.channels = channels\r\n        self.use_positional_encodings = use_positional_encodings\r\n        self.model = DiffusionWrapper(unet_config, conditioning_key)\r\n        count_params(self.model, verbose=True)\r\n        self.use_ema = use_ema\r\n        if self.use_ema:\r\n            self.model_ema = LitEma(self.model)\r\n            print(f\"Keeping EMAs of {len(list(self.model_ema.buffers()))}.\")\r\n\r\n        self.use_scheduler = scheduler_config is not None\r\n        if self.use_scheduler:\r\n            self.scheduler_config = scheduler_config\r\n\r\n        self.v_posterior = v_posterior\r\n        self.original_elbo_weight = original_elbo_weight\r\n        self.l_simple_weight = l_simple_weight\r\n\r\n        if monitor is not None:\r\n            self.monitor = monitor\r\n        self.make_it_fit = make_it_fit\r\n        if reset_ema:\r\n            assert exists(ckpt_path)\r\n        if ckpt_path is not None:\r\n            self.init_from_ckpt(\r\n                ckpt_path, ignore_keys=ignore_keys, only_model=load_only_unet\r\n            )\r\n            if reset_ema:\r\n                assert self.use_ema\r\n                print(\r\n                    \"Resetting ema to pure model weights. This is useful when restoring from an ema-only checkpoint.\"\r\n                )\r\n                self.model_ema = LitEma(self.model)\r\n        if reset_num_ema_updates:\r\n            print(\r\n                \" +++++++++++ WARNING: RESETTING NUM_EMA UPDATES TO ZERO +++++++++++ \"\r\n            )\r\n            assert self.use_ema\r\n            self.model_ema.reset_num_updates()\r\n\r\n        self.register_schedule(\r\n            given_betas=given_betas,\r\n            beta_schedule=beta_schedule,\r\n            timesteps=timesteps,\r\n            linear_start=linear_start,\r\n            linear_end=linear_end,\r\n            cosine_s=cosine_s,\r\n        )\r\n\r\n        self.loss_type = loss_type\r\n\r\n        self.learn_logvar = learn_logvar\r\n        logvar = torch.full(fill_value=logvar_init, size=(self.num_timesteps,))\r\n        if self.learn_logvar:\r\n            self.logvar = nn.Parameter(logvar, requires_grad=True)\r\n        else:\r\n            self.register_buffer(\"logvar\", logvar)\r\n\r\n        self.ucg_training = ucg_training or dict()\r\n        if self.ucg_training:\r\n            self.ucg_prng = np.random.RandomState()\r\n\r\n    def register_schedule(\r\n        self,\r\n        given_betas=None,\r\n        beta_schedule=\"linear\",\r\n        timesteps=1000,\r\n        linear_start=1e-4,\r\n        linear_end=2e-2,\r\n        cosine_s=8e-3,\r\n    ):\r\n        if exists(given_betas):\r\n            betas = given_betas\r\n        else:\r\n            betas = make_beta_schedule(\r\n                beta_schedule,\r\n                timesteps,\r\n                linear_start=linear_start,\r\n                linear_end=linear_end,\r\n                cosine_s=cosine_s,\r\n            )\r\n        alphas = 1.0 - betas\r\n        alphas_cumprod = np.cumprod(alphas, axis=0)\r\n        alphas_cumprod_prev = np.append(1.0, alphas_cumprod[:-1])\r\n\r\n        (timesteps,) = betas.shape\r\n        self.num_timesteps = int(timesteps)\r\n        self.linear_start = linear_start\r\n        self.linear_end = linear_end\r\n        assert alphas_cumprod.shape[0] == self.num_timesteps, (\r\n            \"alphas have to be defined for each timestep\"\r\n        )\r\n\r\n        to_torch = partial(torch.tensor, dtype=torch.float32)\r\n\r\n        self.register_buffer(\"betas\", to_torch(betas))\r\n        self.register_buffer(\"alphas_cumprod\", to_torch(alphas_cumprod))\r\n        self.register_buffer(\"alphas_cumprod_prev\", to_torch(alphas_cumprod_prev))\r\n\r\n        # calculations for diffusion q(x_t | x_{t-1}) and others\r\n        self.register_buffer(\"sqrt_alphas_cumprod\", to_torch(np.sqrt(alphas_cumprod)))\r\n        self.register_buffer(\r\n            \"sqrt_one_minus_alphas_cumprod\", to_torch(np.sqrt(1.0 - alphas_cumprod))\r\n        )\r\n        self.register_buffer(\r\n            \"log_one_minus_alphas_cumprod\", to_torch(np.log(1.0 - alphas_cumprod))\r\n        )\r\n        self.register_buffer(\r\n            \"sqrt_recip_alphas_cumprod\", to_torch(np.sqrt(1.0 / alphas_cumprod))\r\n        )\r\n        self.register_buffer(\r\n            \"sqrt_recipm1_alphas_cumprod\", to_torch(np.sqrt(1.0 / alphas_cumprod - 1))\r\n        )\r\n\r\n        # calculations for posterior q(x_{t-1} | x_t, x_0)\r\n        posterior_variance = (1 - self.v_posterior) * betas * (\r\n            1.0 - alphas_cumprod_prev\r\n        ) / (1.0 - alphas_cumprod) + self.v_posterior * betas\r\n        # above: equal to 1. / (1. / (1. - alpha_cumprod_tm1) + alpha_t / beta_t)\r\n        self.register_buffer(\"posterior_variance\", to_torch(posterior_variance))\r\n        # below: log calculation clipped because the posterior variance is 0 at the beginning of the diffusion chain\r\n        self.register_buffer(\r\n            \"posterior_log_variance_clipped\",\r\n            to_torch(np.log(np.maximum(posterior_variance, 1e-20))),\r\n        )\r\n        self.register_buffer(\r\n            \"posterior_mean_coef1\",\r\n            to_torch(betas * np.sqrt(alphas_cumprod_prev) / (1.0 - alphas_cumprod)),\r\n        )\r\n        self.register_buffer(\r\n            \"posterior_mean_coef2\",\r\n            to_torch(\r\n                (1.0 - alphas_cumprod_prev) * np.sqrt(alphas) / (1.0 - alphas_cumprod)\r\n            ),\r\n        )\r\n\r\n        if self.parameterization == \"eps\":\r\n            lvlb_weights = self.betas**2 / (\r\n                2\r\n                * self.posterior_variance\r\n                * to_torch(alphas)\r\n                * (1 - self.alphas_cumprod)\r\n            )\r\n        elif self.parameterization == \"x0\":\r\n            lvlb_weights = (\r\n                0.5\r\n                * np.sqrt(torch.Tensor(alphas_cumprod))\r\n                / (2.0 * 1 - torch.Tensor(alphas_cumprod))\r\n            )\r\n        elif self.parameterization == \"v\":\r\n            lvlb_weights = torch.ones_like(\r\n                self.betas**2\r\n                / (\r\n                    2\r\n                    * self.posterior_variance\r\n                    * to_torch(alphas)\r\n                    * (1 - self.alphas_cumprod)\r\n                )\r\n            )\r\n        else:\r\n            raise NotImplementedError(\"mu not supported\")\r\n        lvlb_weights[0] = lvlb_weights[1]\r\n        self.register_buffer(\"lvlb_weights\", lvlb_weights, persistent=False)\r\n        assert not torch.isnan(self.lvlb_weights).all()\r\n\r\n    @contextmanager\r\n    def ema_scope(self, context=None):\r\n        if self.use_ema:\r\n            self.model_ema.store(self.model.parameters())\r\n            self.model_ema.copy_to(self.model)\r\n            if context is not None:\r\n                print(f\"{context}: Switched to EMA weights\")\r\n        try:\r\n            yield None\r\n        finally:\r\n            if self.use_ema:\r\n                self.model_ema.restore(self.model.parameters())\r\n                if context is not None:\r\n                    print(f\"{context}: Restored training weights\")\r\n\r\n    @torch.no_grad()\r\n    def init_from_ckpt(self, path, ignore_keys=list(), only_model=False):\r\n        sd = torch.load(path, map_location=\"cpu\")\r\n        if \"state_dict\" in list(sd.keys()):\r\n            sd = sd[\"state_dict\"]\r\n        keys = list(sd.keys())\r\n        for k in keys:\r\n            for ik in ignore_keys:\r\n                if k.startswith(ik):\r\n                    print(\"Deleting key {} from state_dict.\".format(k))\r\n                    del sd[k]\r\n        if self.make_it_fit:\r\n            n_params = len(\r\n                [\r\n                    name\r\n                    for name, _ in itertools.chain(\r\n                        self.named_parameters(), self.named_buffers()\r\n                    )\r\n                ]\r\n            )\r\n            for name, param in tqdm(\r\n                itertools.chain(self.named_parameters(), self.named_buffers()),\r\n                desc=\"Fitting old weights to new weights\",\r\n                total=n_params,\r\n            ):\r\n                if name not in sd:\r\n                    continue\r\n                old_shape = sd[name].shape\r\n                new_shape = param.shape\r\n                assert len(old_shape) == len(new_shape)\r\n                if len(new_shape) > 2:\r\n                    # we only modify first two axes\r\n                    assert new_shape[2:] == old_shape[2:]\r\n                # assumes first axis corresponds to output dim\r\n                if not new_shape == old_shape:\r\n                    new_param = param.clone()\r\n                    old_param = sd[name]\r\n                    if len(new_shape) == 1:\r\n                        for i in range(new_param.shape[0]):\r\n                            new_param[i] = old_param[i % old_shape[0]]\r\n                    elif len(new_shape) >= 2:\r\n                        for i in range(new_param.shape[0]):\r\n                            for j in range(new_param.shape[1]):\r\n                                new_param[i, j] = old_param[\r\n                                    i % old_shape[0], j % old_shape[1]\r\n                                ]\r\n\r\n                        n_used_old = torch.ones(old_shape[1])\r\n                        for j in range(new_param.shape[1]):\r\n                            n_used_old[j % old_shape[1]] += 1\r\n                        n_used_new = torch.zeros(new_shape[1])\r\n                        for j in range(new_param.shape[1]):\r\n                            n_used_new[j] = n_used_old[j % old_shape[1]]\r\n\r\n                        n_used_new = n_used_new[None, :]\r\n                        while len(n_used_new.shape) < len(new_shape):\r\n                            n_used_new = n_used_new.unsqueeze(-1)\r\n                        new_param /= n_used_new\r\n\r\n                    sd[name] = new_param\r\n\r\n        missing, unexpected = (\r\n            self.load_state_dict(sd, strict=False)\r\n            if not only_model\r\n            else self.model.load_state_dict(sd, strict=False)\r\n        )\r\n        print(\r\n            f\"Restored from {path} with {len(missing)} missing and {len(unexpected)} unexpected keys\"\r\n        )\r\n        if len(missing) > 0:\r\n            print(f\"Missing Keys:\\n {missing}\")\r\n        if len(unexpected) > 0:\r\n            print(f\"\\nUnexpected Keys:\\n {unexpected}\")\r\n\r\n    def q_mean_variance(self, x_start, t):\r\n        \"\"\"\r\n        Get the distribution q(x_t | x_0).\r\n        :param x_start: the [N x C x ...] tensor of noiseless inputs.\r\n        :param t: the number of diffusion steps (minus 1). Here, 0 means one step.\r\n        :return: A tuple (mean, variance, log_variance), all of x_start's shape.\r\n        \"\"\"\r\n        mean = extract_into_tensor(self.sqrt_alphas_cumprod, t, x_start.shape) * x_start\r\n        variance = extract_into_tensor(1.0 - self.alphas_cumprod, t, x_start.shape)\r\n        log_variance = extract_into_tensor(\r\n            self.log_one_minus_alphas_cumprod, t, x_start.shape\r\n        )\r\n        return mean, variance, log_variance\r\n\r\n    def predict_start_from_noise(self, x_t, t, noise):\r\n        return (\r\n            extract_into_tensor(self.sqrt_recip_alphas_cumprod, t, x_t.shape) * x_t\r\n            - extract_into_tensor(self.sqrt_recipm1_alphas_cumprod, t, x_t.shape)\r\n            * noise\r\n        )\r\n\r\n    def predict_start_from_z_and_v(self, x_t, t, v):\r\n        # self.register_buffer('sqrt_alphas_cumprod', to_torch(np.sqrt(alphas_cumprod)))\r\n        # self.register_buffer('sqrt_one_minus_alphas_cumprod', to_torch(np.sqrt(1. - alphas_cumprod)))\r\n        return (\r\n            extract_into_tensor(self.sqrt_alphas_cumprod, t, x_t.shape) * x_t\r\n            - extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, x_t.shape) * v\r\n        )\r\n\r\n    # def get_x_t_from_start_and_t(self, start, t, v):\r\n    #     return (\r\n    #             (start+extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, start.shape) * v)/extract_into_tensor(self.sqrt_alphas_cumprod, t, start.shape)\r\n    #     )\r\n\r\n    def predict_eps_from_z_and_v(self, x_t, t, v):\r\n        return (\r\n            extract_into_tensor(self.sqrt_alphas_cumprod, t, x_t.shape) * v\r\n            + extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, x_t.shape)\r\n            * x_t\r\n        )\r\n\r\n    def q_posterior(self, x_start, x_t, t):\r\n        posterior_mean = (\r\n            extract_into_tensor(self.posterior_mean_coef1, t, x_t.shape) * x_start\r\n            + extract_into_tensor(self.posterior_mean_coef2, t, x_t.shape) * x_t\r\n        )\r\n        posterior_variance = extract_into_tensor(self.posterior_variance, t, x_t.shape)\r\n        posterior_log_variance_clipped = extract_into_tensor(\r\n            self.posterior_log_variance_clipped, t, x_t.shape\r\n        )\r\n        return posterior_mean, posterior_variance, posterior_log_variance_clipped\r\n\r\n    def p_mean_variance(self, x, t, clip_denoised: bool):\r\n        model_out = self.model(x, t)\r\n        if self.parameterization == \"eps\":\r\n            x_recon = self.predict_start_from_noise(x, t=t, noise=model_out)\r\n        elif self.parameterization == \"x0\":\r\n            x_recon = model_out\r\n        if clip_denoised:\r\n            x_recon.clamp_(-1.0, 1.0)\r\n\r\n        model_mean, posterior_variance, posterior_log_variance = self.q_posterior(\r\n            x_start=x_recon, x_t=x, t=t\r\n        )\r\n        return model_mean, posterior_variance, posterior_log_variance\r\n\r\n    @torch.no_grad()\r\n    def p_sample(self, x, t, clip_denoised=True, repeat_noise=False):\r\n        b, *_, device = *x.shape, x.device\r\n        model_mean, _, model_log_variance = self.p_mean_variance(\r\n            x=x, t=t, clip_denoised=clip_denoised\r\n        )\r\n        noise = noise_like(x.shape, device, repeat_noise)\r\n        # no noise when t == 0\r\n        nonzero_mask = (1 - (t == 0).float()).reshape(b, *((1,) * (len(x.shape) - 1)))\r\n        return model_mean + nonzero_mask * (0.5 * model_log_variance).exp() * noise\r\n\r\n    @torch.no_grad()\r\n    def p_sample_loop(self, shape, return_intermediates=False):\r\n        device = self.betas.device\r\n        b = shape[0]\r\n        img = torch.randn(shape, device=device)\r\n        intermediates = [img]\r\n        for i in tqdm(\r\n            reversed(range(0, self.num_timesteps)),\r\n            desc=\"Sampling t\",\r\n            total=self.num_timesteps,\r\n        ):\r\n            img = self.p_sample(\r\n                img,\r\n                torch.full((b,), i, device=device, dtype=torch.long),\r\n                clip_denoised=self.clip_denoised,\r\n            )\r\n            if i % self.log_every_t == 0 or i == self.num_timesteps - 1:\r\n                intermediates.append(img)\r\n        if return_intermediates:\r\n            return img, intermediates\r\n        return img\r\n\r\n    @torch.no_grad()\r\n    def sample(self, batch_size=16, return_intermediates=False):\r\n        image_size = self.image_size\r\n        channels = self.channels\r\n        return self.p_sample_loop(\r\n            (batch_size, channels, image_size, image_size),\r\n            return_intermediates=return_intermediates,\r\n        )\r\n\r\n    def q_sample(self, x_start, t, noise=None):\r\n        noise = default(noise, lambda: torch.randn_like(x_start))\r\n        return (\r\n            extract_into_tensor(self.sqrt_alphas_cumprod, t, x_start.shape) * x_start\r\n            + extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, x_start.shape)\r\n            * noise\r\n        )\r\n\r\n    def get_v(self, x, noise, t):\r\n        return (\r\n            extract_into_tensor(self.sqrt_alphas_cumprod, t, x.shape) * noise\r\n            - extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, x.shape) * x\r\n        )\r\n\r\n    def get_loss(self, pred, target, mean=True):\r\n        if self.loss_type == \"l1\":\r\n            loss = (target - pred).abs()\r\n            if mean:\r\n                loss = loss.mean()\r\n        elif self.loss_type == \"l2\":\r\n            if mean:\r\n                loss = torch.nn.functional.mse_loss(target, pred)\r\n            else:\r\n                loss = torch.nn.functional.mse_loss(target, pred, reduction=\"none\")\r\n        else:\r\n            raise NotImplementedError(\"unknown loss type '{loss_type}'\")\r\n\r\n        return loss\r\n\r\n    def p_losses(self, x_start, t, noise=None):\r\n        noise = default(noise, lambda: torch.randn_like(x_start))\r\n        x_noisy = self.q_sample(x_start=x_start, t=t, noise=noise)\r\n        model_out = self.model(x_noisy, t)\r\n\r\n        loss_dict = {}\r\n        if self.parameterization == \"eps\":\r\n            target = noise\r\n        elif self.parameterization == \"x0\":\r\n            target = x_start\r\n        elif self.parameterization == \"v\":\r\n            target = self.get_v(x_start, noise, t)\r\n        else:\r\n            raise NotImplementedError(\r\n                f\"Parameterization {self.parameterization} not yet supported\"\r\n            )\r\n\r\n        loss = self.get_loss(model_out, target, mean=False).mean(dim=[1, 2, 3])\r\n\r\n        log_prefix = \"train\" if self.training else \"val\"\r\n\r\n        loss_dict.update({f\"{log_prefix}/loss_simple\": loss.mean()})\r\n        loss_simple = loss.mean() * self.l_simple_weight\r\n\r\n        loss_vlb = (self.lvlb_weights[t] * loss).mean()\r\n        loss_dict.update({f\"{log_prefix}/loss_vlb\": loss_vlb})\r\n\r\n        loss = loss_simple + self.original_elbo_weight * loss_vlb\r\n\r\n        loss_dict.update({f\"{log_prefix}/loss\": loss})\r\n\r\n        return loss, loss_dict\r\n\r\n    def forward(self, x, *args, **kwargs):\r\n        # b, c, h, w, device, img_size, = *x.shape, x.device, self.image_size\r\n        # assert h == img_size and w == img_size, f'height and width of image must be {img_size}'\r\n        t = torch.randint(\r\n            0, self.num_timesteps, (x.shape[0],), device=self.device\r\n        ).long()\r\n        return self.p_losses(x, t, *args, **kwargs)\r\n\r\n    def get_input(self, batch, k):\r\n        x = batch[k]\r\n        if len(x.shape) == 3:\r\n            x = x[..., None]\r\n        x = rearrange(x, \"b h w c -> b c h w\")\r\n        x = x.to(memory_format=torch.contiguous_format).float()\r\n        return x\r\n\r\n    def shared_step(self, batch):\r\n        x = self.get_input(batch, self.first_stage_key)\r\n        loss, loss_dict = self(x)\r\n        return loss, loss_dict\r\n\r\n    def training_step(self, batch, batch_idx):\r\n        for k in self.ucg_training:\r\n            p = self.ucg_training[k][\"p\"]\r\n            val = self.ucg_training[k][\"val\"]\r\n            if val is None:\r\n                val = \"\"\r\n            for i in range(len(batch[k])):\r\n                if self.ucg_prng.choice(2, p=[1 - p, p]):\r\n                    batch[k][i] = val\r\n\r\n        loss, loss_dict = self.shared_step(batch)\r\n\r\n        self.log_dict(\r\n            loss_dict, prog_bar=True, logger=True, on_step=True, on_epoch=True\r\n        )\r\n\r\n        self.log(\r\n            \"global_step\",\r\n            self.global_step,\r\n            prog_bar=True,\r\n            logger=True,\r\n            on_step=True,\r\n            on_epoch=False,\r\n        )\r\n\r\n        if self.use_scheduler:\r\n            lr = self.optimizers().param_groups[0][\"lr\"]\r\n            self.log(\r\n                \"lr_abs\", lr, prog_bar=True, logger=True, on_step=True, on_epoch=False\r\n            )\r\n\r\n        return loss\r\n\r\n    @torch.no_grad()\r\n    def validation_step(self, batch, batch_idx):\r\n        _, loss_dict_no_ema = self.shared_step(batch)\r\n        with self.ema_scope():\r\n            _, loss_dict_ema = self.shared_step(batch)\r\n            loss_dict_ema = {key + \"_ema\": loss_dict_ema[key] for key in loss_dict_ema}\r\n        self.log_dict(\r\n            loss_dict_no_ema, prog_bar=False, logger=True, on_step=False, on_epoch=True\r\n        )\r\n        self.log_dict(\r\n            loss_dict_ema, prog_bar=False, logger=True, on_step=False, on_epoch=True\r\n        )\r\n\r\n    def on_train_batch_end(self, *args, **kwargs):\r\n        if self.use_ema:\r\n            self.model_ema(self.model)\r\n\r\n    def _get_rows_from_list(self, samples):\r\n        n_imgs_per_row = len(samples)\r\n        denoise_grid = rearrange(samples, \"n b c h w -> b n c h w\")\r\n        denoise_grid = rearrange(denoise_grid, \"b n c h w -> (b n) c h w\")\r\n        denoise_grid = make_grid(denoise_grid, nrow=n_imgs_per_row)\r\n        return denoise_grid\r\n\r\n    @torch.no_grad()\r\n    def log_images(self, batch, N=8, n_row=2, sample=True, return_keys=None, **kwargs):\r\n        log = dict()\r\n        x = self.get_input(batch, self.first_stage_key)\r\n        N = min(x.shape[0], N)\r\n        n_row = min(x.shape[0], n_row)\r\n        x = x.to(self.device)[:N]\r\n        log[\"inputs\"] = x\r\n\r\n        # get diffusion row\r\n        diffusion_row = list()\r\n        x_start = x[:n_row]\r\n\r\n        for t in range(self.num_timesteps):\r\n            if t % self.log_every_t == 0 or t == self.num_timesteps - 1:\r\n                t = repeat(torch.tensor([t]), \"1 -> b\", b=n_row)\r\n                t = t.to(self.device).long()\r\n                noise = torch.randn_like(x_start)\r\n                x_noisy = self.q_sample(x_start=x_start, t=t, noise=noise)\r\n                diffusion_row.append(x_noisy)\r\n\r\n        log[\"diffusion_row\"] = self._get_rows_from_list(diffusion_row)\r\n\r\n        if sample:\r\n            # get denoise row\r\n            with self.ema_scope(\"Plotting\"):\r\n                samples, denoise_row = self.sample(\r\n                    batch_size=N, return_intermediates=True\r\n                )\r\n\r\n            log[\"samples\"] = samples\r\n            log[\"denoise_row\"] = self._get_rows_from_list(denoise_row)\r\n\r\n        if return_keys:\r\n            if np.intersect1d(list(log.keys()), return_keys).shape[0] == 0:\r\n                return log\r\n            else:\r\n                return {key: log[key] for key in return_keys}\r\n        return log\r\n\r\n    def configure_optimizers(self):\r\n        lr = self.learning_rate\r\n        params = list(self.model.parameters())\r\n        if self.learn_logvar:\r\n            params = params + [self.logvar]\r\n        opt = torch.optim.AdamW(params, lr=lr)\r\n        return opt\r\n\r\n\r\nclass LatentDiffusion(DDPM):\r\n    \"\"\"main class\"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        first_stage_config,\r\n        cond_stage_config,\r\n        contextual_stage_config,\r\n        num_timesteps_cond=None,\r\n        cond_stage_key=\"image\",\r\n        cond_stage_trainable=False,\r\n        concat_mode=True,\r\n        cond_stage_forward=None,\r\n        conditioning_key=None,\r\n        scale_factor=1.0,\r\n        scale_by_std=False,\r\n        force_null_conditioning=False,\r\n        masked_image=None,\r\n        mask=None,\r\n        load_loss=False,\r\n        *args,\r\n        **kwargs,\r\n    ):\r\n        self.masked_image = masked_image\r\n        self.mask = mask\r\n        self.load_loss = load_loss\r\n        self.force_null_conditioning = force_null_conditioning\r\n        self.num_timesteps_cond = default(num_timesteps_cond, 1)\r\n        self.scale_by_std = scale_by_std\r\n        assert self.num_timesteps_cond <= kwargs[\"timesteps\"]\r\n        # for backwards compatibility after implementation of DiffusionWrapper\r\n        if conditioning_key is None:\r\n            conditioning_key = \"concat\" if concat_mode else \"crossattn\"\r\n        if (\r\n            cond_stage_config == \"__is_unconditional__\"\r\n            and not self.force_null_conditioning\r\n        ):\r\n            conditioning_key = None\r\n        ckpt_path = kwargs.pop(\"ckpt_path\", None)\r\n        reset_ema = kwargs.pop(\"reset_ema\", False)\r\n        reset_num_ema_updates = kwargs.pop(\"reset_num_ema_updates\", False)\r\n        ignore_keys = kwargs.pop(\"ignore_keys\", [])\r\n        # print(conditioning_key)\r\n        super().__init__(conditioning_key=conditioning_key, *args, **kwargs)\r\n        self.concat_mode = concat_mode\r\n        self.cond_stage_trainable = cond_stage_trainable\r\n        self.cond_stage_key = cond_stage_key\r\n        try:\r\n            self.num_downs = len(first_stage_config.params.ddconfig.ch_mult) - 1\r\n        except (AttributeError, KeyError):\r\n            self.num_downs = 0\r\n        if not scale_by_std:\r\n            self.scale_factor = scale_factor\r\n        else:\r\n            self.register_buffer(\"scale_factor\", torch.tensor(scale_factor))\r\n        self.instantiate_first_stage(first_stage_config)\r\n        self.instantiate_cond_stage(cond_stage_config)\r\n        self.instantiate_contextual_stage(contextual_stage_config)\r\n        self.cond_stage_forward = cond_stage_forward\r\n        self.clip_denoised = False\r\n        self.bbox_tokenizer = None\r\n\r\n        self.restarted_from_ckpt = False\r\n        if ckpt_path is not None:\r\n            self.init_from_ckpt(ckpt_path, ignore_keys)\r\n            self.restarted_from_ckpt = True\r\n            if reset_ema:\r\n                assert self.use_ema\r\n                print(\r\n                    \"Resetting ema to pure model weights. This is useful when restoring from an ema-only checkpoint.\"\r\n                )\r\n                self.model_ema = LitEma(self.model)\r\n        if reset_num_ema_updates:\r\n            print(\r\n                \" +++++++++++ WARNING: RESETTING NUM_EMA UPDATES TO ZERO +++++++++++ \"\r\n            )\r\n            assert self.use_ema\r\n            self.model_ema.reset_num_updates()\r\n\r\n    def make_cond_schedule(\r\n        self,\r\n    ):\r\n        self.cond_ids = torch.full(\r\n            size=(self.num_timesteps,),\r\n            fill_value=self.num_timesteps - 1,\r\n            dtype=torch.long,\r\n        )\r\n        ids = torch.round(\r\n            torch.linspace(0, self.num_timesteps - 1, self.num_timesteps_cond)\r\n        ).long()\r\n        self.cond_ids[: self.num_timesteps_cond] = ids\r\n\r\n    @rank_zero_only\r\n    @torch.no_grad()\r\n    def on_train_batch_start(self, batch, batch_idx, dataloader_idx):\r\n        # only for very first batch\r\n        if (\r\n            self.scale_by_std\r\n            and self.current_epoch == 0\r\n            and self.global_step == 0\r\n            and batch_idx == 0\r\n            and not self.restarted_from_ckpt\r\n        ):\r\n            assert self.scale_factor == 1.0, (\r\n                \"rather not use custom rescaling and std-rescaling simultaneously\"\r\n            )\r\n            # set rescale weight to 1./std of encodings\r\n            print(\"### USING STD-RESCALING ###\")\r\n            x = super().get_input(batch, self.first_stage_key)\r\n            x = x.to(self.device)\r\n            encoder_posterior = self.encode_first_stage(x)\r\n            z = self.get_first_stage_encoding(encoder_posterior).detach()\r\n            del self.scale_factor\r\n            self.register_buffer(\"scale_factor\", 1.0 / z.flatten().std())\r\n            print(f\"setting self.scale_factor to {self.scale_factor}\")\r\n            print(\"### USING STD-RESCALING ###\")\r\n\r\n    def register_schedule(\r\n        self,\r\n        given_betas=None,\r\n        beta_schedule=\"linear\",\r\n        timesteps=1000,\r\n        linear_start=1e-4,\r\n        linear_end=2e-2,\r\n        cosine_s=8e-3,\r\n    ):\r\n        super().register_schedule(\r\n            given_betas, beta_schedule, timesteps, linear_start, linear_end, cosine_s\r\n        )\r\n\r\n        self.shorten_cond_schedule = self.num_timesteps_cond > 1\r\n        if self.shorten_cond_schedule:\r\n            self.make_cond_schedule()\r\n\r\n    def instantiate_first_stage(self, config):\r\n        model = instantiate_from_config(config)\r\n        self.first_stage_model = model.eval()\r\n        self.first_stage_model.train = disabled_train\r\n        for param in self.first_stage_model.parameters():\r\n            param.requires_grad = False\r\n\r\n    def instantiate_contextual_stage(self, config):\r\n        if self.load_loss:\r\n            model = instantiate_from_config(config)\r\n            model.load_state_dict(\r\n                torch.load(\"/mnt/lustre/zxliang/zcli/data/vgg19_conv.pth\"), strict=False\r\n            )\r\n            print(\"vgg loaded\")\r\n            self.contextual_stage_model = model.eval()\r\n            for param in self.contextual_stage_model.parameters():\r\n                param.requires_grad = False\r\n            self.contextual_loss = ContextualLoss().to(self.device)\r\n        elif not self.load_loss:\r\n            self.contextual_stage_model = None\r\n            self.contextual_loss = None\r\n        else:\r\n            print(\"ERROR!!!!!self.load_loss should be either True or False!!!\")\r\n\r\n    def instantiate_cond_stage(self, config):\r\n        if not self.cond_stage_trainable:\r\n            if config == \"__is_first_stage__\":\r\n                print(\"Using first stage also as cond stage.\")\r\n                self.cond_stage_model = self.first_stage_model\r\n            elif config == \"__is_unconditional__\":\r\n                print(f\"Training {self.__class__.__name__} as an unconditional model.\")\r\n                self.cond_stage_model = None\r\n                # self.be_unconditional = True\r\n            else:\r\n                model = instantiate_from_config(config)\r\n                self.cond_stage_model = model.eval()\r\n                self.cond_stage_model.train = disabled_train\r\n                for param in self.cond_stage_model.parameters():\r\n                    param.requires_grad = False\r\n        else:\r\n            assert config != \"__is_first_stage__\"\r\n            assert config != \"__is_unconditional__\"\r\n            model = instantiate_from_config(config)\r\n            self.cond_stage_model = model\r\n\r\n    def _get_denoise_row_from_list(\r\n        self, samples, desc=\"\", force_no_decoder_quantization=False\r\n    ):\r\n        denoise_row = []\r\n        for zd in tqdm(samples, desc=desc):\r\n            denoise_row.append(\r\n                self.decode_first_stage(\r\n                    zd.to(self.device), force_not_quantize=force_no_decoder_quantization\r\n                )\r\n            )\r\n        n_imgs_per_row = len(denoise_row)\r\n        denoise_row = torch.stack(denoise_row)  # n_log_step, n_row, C, H, W\r\n        denoise_grid = rearrange(denoise_row, \"n b c h w -> b n c h w\")\r\n        denoise_grid = rearrange(denoise_grid, \"b n c h w -> (b n) c h w\")\r\n        denoise_grid = make_grid(denoise_grid, nrow=n_imgs_per_row)\r\n        return denoise_grid\r\n\r\n    def get_first_stage_encoding(self, encoder_posterior):\r\n        if isinstance(encoder_posterior, DiagonalGaussianDistribution):\r\n            z = encoder_posterior.sample()\r\n        elif isinstance(encoder_posterior, torch.Tensor):\r\n            z = encoder_posterior\r\n        else:\r\n            raise NotImplementedError(\r\n                f\"encoder_posterior of type '{type(encoder_posterior)}' not yet implemented\"\r\n            )\r\n        return self.scale_factor * z\r\n\r\n    def get_learned_conditioning(self, c):\r\n        if self.cond_stage_forward is None:\r\n            if hasattr(self.cond_stage_model, \"encode\") and callable(\r\n                self.cond_stage_model.encode\r\n            ):\r\n                c = self.cond_stage_model.encode(c)\r\n                if isinstance(c, DiagonalGaussianDistribution):\r\n                    c = c.mode()\r\n            else:\r\n                c = self.cond_stage_model(c)\r\n        else:\r\n            assert hasattr(self.cond_stage_model, self.cond_stage_forward)\r\n            c = getattr(self.cond_stage_model, self.cond_stage_forward)(c)\r\n        return c\r\n\r\n    def meshgrid(self, h, w):\r\n        y = torch.arange(0, h).view(h, 1, 1).repeat(1, w, 1)\r\n        x = torch.arange(0, w).view(1, w, 1).repeat(h, 1, 1)\r\n\r\n        arr = torch.cat([y, x], dim=-1)\r\n        return arr\r\n\r\n    def delta_border(self, h, w):\r\n        \"\"\"\r\n        :param h: height\r\n        :param w: width\r\n        :return: normalized distance to image border,\r\n         wtith min distance = 0 at border and max dist = 0.5 at image center\r\n        \"\"\"\r\n        lower_right_corner = torch.tensor([h - 1, w - 1]).view(1, 1, 2)\r\n        arr = self.meshgrid(h, w) / lower_right_corner\r\n        dist_left_up = torch.min(arr, dim=-1, keepdims=True)[0]\r\n        dist_right_down = torch.min(1 - arr, dim=-1, keepdims=True)[0]\r\n        edge_dist = torch.min(\r\n            torch.cat([dist_left_up, dist_right_down], dim=-1), dim=-1\r\n        )[0]\r\n        return edge_dist\r\n\r\n    def get_weighting(self, h, w, Ly, Lx, device):\r\n        weighting = self.delta_border(h, w)\r\n        weighting = torch.clip(\r\n            weighting,\r\n            self.split_input_params[\"clip_min_weight\"],\r\n            self.split_input_params[\"clip_max_weight\"],\r\n        )\r\n        weighting = weighting.view(1, h * w, 1).repeat(1, 1, Ly * Lx).to(device)\r\n\r\n        if self.split_input_params[\"tie_braker\"]:\r\n            L_weighting = self.delta_border(Ly, Lx)\r\n            L_weighting = torch.clip(\r\n                L_weighting,\r\n                self.split_input_params[\"clip_min_tie_weight\"],\r\n                self.split_input_params[\"clip_max_tie_weight\"],\r\n            )\r\n\r\n            L_weighting = L_weighting.view(1, 1, Ly * Lx).to(device)\r\n            weighting = weighting * L_weighting\r\n        return weighting\r\n\r\n    def get_fold_unfold(\r\n        self, x, kernel_size, stride, uf=1, df=1\r\n    ):  # todo load once not every time, shorten code\r\n        \"\"\"\r\n        :param x: img of size (bs, c, h, w)\r\n        :return: n img crops of size (n, bs, c, kernel_size[0], kernel_size[1])\r\n        \"\"\"\r\n        bs, nc, h, w = x.shape\r\n\r\n        # number of crops in image\r\n        Ly = (h - kernel_size[0]) // stride[0] + 1\r\n        Lx = (w - kernel_size[1]) // stride[1] + 1\r\n\r\n        if uf == 1 and df == 1:\r\n            fold_params = dict(\r\n                kernel_size=kernel_size, dilation=1, padding=0, stride=stride\r\n            )\r\n            unfold = torch.nn.Unfold(**fold_params)\r\n\r\n            fold = torch.nn.Fold(output_size=x.shape[2:], **fold_params)\r\n\r\n            weighting = self.get_weighting(\r\n                kernel_size[0], kernel_size[1], Ly, Lx, x.device\r\n            ).to(x.dtype)\r\n            normalization = fold(weighting).view(1, 1, h, w)  # normalizes the overlap\r\n            weighting = weighting.view((1, 1, kernel_size[0], kernel_size[1], Ly * Lx))\r\n\r\n        elif uf > 1 and df == 1:\r\n            fold_params = dict(\r\n                kernel_size=kernel_size, dilation=1, padding=0, stride=stride\r\n            )\r\n            unfold = torch.nn.Unfold(**fold_params)\r\n\r\n            fold_params2 = dict(\r\n                kernel_size=(kernel_size[0] * uf, kernel_size[0] * uf),\r\n                dilation=1,\r\n                padding=0,\r\n                stride=(stride[0] * uf, stride[1] * uf),\r\n            )\r\n            fold = torch.nn.Fold(\r\n                output_size=(x.shape[2] * uf, x.shape[3] * uf), **fold_params2\r\n            )\r\n\r\n            weighting = self.get_weighting(\r\n                kernel_size[0] * uf, kernel_size[1] * uf, Ly, Lx, x.device\r\n            ).to(x.dtype)\r\n            normalization = fold(weighting).view(\r\n                1, 1, h * uf, w * uf\r\n            )  # normalizes the overlap\r\n            weighting = weighting.view(\r\n                (1, 1, kernel_size[0] * uf, kernel_size[1] * uf, Ly * Lx)\r\n            )\r\n\r\n        elif df > 1 and uf == 1:\r\n            fold_params = dict(\r\n                kernel_size=kernel_size, dilation=1, padding=0, stride=stride\r\n            )\r\n            unfold = torch.nn.Unfold(**fold_params)\r\n\r\n            fold_params2 = dict(\r\n                kernel_size=(kernel_size[0] // df, kernel_size[0] // df),\r\n                dilation=1,\r\n                padding=0,\r\n                stride=(stride[0] // df, stride[1] // df),\r\n            )\r\n            fold = torch.nn.Fold(\r\n                output_size=(x.shape[2] // df, x.shape[3] // df), **fold_params2\r\n            )\r\n\r\n            weighting = self.get_weighting(\r\n                kernel_size[0] // df, kernel_size[1] // df, Ly, Lx, x.device\r\n            ).to(x.dtype)\r\n            normalization = fold(weighting).view(\r\n                1, 1, h // df, w // df\r\n            )  # normalizes the overlap\r\n            weighting = weighting.view(\r\n                (1, 1, kernel_size[0] // df, kernel_size[1] // df, Ly * Lx)\r\n            )\r\n\r\n        else:\r\n            raise NotImplementedError\r\n\r\n        return fold, unfold, normalization, weighting\r\n\r\n    @torch.no_grad()\r\n    def get_input(\r\n        self,\r\n        batch,\r\n        k,\r\n        return_first_stage_outputs=False,\r\n        force_c_encode=False,\r\n        cond_key=None,\r\n        return_original_cond=False,\r\n        bs=None,\r\n        return_x=False,\r\n    ):\r\n        # print(\"batch\",batch)\r\n        # print(\"k\",k)\r\n        x = super().get_input(batch, k)\r\n        masked_image = batch[self.masked_image]\r\n        mask = batch[self.mask]\r\n        # print(mask.shape,masked_image.shape)\r\n        mask = torch.nn.functional.interpolate(\r\n            mask, size=(mask.shape[2] // 8, mask.shape[3] // 8)\r\n        )\r\n        # mask=torch.cat([mask] * 2) #if do_classifier_free_guidance else mask\r\n        mask = mask.to(device=\"cuda\", dtype=x.dtype)\r\n        do_classifier_free_guidance = False\r\n        # mask, masked_image_latents = self.prepare_mask_latents(\r\n        #     mask,\r\n        #     masked_image,\r\n        #     batch_size * num_images_per_prompt,\r\n        #     mask.shape[0],\r\n        #     mask.shape[1],\r\n        #     mask.dtype,\r\n        #     \"cuda\",\r\n        #     torch.manual_seed(859311133),#generator\r\n        #     do_classifier_free_guidance,\r\n        # )\r\n        # print(\"x\",x)\r\n        if bs is not None:\r\n            x = x[:bs]\r\n        x = x.to(self.device)\r\n\r\n        encoder_posterior = self.encode_first_stage(x)\r\n        z = self.get_first_stage_encoding(encoder_posterior).detach()\r\n\r\n        masked_image_latents = self.get_first_stage_encoding(\r\n            self.encode_first_stage(masked_image)\r\n        ).detach()\r\n\r\n        if self.model.conditioning_key is not None and not self.force_null_conditioning:\r\n            if cond_key is None:\r\n                cond_key = self.cond_stage_key\r\n            if cond_key != self.first_stage_key:\r\n                if cond_key in [\"caption\", \"coordinates_bbox\", \"txt\"]:\r\n                    xc = batch[cond_key]\r\n                elif cond_key in [\"class_label\", \"cls\"]:\r\n                    xc = batch\r\n                else:\r\n                    xc = super().get_input(batch, cond_key).to(self.device)\r\n            else:\r\n                xc = x\r\n            if not self.cond_stage_trainable or force_c_encode:\r\n                if isinstance(xc, dict) or isinstance(xc, list):\r\n                    c = self.get_learned_conditioning(xc)\r\n                else:\r\n                    c = self.get_learned_conditioning(xc.to(self.device))\r\n            else:\r\n                c = xc\r\n            if bs is not None:\r\n                c = c[:bs]\r\n\r\n            if self.use_positional_encodings:\r\n                pos_x, pos_y = self.compute_latent_shifts(batch)\r\n                ckey = __conditioning_keys__[self.model.conditioning_key]\r\n                c = {ckey: c, \"pos_x\": pos_x, \"pos_y\": pos_y}\r\n\r\n        else:\r\n            c = None\r\n            xc = None\r\n            if self.use_positional_encodings:\r\n                pos_x, pos_y = self.compute_latent_shifts(batch)\r\n                c = {\"pos_x\": pos_x, \"pos_y\": pos_y}\r\n        out = [z, mask, masked_image_latents, c]\r\n        if return_first_stage_outputs:\r\n            xrec = self.decode_first_stage(z)\r\n            out.extend([x, xrec])\r\n        if return_x:\r\n            out.extend([x])\r\n        if return_original_cond:\r\n            out.append(xc)\r\n        return out\r\n\r\n    @torch.no_grad()\r\n    def decode_first_stage(self, z, predict_cids=False, force_not_quantize=False):\r\n        if predict_cids:\r\n            if z.dim() == 4:\r\n                z = torch.argmax(z.exp(), dim=1).long()\r\n            z = self.first_stage_model.quantize.get_codebook_entry(z, shape=None)\r\n            z = rearrange(z, \"b h w c -> b c h w\").contiguous()\r\n\r\n        z = 1.0 / self.scale_factor * z\r\n        return self.first_stage_model.decode(z)\r\n\r\n    @torch.no_grad()\r\n    def encode_first_stage(self, x):\r\n        return self.first_stage_model.encode(x)\r\n\r\n    @torch.no_grad()\r\n    def decode_first_stage_before_vae(\r\n        self, z, predict_cids=False, force_not_quantize=False\r\n    ):\r\n        if predict_cids:\r\n            if z.dim() == 4:\r\n                z = torch.argmax(z.exp(), dim=1).long()\r\n            z = self.first_stage_model.quantize.get_codebook_entry(z, shape=None)\r\n            z = rearrange(z, \"b h w c -> b c h w\").contiguous()\r\n\r\n        z = 1.0 / self.scale_factor * z\r\n        return z\r\n\r\n    def shared_step(self, batch, **kwargs):\r\n        x, mask, masked_image_latents, c = self.get_input(batch, self.first_stage_key)\r\n        loss = self(x, mask, masked_image_latents, c)\r\n        return loss\r\n\r\n    def forward(self, x, mask, masked_image_latents, c, *args, **kwargs):\r\n        t = torch.randint(\r\n            0, self.num_timesteps, (x.shape[0],), device=self.device\r\n        ).long()\r\n        if self.model.conditioning_key is not None:\r\n            assert c is not None\r\n            if self.cond_stage_trainable:\r\n                c = self.get_learned_conditioning(c)\r\n            if self.shorten_cond_schedule:  # TODO: drop this option\r\n                tc = self.cond_ids[t].to(self.device)\r\n                c = self.q_sample(x_start=c, t=tc, noise=torch.randn_like(c.float()))\r\n        return self.p_losses(x, mask, masked_image_latents, c, t, *args, **kwargs)\r\n\r\n    def apply_model(self, x_noisy, t, cond, return_ids=False):\r\n        if isinstance(cond, dict):\r\n            # hybrid case, cond is expected to be a dict\r\n            pass\r\n        else:\r\n            if not isinstance(cond, list):\r\n                cond = [cond]\r\n            key = (\r\n                \"c_concat\" if self.model.conditioning_key == \"concat\" else \"c_crossattn\"\r\n            )\r\n            cond = {key: cond}\r\n\r\n        x_recon = self.model(x_noisy, t, **cond)\r\n\r\n        if isinstance(x_recon, tuple) and not return_ids:\r\n            return x_recon[0]\r\n        else:\r\n            return x_recon\r\n\r\n    def _predict_eps_from_xstart(self, x_t, t, pred_xstart):\r\n        return (\r\n            extract_into_tensor(self.sqrt_recip_alphas_cumprod, t, x_t.shape) * x_t\r\n            - pred_xstart\r\n        ) / extract_into_tensor(self.sqrt_recipm1_alphas_cumprod, t, x_t.shape)\r\n\r\n    def _prior_bpd(self, x_start):\r\n        \"\"\"\r\n        Get the prior KL term for the variational lower-bound, measured in\r\n        bits-per-dim.\r\n        This term can't be optimized, as it only depends on the encoder.\r\n        :param x_start: the [N x C x ...] tensor of inputs.\r\n        :return: a batch of [N] KL values (in bits), one per batch element.\r\n        \"\"\"\r\n        batch_size = x_start.shape[0]\r\n        t = torch.tensor([self.num_timesteps - 1] * batch_size, device=x_start.device)\r\n        qt_mean, _, qt_log_variance = self.q_mean_variance(x_start, t)\r\n        kl_prior = normal_kl(\r\n            mean1=qt_mean, logvar1=qt_log_variance, mean2=0.0, logvar2=0.0\r\n        )\r\n        return mean_flat(kl_prior) / np.log(2.0)\r\n\r\n    def p_losses(\r\n        self, x_start, mask, masked_image_latents, cond, t, noise=None\r\n    ):  # latent diffusion\r\n        noise = default(noise, lambda: torch.randn_like(x_start))\r\n        x_noisy = self.q_sample(x_start=x_start, t=t, noise=noise)\r\n        model_output = self.apply_model(x_noisy, mask, masked_image_latents, t, cond)\r\n        # print(\"before loss: \", model_output.shape)\r\n        loss_dict = {}\r\n        prefix = \"train\" if self.training else \"val\"\r\n\r\n        if self.parameterization == \"x0\":\r\n            target = x_start\r\n        elif self.parameterization == \"eps\":\r\n            target = noise\r\n        elif self.parameterization == \"v\":\r\n            target = self.get_v(x_start, noise, t)\r\n        else:\r\n            raise NotImplementedError()\r\n\r\n        loss_simple = self.get_loss(model_output, target, mean=False).mean([1, 2, 3])\r\n        loss_dict.update({f\"{prefix}/loss_simple\": loss_simple.mean()})\r\n\r\n        logvar_t = self.logvar[t].to(self.device)\r\n        loss = loss_simple / torch.exp(logvar_t) + logvar_t\r\n        # loss = loss_simple / torch.exp(self.logvar) + self.logvar\r\n        if self.learn_logvar:\r\n            loss_dict.update({f\"{prefix}/loss_gamma\": loss.mean()})\r\n            loss_dict.update({\"logvar\": self.logvar.data.mean()})\r\n\r\n        loss = self.l_simple_weight * loss.mean()\r\n\r\n        loss_vlb = self.get_loss(model_output, target, mean=False).mean(dim=(1, 2, 3))\r\n        loss_vlb = (self.lvlb_weights[t] * loss_vlb).mean()\r\n        loss_dict.update({f\"{prefix}/loss_vlb\": loss_vlb})\r\n        loss += self.original_elbo_weight * loss_vlb\r\n        loss_dict.update({f\"{prefix}/loss\": loss})\r\n\r\n        return loss, loss_dict\r\n\r\n    def p_mean_variance(\r\n        self,\r\n        x,\r\n        c,\r\n        t,\r\n        clip_denoised: bool,\r\n        return_codebook_ids=False,\r\n        quantize_denoised=False,\r\n        return_x0=False,\r\n        score_corrector=None,\r\n        corrector_kwargs=None,\r\n    ):\r\n        t_in = t\r\n        model_out = self.apply_model(x, t_in, c, return_ids=return_codebook_ids)\r\n\r\n        if score_corrector is not None:\r\n            assert self.parameterization == \"eps\"\r\n            model_out = score_corrector.modify_score(\r\n                self, model_out, x, t, c, **corrector_kwargs\r\n            )\r\n\r\n        if return_codebook_ids:\r\n            model_out, logits = model_out\r\n\r\n        if self.parameterization == \"eps\":\r\n            x_recon = self.predict_start_from_noise(x, t=t, noise=model_out)\r\n        elif self.parameterization == \"x0\":\r\n            x_recon = model_out\r\n        else:\r\n            raise NotImplementedError()\r\n\r\n        if clip_denoised:\r\n            x_recon.clamp_(-1.0, 1.0)\r\n        if quantize_denoised:\r\n            x_recon, _, [_, _, indices] = self.first_stage_model.quantize(x_recon)\r\n        model_mean, posterior_variance, posterior_log_variance = self.q_posterior(\r\n            x_start=x_recon, x_t=x, t=t\r\n        )\r\n        if return_codebook_ids:\r\n            return model_mean, posterior_variance, posterior_log_variance, logits\r\n        elif return_x0:\r\n            return model_mean, posterior_variance, posterior_log_variance, x_recon\r\n        else:\r\n            return model_mean, posterior_variance, posterior_log_variance\r\n\r\n    @torch.no_grad()\r\n    def p_sample(\r\n        self,\r\n        x,\r\n        c,\r\n        t,\r\n        clip_denoised=False,\r\n        repeat_noise=False,\r\n        return_codebook_ids=False,\r\n        quantize_denoised=False,\r\n        return_x0=False,\r\n        temperature=1.0,\r\n        noise_dropout=0.0,\r\n        score_corrector=None,\r\n        corrector_kwargs=None,\r\n    ):\r\n        b, *_, device = *x.shape, x.device\r\n        outputs = self.p_mean_variance(\r\n            x=x,\r\n            c=c,\r\n            t=t,\r\n            clip_denoised=clip_denoised,\r\n            return_codebook_ids=return_codebook_ids,\r\n            quantize_denoised=quantize_denoised,\r\n            return_x0=return_x0,\r\n            score_corrector=score_corrector,\r\n            corrector_kwargs=corrector_kwargs,\r\n        )\r\n        if return_codebook_ids:\r\n            raise DeprecationWarning(\"Support dropped.\")\r\n            model_mean, _, model_log_variance, logits = outputs\r\n        elif return_x0:\r\n            model_mean, _, model_log_variance, x0 = outputs\r\n        else:\r\n            model_mean, _, model_log_variance = outputs\r\n\r\n        noise = noise_like(x.shape, device, repeat_noise) * temperature\r\n        if noise_dropout > 0.0:\r\n            noise = torch.nn.functional.dropout(noise, p=noise_dropout)\r\n        # no noise when t == 0\r\n        nonzero_mask = (1 - (t == 0).float()).reshape(b, *((1,) * (len(x.shape) - 1)))\r\n\r\n        if return_codebook_ids:\r\n            return model_mean + nonzero_mask * (\r\n                0.5 * model_log_variance\r\n            ).exp() * noise, logits.argmax(dim=1)\r\n        if return_x0:\r\n            return model_mean + nonzero_mask * (\r\n                0.5 * model_log_variance\r\n            ).exp() * noise, x0\r\n        else:\r\n            return model_mean + nonzero_mask * (0.5 * model_log_variance).exp() * noise\r\n\r\n    @torch.no_grad()\r\n    def progressive_denoising(\r\n        self,\r\n        cond,\r\n        shape,\r\n        verbose=True,\r\n        callback=None,\r\n        quantize_denoised=False,\r\n        img_callback=None,\r\n        mask=None,\r\n        x0=None,\r\n        temperature=1.0,\r\n        noise_dropout=0.0,\r\n        score_corrector=None,\r\n        corrector_kwargs=None,\r\n        batch_size=None,\r\n        x_T=None,\r\n        start_T=None,\r\n        log_every_t=None,\r\n    ):\r\n        if not log_every_t:\r\n            log_every_t = self.log_every_t\r\n        timesteps = self.num_timesteps\r\n        if batch_size is not None:\r\n            b = batch_size if batch_size is not None else shape[0]\r\n            shape = [batch_size] + list(shape)\r\n        else:\r\n            b = batch_size = shape[0]\r\n        if x_T is None:\r\n            img = torch.randn(shape, device=self.device)\r\n        else:\r\n            img = x_T\r\n        intermediates = []\r\n        if cond is not None:\r\n            if isinstance(cond, dict):\r\n                cond = {\r\n                    key: cond[key][:batch_size]\r\n                    if not isinstance(cond[key], list)\r\n                    else list(map(lambda x: x[:batch_size], cond[key]))\r\n                    for key in cond\r\n                }\r\n            else:\r\n                cond = (\r\n                    [c[:batch_size] for c in cond]\r\n                    if isinstance(cond, list)\r\n                    else cond[:batch_size]\r\n                )\r\n\r\n        if start_T is not None:\r\n            timesteps = min(timesteps, start_T)\r\n        iterator = (\r\n            tqdm(\r\n                reversed(range(0, timesteps)),\r\n                desc=\"Progressive Generation\",\r\n                total=timesteps,\r\n            )\r\n            if verbose\r\n            else reversed(range(0, timesteps))\r\n        )\r\n        if isinstance(temperature, float):\r\n            temperature = [temperature] * timesteps\r\n\r\n        for i in iterator:\r\n            ts = torch.full((b,), i, device=self.device, dtype=torch.long)\r\n            if self.shorten_cond_schedule:\r\n                assert self.model.conditioning_key != \"hybrid\"\r\n                tc = self.cond_ids[ts].to(cond.device)\r\n                cond = self.q_sample(x_start=cond, t=tc, noise=torch.randn_like(cond))\r\n\r\n            img, x0_partial = self.p_sample(\r\n                img,\r\n                cond,\r\n                ts,\r\n                clip_denoised=self.clip_denoised,\r\n                quantize_denoised=quantize_denoised,\r\n                return_x0=True,\r\n                temperature=temperature[i],\r\n                noise_dropout=noise_dropout,\r\n                score_corrector=score_corrector,\r\n                corrector_kwargs=corrector_kwargs,\r\n            )\r\n            if mask is not None:\r\n                assert x0 is not None\r\n                img_orig = self.q_sample(x0, ts)\r\n                img = img_orig * mask + (1.0 - mask) * img\r\n\r\n            if i % log_every_t == 0 or i == timesteps - 1:\r\n                intermediates.append(x0_partial)\r\n            if callback:\r\n                callback(i)\r\n            if img_callback:\r\n                img_callback(img, i)\r\n        return img, intermediates\r\n\r\n    @torch.no_grad()\r\n    def p_sample_loop(\r\n        self,\r\n        cond,\r\n        shape,\r\n        return_intermediates=False,\r\n        x_T=None,\r\n        verbose=True,\r\n        callback=None,\r\n        timesteps=None,\r\n        quantize_denoised=False,\r\n        mask=None,\r\n        x0=None,\r\n        img_callback=None,\r\n        start_T=None,\r\n        log_every_t=None,\r\n    ):\r\n        if not log_every_t:\r\n            log_every_t = self.log_every_t\r\n        device = self.betas.device\r\n        b = shape[0]\r\n        if x_T is None:\r\n            img = torch.randn(shape, device=device)\r\n        else:\r\n            img = x_T\r\n\r\n        intermediates = [img]\r\n        if timesteps is None:\r\n            timesteps = self.num_timesteps\r\n\r\n        if start_T is not None:\r\n            timesteps = min(timesteps, start_T)\r\n        iterator = (\r\n            tqdm(reversed(range(0, timesteps)), desc=\"Sampling t\", total=timesteps)\r\n            if verbose\r\n            else reversed(range(0, timesteps))\r\n        )\r\n\r\n        if mask is not None:\r\n            assert x0 is not None\r\n            assert x0.shape[2:3] == mask.shape[2:3]  # spatial size has to match\r\n\r\n        for i in iterator:\r\n            ts = torch.full((b,), i, device=device, dtype=torch.long)\r\n            if self.shorten_cond_schedule:\r\n                assert self.model.conditioning_key != \"hybrid\"\r\n                tc = self.cond_ids[ts].to(cond.device)\r\n                cond = self.q_sample(x_start=cond, t=tc, noise=torch.randn_like(cond))\r\n\r\n            img = self.p_sample(\r\n                img,\r\n                cond,\r\n                ts,\r\n                clip_denoised=self.clip_denoised,\r\n                quantize_denoised=quantize_denoised,\r\n            )\r\n            if mask is not None:\r\n                img_orig = self.q_sample(x0, ts)\r\n                img = img_orig * mask + (1.0 - mask) * img\r\n\r\n            if i % log_every_t == 0 or i == timesteps - 1:\r\n                intermediates.append(img)\r\n            if callback:\r\n                callback(i)\r\n            if img_callback:\r\n                img_callback(img, i)\r\n\r\n        if return_intermediates:\r\n            return img, intermediates\r\n        return img\r\n\r\n    @torch.no_grad()\r\n    def sample(\r\n        self,\r\n        cond,\r\n        batch_size=16,\r\n        return_intermediates=False,\r\n        x_T=None,\r\n        verbose=True,\r\n        timesteps=None,\r\n        quantize_denoised=False,\r\n        mask=None,\r\n        x0=None,\r\n        shape=None,\r\n        **kwargs,\r\n    ):\r\n        if shape is None:\r\n            shape = (batch_size, self.channels, self.image_size, self.image_size)\r\n        if cond is not None:\r\n            if isinstance(cond, dict):\r\n                cond = {\r\n                    key: cond[key][:batch_size]\r\n                    if not isinstance(cond[key], list)\r\n                    else list(map(lambda x: x[:batch_size], cond[key]))\r\n                    for key in cond\r\n                }\r\n            else:\r\n                cond = (\r\n                    [c[:batch_size] for c in cond]\r\n                    if isinstance(cond, list)\r\n                    else cond[:batch_size]\r\n                )\r\n        return self.p_sample_loop(\r\n            cond,\r\n            shape,\r\n            return_intermediates=return_intermediates,\r\n            x_T=x_T,\r\n            verbose=verbose,\r\n            timesteps=timesteps,\r\n            quantize_denoised=quantize_denoised,\r\n            mask=mask,\r\n            x0=x0,\r\n        )\r\n\r\n    @torch.no_grad()\r\n    def sample_log(self, cond, batch_size, ddim, ddim_steps, **kwargs):\r\n        if ddim:\r\n            ddim_sampler = DDIMSampler(self)\r\n            shape = (self.channels, self.image_size, self.image_size)\r\n            samples, intermediates = ddim_sampler.sample(\r\n                ddim_steps, batch_size, shape, cond, verbose=False, **kwargs\r\n            )\r\n\r\n        else:\r\n            samples, intermediates = self.sample(\r\n                cond=cond, batch_size=batch_size, return_intermediates=True, **kwargs\r\n            )\r\n\r\n        return samples, intermediates\r\n\r\n    @torch.no_grad()\r\n    def get_unconditional_conditioning(self, batch_size, null_label=None):\r\n        if null_label is not None:\r\n            xc = null_label\r\n            if isinstance(xc, ListConfig):\r\n                xc = list(xc)\r\n            if isinstance(xc, dict) or isinstance(xc, list):\r\n                c = self.get_learned_conditioning(xc)\r\n            else:\r\n                if hasattr(xc, \"to\"):\r\n                    xc = xc.to(self.device)\r\n                c = self.get_learned_conditioning(xc)\r\n        else:\r\n            if self.cond_stage_key in [\"class_label\", \"cls\"]:\r\n                xc = self.cond_stage_model.get_unconditional_conditioning(\r\n                    batch_size, device=self.device\r\n                )\r\n                return self.get_learned_conditioning(xc)\r\n            else:\r\n                raise NotImplementedError(\"todo\")\r\n        if isinstance(c, list):  # in case the encoder gives us a list\r\n            for i in range(len(c)):\r\n                c[i] = repeat(c[i], \"1 ... -> b ...\", b=batch_size).to(self.device)\r\n        else:\r\n            c = repeat(c, \"1 ... -> b ...\", b=batch_size).to(self.device)\r\n        return c\r\n\r\n    @torch.no_grad()\r\n    def log_images(\r\n        self,\r\n        batch,\r\n        N=8,\r\n        n_row=4,\r\n        sample=True,\r\n        ddim_steps=50,\r\n        ddim_eta=0.0,\r\n        return_keys=None,\r\n        quantize_denoised=True,\r\n        inpaint=True,\r\n        plot_denoise_rows=False,\r\n        plot_progressive_rows=True,\r\n        plot_diffusion_rows=True,\r\n        unconditional_guidance_scale=1.0,\r\n        unconditional_guidance_label=None,\r\n        use_ema_scope=True,\r\n        **kwargs,\r\n    ):\r\n        ema_scope = self.ema_scope if use_ema_scope else nullcontext\r\n        use_ddim = ddim_steps is not None\r\n\r\n        log = dict()\r\n        z, c, x, xrec, xc = self.get_input(\r\n            batch,\r\n            self.first_stage_key,\r\n            return_first_stage_outputs=True,\r\n            force_c_encode=True,\r\n            return_original_cond=True,\r\n            bs=N,\r\n        )\r\n        N = min(x.shape[0], N)\r\n        n_row = min(x.shape[0], n_row)\r\n        log[\"inputs\"] = x\r\n        log[\"reconstruction\"] = xrec\r\n        if self.model.conditioning_key is not None:\r\n            if hasattr(self.cond_stage_model, \"decode\"):\r\n                xc = self.cond_stage_model.decode(c)\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"caption\", \"txt\"]:\r\n                xc = log_txt_as_img(\r\n                    (x.shape[2], x.shape[3]),\r\n                    batch[self.cond_stage_key],\r\n                    size=x.shape[2] // 25,\r\n                )\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"class_label\", \"cls\"]:\r\n                try:\r\n                    xc = log_txt_as_img(\r\n                        (x.shape[2], x.shape[3]),\r\n                        batch[\"human_label\"],\r\n                        size=x.shape[2] // 25,\r\n                    )\r\n                    log[\"conditioning\"] = xc\r\n                except KeyError:\r\n                    # probably no \"human_label\" in batch\r\n                    pass\r\n            elif isimage(xc):\r\n                log[\"conditioning\"] = xc\r\n            if ismap(xc):\r\n                log[\"original_conditioning\"] = self.to_rgb(xc)\r\n\r\n        if plot_diffusion_rows:\r\n            # get diffusion row\r\n            diffusion_row = list()\r\n            z_start = z[:n_row]\r\n            for t in range(self.num_timesteps):\r\n                if t % self.log_every_t == 0 or t == self.num_timesteps - 1:\r\n                    t = repeat(torch.tensor([t]), \"1 -> b\", b=n_row)\r\n                    t = t.to(self.device).long()\r\n                    noise = torch.randn_like(z_start)\r\n                    z_noisy = self.q_sample(x_start=z_start, t=t, noise=noise)\r\n                    diffusion_row.append(self.decode_first_stage(z_noisy))\r\n\r\n            diffusion_row = torch.stack(diffusion_row)  # n_log_step, n_row, C, H, W\r\n            diffusion_grid = rearrange(diffusion_row, \"n b c h w -> b n c h w\")\r\n            diffusion_grid = rearrange(diffusion_grid, \"b n c h w -> (b n) c h w\")\r\n            diffusion_grid = make_grid(diffusion_grid, nrow=diffusion_row.shape[0])\r\n            log[\"diffusion_row\"] = diffusion_grid\r\n\r\n        if sample:\r\n            # get denoise row\r\n            with ema_scope(\"Sampling\"):\r\n                samples, z_denoise_row = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                )\r\n                # samples, z_denoise_row = self.sample(cond=c, batch_size=N, return_intermediates=True)\r\n            x_samples = self.decode_first_stage(samples)\r\n            log[\"samples\"] = x_samples\r\n            if plot_denoise_rows:\r\n                denoise_grid = self._get_denoise_row_from_list(z_denoise_row)\r\n                log[\"denoise_row\"] = denoise_grid\r\n\r\n            if (\r\n                quantize_denoised\r\n                and not isinstance(self.first_stage_model, AutoencoderKL)\r\n                and not isinstance(self.first_stage_model, IdentityFirstStage)\r\n            ):\r\n                # also display when quantizing x0 while sampling\r\n                with ema_scope(\"Plotting Quantized Denoised\"):\r\n                    samples, z_denoise_row = self.sample_log(\r\n                        cond=c,\r\n                        batch_size=N,\r\n                        ddim=use_ddim,\r\n                        ddim_steps=ddim_steps,\r\n                        eta=ddim_eta,\r\n                        quantize_denoised=True,\r\n                    )\r\n                    # samples, z_denoise_row = self.sample(cond=c, batch_size=N, return_intermediates=True,\r\n                    #                                      quantize_denoised=True)\r\n                x_samples = self.decode_first_stage(samples.to(self.device))\r\n                log[\"samples_x0_quantized\"] = x_samples\r\n\r\n        if unconditional_guidance_scale > 1.0:\r\n            uc = self.get_unconditional_conditioning(N, unconditional_guidance_label)\r\n            if self.model.conditioning_key == \"crossattn-adm\":\r\n                uc = {\"c_crossattn\": [uc], \"c_adm\": c[\"c_adm\"]}\r\n            with ema_scope(\"Sampling with classifier-free guidance\"):\r\n                samples_cfg, _ = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                    unconditional_guidance_scale=unconditional_guidance_scale,\r\n                    unconditional_conditioning=uc,\r\n                )\r\n                x_samples_cfg = self.decode_first_stage(samples_cfg)\r\n                log[f\"samples_cfg_scale_{unconditional_guidance_scale:.2f}\"] = (\r\n                    x_samples_cfg\r\n                )\r\n\r\n        if inpaint:\r\n            # make a simple center square\r\n            b, h, w = z.shape[0], z.shape[2], z.shape[3]\r\n            mask = torch.ones(N, h, w).to(self.device)\r\n            # zeros will be filled in\r\n            mask[:, h // 4 : 3 * h // 4, w // 4 : 3 * w // 4] = 0.0\r\n            mask = mask[:, None, ...]\r\n            with ema_scope(\"Plotting Inpaint\"):\r\n                samples, _ = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    eta=ddim_eta,\r\n                    ddim_steps=ddim_steps,\r\n                    x0=z[:N],\r\n                    mask=mask,\r\n                )\r\n            x_samples = self.decode_first_stage(samples.to(self.device))\r\n            log[\"samples_inpainting\"] = x_samples\r\n            log[\"mask\"] = mask\r\n\r\n            # outpaint\r\n            mask = 1.0 - mask\r\n            with ema_scope(\"Plotting Outpaint\"):\r\n                samples, _ = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    eta=ddim_eta,\r\n                    ddim_steps=ddim_steps,\r\n                    x0=z[:N],\r\n                    mask=mask,\r\n                )\r\n            x_samples = self.decode_first_stage(samples.to(self.device))\r\n            log[\"samples_outpainting\"] = x_samples\r\n\r\n        if plot_progressive_rows:\r\n            with ema_scope(\"Plotting Progressives\"):\r\n                img, progressives = self.progressive_denoising(\r\n                    c,\r\n                    shape=(self.channels, self.image_size, self.image_size),\r\n                    batch_size=N,\r\n                )\r\n            prog_row = self._get_denoise_row_from_list(\r\n                progressives, desc=\"Progressive Generation\"\r\n            )\r\n            log[\"progressive_row\"] = prog_row\r\n\r\n        if return_keys:\r\n            if np.intersect1d(list(log.keys()), return_keys).shape[0] == 0:\r\n                return log\r\n            else:\r\n                return {key: log[key] for key in return_keys}\r\n        return log\r\n\r\n    def configure_optimizers(self):\r\n        lr = self.learning_rate\r\n        params = list(self.model.parameters())\r\n        if self.cond_stage_trainable:\r\n            print(f\"{self.__class__.__name__}: Also optimizing conditioner params!\")\r\n            params = params + list(self.cond_stage_model.parameters())\r\n        if self.learn_logvar:\r\n            print(\"Diffusion model optimizing logvar\")\r\n            params.append(self.logvar)\r\n        opt = torch.optim.AdamW(params, lr=lr)\r\n        if self.use_scheduler:\r\n            assert \"target\" in self.scheduler_config\r\n            scheduler = instantiate_from_config(self.scheduler_config)\r\n\r\n            print(\"Setting up LambdaLR scheduler...\")\r\n            scheduler = [\r\n                {\r\n                    \"scheduler\": LambdaLR(opt, lr_lambda=scheduler.schedule),\r\n                    \"interval\": \"step\",\r\n                    \"frequency\": 1,\r\n                }\r\n            ]\r\n            return [opt], scheduler\r\n        return opt\r\n\r\n    @torch.no_grad()\r\n    def to_rgb(self, x):\r\n        x = x.float()\r\n        if not hasattr(self, \"colorize\"):\r\n            self.colorize = torch.randn(3, x.shape[1], 1, 1).to(x)\r\n        x = nn.functional.conv2d(x, weight=self.colorize)\r\n        x = 2.0 * (x - x.min()) / (x.max() - x.min()) - 1.0\r\n        return x\r\n\r\n\r\nclass DiffusionWrapper(pl.LightningModule):\r\n    def __init__(self, diff_model_config, conditioning_key):\r\n        super().__init__()\r\n        self.sequential_cross_attn = diff_model_config.pop(\r\n            \"sequential_crossattn\", False\r\n        )\r\n        self.diffusion_model = instantiate_from_config(diff_model_config)\r\n        self.conditioning_key = conditioning_key\r\n        assert self.conditioning_key in [\r\n            None,\r\n            \"concat\",\r\n            \"crossattn\",\r\n            \"hybrid\",\r\n            \"adm\",\r\n            \"hybrid-adm\",\r\n            \"crossattn-adm\",\r\n        ]\r\n\r\n    def forward(\r\n        self, x, t, c_concat: list = None, c_crossattn: list = None, c_adm=None\r\n    ):\r\n        if self.conditioning_key is None:\r\n            out = self.diffusion_model(x, t)\r\n        elif self.conditioning_key == \"concat\":\r\n            xc = torch.cat([x] + c_concat, dim=1)\r\n            out = self.diffusion_model(xc, t)\r\n        elif self.conditioning_key == \"crossattn\":\r\n            if not self.sequential_cross_attn:\r\n                cc = torch.cat(c_crossattn, 1)\r\n            else:\r\n                cc = c_crossattn\r\n            out = self.diffusion_model(x, t, context=cc)\r\n        elif self.conditioning_key == \"hybrid\":\r\n            xc = torch.cat([x] + c_concat, dim=1)\r\n            cc = torch.cat(c_crossattn, 1)\r\n            out = self.diffusion_model(xc, t, context=cc)\r\n        elif self.conditioning_key == \"hybrid-adm\":\r\n            assert c_adm is not None\r\n            xc = torch.cat([x] + c_concat, dim=1)\r\n            cc = torch.cat(c_crossattn, 1)\r\n            out = self.diffusion_model(xc, t, context=cc, y=c_adm)\r\n        elif self.conditioning_key == \"crossattn-adm\":\r\n            assert c_adm is not None\r\n            cc = torch.cat(c_crossattn, 1)\r\n            out = self.diffusion_model(x, t, context=cc, y=c_adm)\r\n        elif self.conditioning_key == \"adm\":\r\n            cc = c_crossattn[0]\r\n            out = self.diffusion_model(x, t, y=cc)\r\n        else:\r\n            raise NotImplementedError()\r\n\r\n        return out\r\n\r\n\r\nclass LatentUpscaleDiffusion(LatentDiffusion):\r\n    def __init__(\r\n        self,\r\n        *args,\r\n        low_scale_config,\r\n        low_scale_key=\"LR\",\r\n        noise_level_key=None,\r\n        **kwargs,\r\n    ):\r\n        super().__init__(*args, **kwargs)\r\n        # assumes that neither the cond_stage nor the low_scale_model contain trainable params\r\n        assert not self.cond_stage_trainable\r\n        self.instantiate_low_stage(low_scale_config)\r\n        self.low_scale_key = low_scale_key\r\n        self.noise_level_key = noise_level_key\r\n\r\n    def instantiate_low_stage(self, config):\r\n        model = instantiate_from_config(config)\r\n        self.low_scale_model = model.eval()\r\n        self.low_scale_model.train = disabled_train\r\n        for param in self.low_scale_model.parameters():\r\n            param.requires_grad = False\r\n\r\n    @torch.no_grad()\r\n    def get_input(self, batch, k, cond_key=None, bs=None, log_mode=False):\r\n        if not log_mode:\r\n            z, c = super().get_input(batch, k, force_c_encode=True, bs=bs)\r\n        else:\r\n            z, c, x, xrec, xc = super().get_input(\r\n                batch,\r\n                self.first_stage_key,\r\n                return_first_stage_outputs=True,\r\n                force_c_encode=True,\r\n                return_original_cond=True,\r\n                bs=bs,\r\n            )\r\n        x_low = batch[self.low_scale_key][:bs]\r\n        x_low = rearrange(x_low, \"b h w c -> b c h w\")\r\n        x_low = x_low.to(memory_format=torch.contiguous_format).float()\r\n        zx, noise_level = self.low_scale_model(x_low)\r\n        if self.noise_level_key is not None:\r\n            # get noise level from batch instead, e.g. when extracting a custom noise level for bsr\r\n            raise NotImplementedError(\"TODO\")\r\n\r\n        all_conds = {\"c_concat\": [zx], \"c_crossattn\": [c], \"c_adm\": noise_level}\r\n        if log_mode:\r\n            # TODO: maybe disable if too expensive\r\n            x_low_rec = self.low_scale_model.decode(zx)\r\n            return z, all_conds, x, xrec, xc, x_low, x_low_rec, noise_level\r\n        return z, all_conds\r\n\r\n    @torch.no_grad()\r\n    def log_images(\r\n        self,\r\n        batch,\r\n        N=8,\r\n        n_row=4,\r\n        sample=True,\r\n        ddim_steps=200,\r\n        ddim_eta=1.0,\r\n        return_keys=None,\r\n        plot_denoise_rows=False,\r\n        plot_progressive_rows=True,\r\n        plot_diffusion_rows=True,\r\n        unconditional_guidance_scale=1.0,\r\n        unconditional_guidance_label=None,\r\n        use_ema_scope=True,\r\n        **kwargs,\r\n    ):\r\n        ema_scope = self.ema_scope if use_ema_scope else nullcontext\r\n        use_ddim = ddim_steps is not None\r\n\r\n        log = dict()\r\n        z, c, x, xrec, xc, x_low, x_low_rec, noise_level = self.get_input(\r\n            batch, self.first_stage_key, bs=N, log_mode=True\r\n        )\r\n        N = min(x.shape[0], N)\r\n        n_row = min(x.shape[0], n_row)\r\n        log[\"inputs\"] = x\r\n        log[\"reconstruction\"] = xrec\r\n        log[\"x_lr\"] = x_low\r\n        log[\r\n            f\"x_lr_rec_@noise_levels{'-'.join(map(lambda x: str(x), list(noise_level.cpu().numpy())))}\"\r\n        ] = x_low_rec\r\n        if self.model.conditioning_key is not None:\r\n            if hasattr(self.cond_stage_model, \"decode\"):\r\n                xc = self.cond_stage_model.decode(c)\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"caption\", \"txt\"]:\r\n                xc = log_txt_as_img(\r\n                    (x.shape[2], x.shape[3]),\r\n                    batch[self.cond_stage_key],\r\n                    size=x.shape[2] // 25,\r\n                )\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"class_label\", \"cls\"]:\r\n                xc = log_txt_as_img(\r\n                    (x.shape[2], x.shape[3]),\r\n                    batch[\"human_label\"],\r\n                    size=x.shape[2] // 25,\r\n                )\r\n                log[\"conditioning\"] = xc\r\n            elif isimage(xc):\r\n                log[\"conditioning\"] = xc\r\n            if ismap(xc):\r\n                log[\"original_conditioning\"] = self.to_rgb(xc)\r\n\r\n        if plot_diffusion_rows:\r\n            # get diffusion row\r\n            diffusion_row = list()\r\n            z_start = z[:n_row]\r\n            for t in range(self.num_timesteps):\r\n                if t % self.log_every_t == 0 or t == self.num_timesteps - 1:\r\n                    t = repeat(torch.tensor([t]), \"1 -> b\", b=n_row)\r\n                    t = t.to(self.device).long()\r\n                    noise = torch.randn_like(z_start)\r\n                    z_noisy = self.q_sample(x_start=z_start, t=t, noise=noise)\r\n                    diffusion_row.append(self.decode_first_stage(z_noisy))\r\n\r\n            diffusion_row = torch.stack(diffusion_row)  # n_log_step, n_row, C, H, W\r\n            diffusion_grid = rearrange(diffusion_row, \"n b c h w -> b n c h w\")\r\n            diffusion_grid = rearrange(diffusion_grid, \"b n c h w -> (b n) c h w\")\r\n            diffusion_grid = make_grid(diffusion_grid, nrow=diffusion_row.shape[0])\r\n            log[\"diffusion_row\"] = diffusion_grid\r\n\r\n        if sample:\r\n            # get denoise row\r\n            with ema_scope(\"Sampling\"):\r\n                samples, z_denoise_row = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                )\r\n                # samples, z_denoise_row = self.sample(cond=c, batch_size=N, return_intermediates=True)\r\n            x_samples = self.decode_first_stage(samples)\r\n            log[\"samples\"] = x_samples\r\n            if plot_denoise_rows:\r\n                denoise_grid = self._get_denoise_row_from_list(z_denoise_row)\r\n                log[\"denoise_row\"] = denoise_grid\r\n\r\n        if unconditional_guidance_scale > 1.0:\r\n            uc_tmp = self.get_unconditional_conditioning(\r\n                N, unconditional_guidance_label\r\n            )\r\n            # TODO explore better \"unconditional\" choices for the other keys\r\n            # maybe guide away from empty text label and highest noise level and maximally degraded zx?\r\n            uc = dict()\r\n            for k in c:\r\n                if k == \"c_crossattn\":\r\n                    assert isinstance(c[k], list) and len(c[k]) == 1\r\n                    uc[k] = [uc_tmp]\r\n                elif k == \"c_adm\":  # todo: only run with text-based guidance?\r\n                    assert isinstance(c[k], torch.Tensor)\r\n                    # uc[k] = torch.ones_like(c[k]) * self.low_scale_model.max_noise_level\r\n                    uc[k] = c[k]\r\n                elif isinstance(c[k], list):\r\n                    uc[k] = [c[k][i] for i in range(len(c[k]))]\r\n                else:\r\n                    uc[k] = c[k]\r\n\r\n            with ema_scope(\"Sampling with classifier-free guidance\"):\r\n                samples_cfg, _ = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                    unconditional_guidance_scale=unconditional_guidance_scale,\r\n                    unconditional_conditioning=uc,\r\n                )\r\n                x_samples_cfg = self.decode_first_stage(samples_cfg)\r\n                log[f\"samples_cfg_scale_{unconditional_guidance_scale:.2f}\"] = (\r\n                    x_samples_cfg\r\n                )\r\n\r\n        if plot_progressive_rows:\r\n            with ema_scope(\"Plotting Progressives\"):\r\n                img, progressives = self.progressive_denoising(\r\n                    c,\r\n                    shape=(self.channels, self.image_size, self.image_size),\r\n                    batch_size=N,\r\n                )\r\n            prog_row = self._get_denoise_row_from_list(\r\n                progressives, desc=\"Progressive Generation\"\r\n            )\r\n            log[\"progressive_row\"] = prog_row\r\n\r\n        return log\r\n\r\n\r\nclass LatentFinetuneDiffusion(LatentDiffusion):\r\n    \"\"\"\r\n    Basis for different finetunas, such as inpainting or depth2image\r\n    To disable finetuning mode, set finetune_keys to None\r\n    \"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        concat_keys: tuple,\r\n        finetune_keys=(\r\n            \"model.diffusion_model.input_blocks.0.0.weight\",\r\n            \"model_ema.diffusion_modelinput_blocks00weight\",\r\n        ),\r\n        keep_finetune_dims=4,\r\n        # if model was trained without concat mode before and we would like to keep these channels\r\n        c_concat_log_start=None,  # to log reconstruction of c_concat codes\r\n        c_concat_log_end=None,\r\n        *args,\r\n        **kwargs,\r\n    ):\r\n        ckpt_path = kwargs.pop(\"ckpt_path\", None)\r\n        ignore_keys = kwargs.pop(\"ignore_keys\", list())\r\n        super().__init__(*args, **kwargs)\r\n        self.finetune_keys = finetune_keys\r\n        self.concat_keys = concat_keys\r\n        self.keep_dims = keep_finetune_dims\r\n        self.c_concat_log_start = c_concat_log_start\r\n        self.c_concat_log_end = c_concat_log_end\r\n        if exists(self.finetune_keys):\r\n            assert exists(ckpt_path), \"can only finetune from a given checkpoint\"\r\n        if exists(ckpt_path):\r\n            self.init_from_ckpt(ckpt_path, ignore_keys)\r\n\r\n    def init_from_ckpt(self, path, ignore_keys=list(), only_model=False):\r\n        sd = torch.load(path, map_location=\"cpu\")\r\n        if \"state_dict\" in list(sd.keys()):\r\n            sd = sd[\"state_dict\"]\r\n        keys = list(sd.keys())\r\n        for k in keys:\r\n            for ik in ignore_keys:\r\n                if k.startswith(ik):\r\n                    print(\"Deleting key {} from state_dict.\".format(k))\r\n                    del sd[k]\r\n\r\n            # make it explicit, finetune by including extra input channels\r\n            if exists(self.finetune_keys) and k in self.finetune_keys:\r\n                new_entry = None\r\n                for name, param in self.named_parameters():\r\n                    if name in self.finetune_keys:\r\n                        print(\r\n                            f\"modifying key '{name}' and keeping its original {self.keep_dims} (channels) dimensions only\"\r\n                        )\r\n                        new_entry = torch.zeros_like(param)  # zero init\r\n                assert exists(new_entry), \"did not find matching parameter to modify\"\r\n                new_entry[:, : self.keep_dims, ...] = sd[k]\r\n                sd[k] = new_entry\r\n\r\n        missing, unexpected = (\r\n            self.load_state_dict(sd, strict=False)\r\n            if not only_model\r\n            else self.model.load_state_dict(sd, strict=False)\r\n        )\r\n        print(\r\n            f\"Restored from {path} with {len(missing)} missing and {len(unexpected)} unexpected keys\"\r\n        )\r\n        if len(missing) > 0:\r\n            print(f\"Missing Keys: {missing}\")\r\n        if len(unexpected) > 0:\r\n            print(f\"Unexpected Keys: {unexpected}\")\r\n\r\n    @torch.no_grad()\r\n    def log_images(\r\n        self,\r\n        batch,\r\n        N=8,\r\n        n_row=4,\r\n        sample=True,\r\n        ddim_steps=200,\r\n        ddim_eta=1.0,\r\n        return_keys=None,\r\n        quantize_denoised=True,\r\n        inpaint=True,\r\n        plot_denoise_rows=False,\r\n        plot_progressive_rows=True,\r\n        plot_diffusion_rows=True,\r\n        unconditional_guidance_scale=1.0,\r\n        unconditional_guidance_label=None,\r\n        use_ema_scope=True,\r\n        **kwargs,\r\n    ):\r\n        ema_scope = self.ema_scope if use_ema_scope else nullcontext\r\n        use_ddim = ddim_steps is not None\r\n\r\n        log = dict()\r\n        z, c, x, xrec, xc = self.get_input(\r\n            batch, self.first_stage_key, bs=N, return_first_stage_outputs=True\r\n        )\r\n        c_cat, c = c[\"c_concat\"][0], c[\"c_crossattn\"][0]\r\n        N = min(x.shape[0], N)\r\n        n_row = min(x.shape[0], n_row)\r\n        log[\"inputs\"] = x\r\n        log[\"reconstruction\"] = xrec\r\n        if self.model.conditioning_key is not None:\r\n            if hasattr(self.cond_stage_model, \"decode\"):\r\n                xc = self.cond_stage_model.decode(c)\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"caption\", \"txt\"]:\r\n                xc = log_txt_as_img(\r\n                    (x.shape[2], x.shape[3]),\r\n                    batch[self.cond_stage_key],\r\n                    size=x.shape[2] // 25,\r\n                )\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"class_label\", \"cls\"]:\r\n                xc = log_txt_as_img(\r\n                    (x.shape[2], x.shape[3]),\r\n                    batch[\"human_label\"],\r\n                    size=x.shape[2] // 25,\r\n                )\r\n                log[\"conditioning\"] = xc\r\n            elif isimage(xc):\r\n                log[\"conditioning\"] = xc\r\n            if ismap(xc):\r\n                log[\"original_conditioning\"] = self.to_rgb(xc)\r\n\r\n        if not (self.c_concat_log_start is None and self.c_concat_log_end is None):\r\n            log[\"c_concat_decoded\"] = self.decode_first_stage(\r\n                c_cat[:, self.c_concat_log_start : self.c_concat_log_end]\r\n            )\r\n\r\n        if plot_diffusion_rows:\r\n            # get diffusion row\r\n            diffusion_row = list()\r\n            z_start = z[:n_row]\r\n            for t in range(self.num_timesteps):\r\n                if t % self.log_every_t == 0 or t == self.num_timesteps - 1:\r\n                    t = repeat(torch.tensor([t]), \"1 -> b\", b=n_row)\r\n                    t = t.to(self.device).long()\r\n                    noise = torch.randn_like(z_start)\r\n                    z_noisy = self.q_sample(x_start=z_start, t=t, noise=noise)\r\n                    diffusion_row.append(self.decode_first_stage(z_noisy))\r\n\r\n            diffusion_row = torch.stack(diffusion_row)  # n_log_step, n_row, C, H, W\r\n            diffusion_grid = rearrange(diffusion_row, \"n b c h w -> b n c h w\")\r\n            diffusion_grid = rearrange(diffusion_grid, \"b n c h w -> (b n) c h w\")\r\n            diffusion_grid = make_grid(diffusion_grid, nrow=diffusion_row.shape[0])\r\n            log[\"diffusion_row\"] = diffusion_grid\r\n\r\n        if sample:\r\n            # get denoise row\r\n            with ema_scope(\"Sampling\"):\r\n                samples, z_denoise_row = self.sample_log(\r\n                    cond={\"c_concat\": [c_cat], \"c_crossattn\": [c]},\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                )\r\n                # samples, z_denoise_row = self.sample(cond=c, batch_size=N, return_intermediates=True)\r\n            x_samples = self.decode_first_stage(samples)\r\n            log[\"samples\"] = x_samples\r\n            if plot_denoise_rows:\r\n                denoise_grid = self._get_denoise_row_from_list(z_denoise_row)\r\n                log[\"denoise_row\"] = denoise_grid\r\n\r\n        if unconditional_guidance_scale > 1.0:\r\n            uc_cross = self.get_unconditional_conditioning(\r\n                N, unconditional_guidance_label\r\n            )\r\n            uc_cat = c_cat\r\n            uc_full = {\"c_concat\": [uc_cat], \"c_crossattn\": [uc_cross]}\r\n            with ema_scope(\"Sampling with classifier-free guidance\"):\r\n                samples_cfg, _ = self.sample_log(\r\n                    cond={\"c_concat\": [c_cat], \"c_crossattn\": [c]},\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                    unconditional_guidance_scale=unconditional_guidance_scale,\r\n                    unconditional_conditioning=uc_full,\r\n                )\r\n                x_samples_cfg = self.decode_first_stage(samples_cfg)\r\n                log[f\"samples_cfg_scale_{unconditional_guidance_scale:.2f}\"] = (\r\n                    x_samples_cfg\r\n                )\r\n\r\n        return log\r\n\r\n\r\nclass LatentInpaintDiffusion(LatentFinetuneDiffusion):\r\n    \"\"\"\r\n    can either run as pure inpainting model (only concat mode) or with mixed conditionings,\r\n    e.g. mask as concat and text via cross-attn.\r\n    To disable finetuning mode, set finetune_keys to None\r\n    \"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        concat_keys=(\"mask\", \"masked_image\"),\r\n        masked_image_key=\"masked_image\",\r\n        *args,\r\n        **kwargs,\r\n    ):\r\n        super().__init__(concat_keys, *args, **kwargs)\r\n        self.masked_image_key = masked_image_key\r\n        assert self.masked_image_key in concat_keys\r\n\r\n    @torch.no_grad()\r\n    def get_input(\r\n        self, batch, k, cond_key=None, bs=None, return_first_stage_outputs=False\r\n    ):\r\n        # note: restricted to non-trainable encoders currently\r\n        assert not self.cond_stage_trainable, (\r\n            \"trainable cond stages not yet supported for inpainting\"\r\n        )\r\n        z, c, x, xrec, xc = super().get_input(\r\n            batch,\r\n            self.first_stage_key,\r\n            return_first_stage_outputs=True,\r\n            force_c_encode=True,\r\n            return_original_cond=True,\r\n            bs=bs,\r\n        )\r\n\r\n        assert exists(self.concat_keys)\r\n        c_cat = list()\r\n        for ck in self.concat_keys:\r\n            cc = (\r\n                rearrange(batch[ck], \"b h w c -> b c h w\")\r\n                .to(memory_format=torch.contiguous_format)\r\n                .float()\r\n            )\r\n            if bs is not None:\r\n                cc = cc[:bs]\r\n                cc = cc.to(self.device)\r\n            bchw = z.shape\r\n            if ck != self.masked_image_key:\r\n                cc = torch.nn.functional.interpolate(cc, size=bchw[-2:])\r\n            else:\r\n                cc = self.get_first_stage_encoding(self.encode_first_stage(cc))\r\n            c_cat.append(cc)\r\n        c_cat = torch.cat(c_cat, dim=1)\r\n        all_conds = {\"c_concat\": [c_cat], \"c_crossattn\": [c]}\r\n        if return_first_stage_outputs:\r\n            return z, all_conds, x, xrec, xc\r\n        return z, all_conds\r\n\r\n    @torch.no_grad()\r\n    def log_images(self, *args, **kwargs):\r\n        log = super(LatentInpaintDiffusion, self).log_images(*args, **kwargs)\r\n        log[\"masked_image\"] = (\r\n            rearrange(args[0][\"masked_image\"], \"b h w c -> b c h w\")\r\n            .to(memory_format=torch.contiguous_format)\r\n            .float()\r\n        )\r\n        return log\r\n\r\n\r\nclass LatentDepth2ImageDiffusion(LatentFinetuneDiffusion):\r\n    \"\"\"\r\n    condition on monocular depth estimation\r\n    \"\"\"\r\n\r\n    def __init__(self, depth_stage_config, concat_keys=(\"midas_in\",), *args, **kwargs):\r\n        super().__init__(concat_keys=concat_keys, *args, **kwargs)\r\n        self.depth_model = instantiate_from_config(depth_stage_config)\r\n        self.depth_stage_key = concat_keys[0]\r\n\r\n    @torch.no_grad()\r\n    def get_input(\r\n        self, batch, k, cond_key=None, bs=None, return_first_stage_outputs=False\r\n    ):\r\n        # note: restricted to non-trainable encoders currently\r\n        assert not self.cond_stage_trainable, (\r\n            \"trainable cond stages not yet supported for depth2img\"\r\n        )\r\n        z, c, x, xrec, xc = super().get_input(\r\n            batch,\r\n            self.first_stage_key,\r\n            return_first_stage_outputs=True,\r\n            force_c_encode=True,\r\n            return_original_cond=True,\r\n            bs=bs,\r\n        )\r\n\r\n        assert exists(self.concat_keys)\r\n        assert len(self.concat_keys) == 1\r\n        c_cat = list()\r\n        for ck in self.concat_keys:\r\n            cc = batch[ck]\r\n            if bs is not None:\r\n                cc = cc[:bs]\r\n                cc = cc.to(self.device)\r\n            cc = self.depth_model(cc)\r\n            cc = torch.nn.functional.interpolate(\r\n                cc,\r\n                size=z.shape[2:],\r\n                mode=\"bicubic\",\r\n                align_corners=False,\r\n            )\r\n\r\n            depth_min, depth_max = (\r\n                torch.amin(cc, dim=[1, 2, 3], keepdim=True),\r\n                torch.amax(cc, dim=[1, 2, 3], keepdim=True),\r\n            )\r\n            cc = 2.0 * (cc - depth_min) / (depth_max - depth_min + 0.001) - 1.0\r\n            c_cat.append(cc)\r\n        c_cat = torch.cat(c_cat, dim=1)\r\n        all_conds = {\"c_concat\": [c_cat], \"c_crossattn\": [c]}\r\n        if return_first_stage_outputs:\r\n            return z, all_conds, x, xrec, xc\r\n        return z, all_conds\r\n\r\n    @torch.no_grad()\r\n    def log_images(self, *args, **kwargs):\r\n        log = super().log_images(*args, **kwargs)\r\n        depth = self.depth_model(args[0][self.depth_stage_key])\r\n        depth_min, depth_max = (\r\n            torch.amin(depth, dim=[1, 2, 3], keepdim=True),\r\n            torch.amax(depth, dim=[1, 2, 3], keepdim=True),\r\n        )\r\n        log[\"depth\"] = 2.0 * (depth - depth_min) / (depth_max - depth_min) - 1.0\r\n        return log\r\n\r\n\r\nclass LatentUpscaleFinetuneDiffusion(LatentFinetuneDiffusion):\r\n    \"\"\"\r\n    condition on low-res image (and optionally on some spatial noise augmentation)\r\n    \"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        concat_keys=(\"lr\",),\r\n        reshuffle_patch_size=None,\r\n        low_scale_config=None,\r\n        low_scale_key=None,\r\n        *args,\r\n        **kwargs,\r\n    ):\r\n        super().__init__(concat_keys=concat_keys, *args, **kwargs)\r\n        self.reshuffle_patch_size = reshuffle_patch_size\r\n        self.low_scale_model = None\r\n        if low_scale_config is not None:\r\n            print(\"Initializing a low-scale model\")\r\n            assert exists(low_scale_key)\r\n            self.instantiate_low_stage(low_scale_config)\r\n            self.low_scale_key = low_scale_key\r\n\r\n    def instantiate_low_stage(self, config):\r\n        model = instantiate_from_config(config)\r\n        self.low_scale_model = model.eval()\r\n        self.low_scale_model.train = disabled_train\r\n        for param in self.low_scale_model.parameters():\r\n            param.requires_grad = False\r\n\r\n    @torch.no_grad()\r\n    def get_input(\r\n        self, batch, k, cond_key=None, bs=None, return_first_stage_outputs=False\r\n    ):\r\n        # note: restricted to non-trainable encoders currently\r\n        assert not self.cond_stage_trainable, (\r\n            \"trainable cond stages not yet supported for upscaling-ft\"\r\n        )\r\n        z, c, x, xrec, xc = super().get_input(\r\n            batch,\r\n            self.first_stage_key,\r\n            return_first_stage_outputs=True,\r\n            force_c_encode=True,\r\n            return_original_cond=True,\r\n            bs=bs,\r\n        )\r\n\r\n        assert exists(self.concat_keys)\r\n        assert len(self.concat_keys) == 1\r\n        # optionally make spatial noise_level here\r\n        c_cat = list()\r\n        noise_level = None\r\n        for ck in self.concat_keys:\r\n            cc = batch[ck]\r\n            cc = rearrange(cc, \"b h w c -> b c h w\")\r\n            if exists(self.reshuffle_patch_size):\r\n                assert isinstance(self.reshuffle_patch_size, int)\r\n                cc = rearrange(\r\n                    cc,\r\n                    \"b c (p1 h) (p2 w) -> b (p1 p2 c) h w\",\r\n                    p1=self.reshuffle_patch_size,\r\n                    p2=self.reshuffle_patch_size,\r\n                )\r\n            if bs is not None:\r\n                cc = cc[:bs]\r\n                cc = cc.to(self.device)\r\n            if exists(self.low_scale_model) and ck == self.low_scale_key:\r\n                cc, noise_level = self.low_scale_model(cc)\r\n            c_cat.append(cc)\r\n        c_cat = torch.cat(c_cat, dim=1)\r\n        if exists(noise_level):\r\n            all_conds = {\"c_concat\": [c_cat], \"c_crossattn\": [c], \"c_adm\": noise_level}\r\n        else:\r\n            all_conds = {\"c_concat\": [c_cat], \"c_crossattn\": [c]}\r\n        if return_first_stage_outputs:\r\n            return z, all_conds, x, xrec, xc\r\n        return z, all_conds\r\n\r\n    @torch.no_grad()\r\n    def log_images(self, *args, **kwargs):\r\n        log = super().log_images(*args, **kwargs)\r\n        log[\"lr\"] = rearrange(args[0][\"lr\"], \"b h w c -> b c h w\")\r\n        return log\r\n", "modifiedCode": "\"\"\"\r\nwild mixture of\r\nhttps://github.com/lucidrains/denoising-diffusion-pytorch/blob/7706bdfc6f527f58d33f84b7b522e61e6e3164b3/denoising_diffusion_pytorch/denoising_diffusion_pytorch.py\r\nhttps://github.com/openai/improved-diffusion/blob/e94489283bb876ac1477d5dd7709bbbd2d9902ce/improved_diffusion/gaussian_diffusion.py\r\nhttps://github.com/CompVis/taming-transformers\r\n-- merci\r\n\"\"\"\r\n\r\nimport itertools\r\nfrom contextlib import contextmanager, nullcontext\r\nfrom functools import partial\r\n\r\nimport numpy as np\r\nimport pytorch_lightning as pl\r\nimport torch\r\nimport torch.nn as nn\r\nfrom einops import rearrange, repeat\r\nfrom omegaconf import ListConfig\r\nfrom pytorch_lightning.utilities.distributed import rank_zero_only\r\nfrom torch.optim.lr_scheduler import LambdaLR\r\nfrom torchvision.utils import make_grid\r\nfrom tqdm import tqdm\r\n\r\nfrom ldm.models.autoencoder import AutoencoderKL, IdentityFirstStage\r\nfrom ldm.models.diffusion.ddim import DDIMSampler\r\nfrom ldm.modules.diffusionmodules.util import (\r\n    extract_into_tensor,\r\n    make_beta_schedule,\r\n    noise_like,\r\n)\r\nfrom ldm.modules.distributions.distributions import (\r\n    DiagonalGaussianDistribution,\r\n    normal_kl,\r\n)\r\nfrom ldm.modules.ema import LitEma\r\nfrom ldm.util import (\r\n    count_params,\r\n    default,\r\n    exists,\r\n    instantiate_from_config,\r\n    isimage,\r\n    ismap,\r\n    log_txt_as_img,\r\n    mean_flat,\r\n)\r\n\r\n__conditioning_keys__ = {\"concat\": \"c_concat\", \"crossattn\": \"c_crossattn\", \"adm\": \"y\"}\r\n\r\n\r\nclass Distance_Type:\r\n    \"\"\"Distance types for contextual loss computation.\"\"\"\r\n\r\n    L2_Distance = 0\r\n    L1_Distance = 1\r\n    Cosine_Distance = 2\r\n\r\n\r\nclass ContextualLoss(nn.Module):\r\n    \"\"\"\r\n    Sophisticated Contextual Loss implementation for CtrlColor.\r\n    Based on \"The Contextual Loss for Image Transformation with Non-Aligned Data\"\r\n\r\n    Configured for CtrlColor paper specifications:\r\n    - VGG19 layers 3 and 5 (conv_3_2, conv_5_2)\r\n    - Cosine distance\r\n    - h=0.01 bandwidth parameter\r\n    - Layer weights: w₃=2, w₅=8\r\n    \"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        layers_weights=None,\r\n        crop_quarter=False,\r\n        max_1d_size=100,\r\n        distance_type=Distance_Type.Cosine_Distance,\r\n        b=1.0,\r\n        h=0.01,\r\n    ):\r\n        super(ContextualLoss, self).__init__()\r\n\r\n        # Default to paper specifications if not provided\r\n        if layers_weights is None:\r\n            layers_weights = {\r\n                \"conv_3_2\": 2.0,  # φ³, w₃=2\r\n                \"conv_5_2\": 8.0,  # φ⁵, w₅=8\r\n            }\r\n\r\n        self.layers_weights = layers_weights\r\n        self.crop_quarter = crop_quarter\r\n        self.distance_type = distance_type\r\n        self.max_1d_size = max_1d_size\r\n        self.b = b\r\n        self.h = h  # Paper uses h=0.01\r\n\r\n        # VGG model will be set by instantiate_contextual_stage\r\n        self.vgg_model = None\r\n\r\n    def set_vgg_model(self, vgg_model):\r\n        \"\"\"Set the VGG model for feature extraction.\"\"\"\r\n        self.vgg_model = vgg_model\r\n\r\n    def forward(self, images, gt):\r\n        \"\"\"\r\n        Compute contextual loss between images and ground truth.\r\n\r\n        Args:\r\n            images: Input images tensor (B, C, H, W)\r\n            gt: Ground truth images tensor (B, C, H, W)\r\n\r\n        Returns:\r\n            Contextual loss value\r\n        \"\"\"\r\n        if self.vgg_model is None:\r\n            raise ValueError(\"VGG model not set. Call set_vgg_model() first.\")\r\n\r\n        device = images.device\r\n        loss = torch.zeros(1, device=device)\r\n\r\n        # Extract VGG features\r\n        vgg_images = self.vgg_model(images)\r\n        vgg_gt = self.vgg_model(gt)\r\n\r\n        # Compute contextual loss for each specified layer\r\n        for layer_name, weight in self.layers_weights.items():\r\n            if layer_name not in vgg_images or layer_name not in vgg_gt:\r\n                print(f\"Warning: Layer {layer_name} not found in VGG features\")\r\n                continue\r\n\r\n            feat_images = vgg_images[layer_name]\r\n            feat_gt = vgg_gt[layer_name]\r\n\r\n            N, C, H, W = feat_images.size()\r\n\r\n            # Apply random pooling if feature map is too large\r\n            if H * W > self.max_1d_size**2:\r\n                feat_images = self._random_pooling(\r\n                    feat_images, output_1d_size=self.max_1d_size\r\n                )\r\n                feat_gt = self._random_pooling(feat_gt, output_1d_size=self.max_1d_size)\r\n\r\n            # Compute contextual loss for this layer\r\n            layer_loss = self.calculate_CX_Loss(feat_images, feat_gt)\r\n            loss += layer_loss * weight\r\n\r\n        return loss\r\n\r\n    @staticmethod\r\n    def _random_sampling(tensor, n, indices):\r\n        \"\"\"Random sampling of tensor elements.\"\"\"\r\n        N, C, H, W = tensor.size()\r\n        S = H * W\r\n        tensor = tensor.view(N, C, S)\r\n        if indices is None:\r\n            indices = torch.randperm(S)[:n].contiguous().type_as(tensor).long()\r\n            indices = indices.view(1, 1, -1).expand(N, C, -1)\r\n        indices = ContextualLoss._move_to_current_device(indices)\r\n        res = torch.gather(tensor, index=indices, dim=-1)\r\n        return res, indices\r\n\r\n    @staticmethod\r\n    def _move_to_current_device(tensor):\r\n        \"\"\"Move tensor to current device.\"\"\"\r\n        if tensor.device.type == \"cuda\":\r\n            id = torch.cuda.current_device()\r\n            return tensor.cuda(id)\r\n        return tensor\r\n\r\n    @staticmethod\r\n    def _random_pooling(feats, output_1d_size=100):\r\n        \"\"\"Apply random pooling to reduce feature map size.\"\"\"\r\n        single_input = type(feats) is torch.Tensor\r\n\r\n        if single_input:\r\n            feats = [feats]\r\n\r\n        N, C, H, W = feats[0].size()\r\n        feats_sample, indices = ContextualLoss._random_sampling(\r\n            feats[0], output_1d_size**2, None\r\n        )\r\n        res = [feats_sample]\r\n\r\n        for i in range(1, len(feats)):\r\n            feats_sample, _ = ContextualLoss._random_sampling(feats[i], -1, indices)\r\n            res.append(feats_sample)\r\n\r\n        res = [\r\n            feats_sample.view(N, C, output_1d_size, output_1d_size)\r\n            for feats_sample in res\r\n        ]\r\n\r\n        if single_input:\r\n            return res[0]\r\n        return res\r\n\r\n    @staticmethod\r\n    def _create_using_L2(I_features, T_features):\r\n        \"\"\"Create distance matrix using L2 distance.\"\"\"\r\n        assert I_features.size() == T_features.size()\r\n        N, C, H, W = I_features.size()\r\n\r\n        Ivecs = I_features.view(N, C, -1)\r\n        Tvecs = T_features.view(N, C, -1)\r\n\r\n        square_I = torch.sum(Ivecs * Ivecs, dim=1, keepdim=False)\r\n        square_T = torch.sum(Tvecs * Tvecs, dim=1, keepdim=False)\r\n\r\n        raw_distance = []\r\n        for i in range(N):\r\n            Ivec, Tvec, s_I, s_T = (\r\n                Ivecs[i, ...],\r\n                Tvecs[i, ...],\r\n                square_I[i, ...],\r\n                square_T[i, ...],\r\n            )\r\n            AB = Ivec.permute(1, 0) @ Tvec\r\n            dist = s_I.view(-1, 1) + s_T.view(1, -1) - 2 * AB\r\n            raw_distance.append(dist.view(1, H, W, H * W))\r\n\r\n        raw_distance = torch.cat(raw_distance, dim=0)\r\n        raw_distance = torch.clamp(raw_distance, min=0.0)\r\n        return raw_distance\r\n\r\n    @staticmethod\r\n    def _create_using_L1(I_features, T_features):\r\n        \"\"\"Create distance matrix using L1 distance.\"\"\"\r\n        assert I_features.size() == T_features.size()\r\n        N, C, H, W = I_features.size()\r\n\r\n        Ivecs = I_features.view(N, C, -1)\r\n        Tvecs = T_features.view(N, C, -1)\r\n\r\n        raw_distance = []\r\n        for i in range(N):\r\n            Ivec, Tvec = Ivecs[i, ...], Tvecs[i, ...]\r\n            dist = torch.sum(\r\n                torch.abs(Ivec.view(C, -1, 1) - Tvec.view(C, 1, -1)),\r\n                dim=0,\r\n                keepdim=False,\r\n            )\r\n            raw_distance.append(dist.view(1, H, W, H * W))\r\n\r\n        raw_distance = torch.cat(raw_distance, dim=0)\r\n        return raw_distance\r\n\r\n    @staticmethod\r\n    def _centered_by_T(I, T):\r\n        \"\"\"Center features by T's mean.\"\"\"\r\n        mean_T = (\r\n            T.mean(dim=0, keepdim=True)\r\n            .mean(dim=2, keepdim=True)\r\n            .mean(dim=3, keepdim=True)\r\n        )\r\n        return I - mean_T, T - mean_T\r\n\r\n    @staticmethod\r\n    def _normalized_L2_channelwise(tensor):\r\n        \"\"\"Normalize tensor channelwise using L2 norm.\"\"\"\r\n        norms = tensor.norm(p=2, dim=1, keepdim=True)\r\n        return tensor / norms\r\n\r\n    @staticmethod\r\n    def _create_using_dotP(I_features, T_features):\r\n        \"\"\"Create distance matrix using cosine distance (dot product).\"\"\"\r\n        assert I_features.size() == T_features.size()\r\n        I_features, T_features = ContextualLoss._centered_by_T(I_features, T_features)\r\n        I_features = ContextualLoss._normalized_L2_channelwise(I_features)\r\n        T_features = ContextualLoss._normalized_L2_channelwise(T_features)\r\n\r\n        N, C, H, W = I_features.size()\r\n        cosine_dist = []\r\n        for i in range(N):\r\n            T_features_i = (\r\n                T_features[i].view(1, 1, C, H * W).permute(3, 2, 0, 1).contiguous()\r\n            )\r\n            I_features_i = I_features[i].unsqueeze(0)\r\n            dist = (\r\n                torch.nn.functional.conv2d(I_features_i, T_features_i)\r\n                .permute(0, 2, 3, 1)\r\n                .contiguous()\r\n            )\r\n            cosine_dist.append(dist)\r\n\r\n        cosine_dist = torch.cat(cosine_dist, dim=0)\r\n        cosine_dist = (1 - cosine_dist) / 2\r\n        cosine_dist = cosine_dist.clamp(min=0.0)\r\n        return cosine_dist\r\n\r\n    @staticmethod\r\n    def _calculate_relative_distance(raw_distance, epsilon=1e-5):\r\n        \"\"\"Normalize distances as in Eq. (2) of the paper.\"\"\"\r\n        div = torch.min(raw_distance, dim=-1, keepdim=True)[0]\r\n        relative_dist = raw_distance / (div + epsilon)\r\n        return relative_dist\r\n\r\n    def calculate_CX_Loss(self, I_features, T_features):\r\n        \"\"\"Calculate the contextual loss between I and T features.\"\"\"\r\n        I_features = ContextualLoss._move_to_current_device(I_features)\r\n        T_features = ContextualLoss._move_to_current_device(T_features)\r\n\r\n        # Check for NaN or Inf\r\n        if torch.sum(torch.isnan(I_features)) == torch.numel(I_features) or torch.sum(\r\n            torch.isinf(I_features)\r\n        ) == torch.numel(I_features):\r\n            raise ValueError(\"NaN or Inf in I_features\")\r\n        if torch.sum(torch.isnan(T_features)) == torch.numel(T_features) or torch.sum(\r\n            torch.isinf(T_features)\r\n        ) == torch.numel(T_features):\r\n            raise ValueError(\"NaN or Inf in T_features\")\r\n\r\n        # Compute raw distance based on distance type\r\n        if self.distance_type == Distance_Type.L1_Distance:\r\n            raw_distance = ContextualLoss._create_using_L1(I_features, T_features)\r\n        elif self.distance_type == Distance_Type.L2_Distance:\r\n            raw_distance = ContextualLoss._create_using_L2(I_features, T_features)\r\n        else:  # Cosine distance (paper default)\r\n            raw_distance = ContextualLoss._create_using_dotP(I_features, T_features)\r\n\r\n        # Calculate relative distance\r\n        relative_distance = ContextualLoss._calculate_relative_distance(raw_distance)\r\n        del raw_distance\r\n\r\n        # Apply exponential weighting\r\n        exp_distance = torch.exp((self.b - relative_distance) / self.h)\r\n        del relative_distance\r\n\r\n        # Calculate contextual similarity\r\n        contextual_sim = exp_distance / torch.sum(exp_distance, dim=-1, keepdim=True)\r\n        del exp_distance\r\n\r\n        # Calculate final loss\r\n        max_gt_sim = torch.max(torch.max(contextual_sim, dim=1)[0], dim=1)[0]\r\n        del contextual_sim\r\n        CS = torch.mean(max_gt_sim, dim=1)\r\n        CX_loss = torch.mean(-torch.log(CS))\r\n\r\n        if torch.isnan(CX_loss):\r\n            raise ValueError(\"NaN in computing CX_loss\")\r\n\r\n        return CX_loss\r\n\r\n\r\ndef disabled_train(self, mode=True):\r\n    \"\"\"Overwrite model.train with this function to make sure train/eval mode\r\n    does not change anymore.\"\"\"\r\n    return self\r\n\r\n\r\ndef uniform_on_device(r1, r2, shape, device):\r\n    return (r1 - r2) * torch.rand(*shape, device=device) + r2\r\n\r\n\r\ndef prepare_mask_latents(\r\n    mask,\r\n    masked_image,\r\n    batch_size,\r\n    height,\r\n    width,\r\n    dtype,\r\n    device,\r\n    generator,\r\n    do_classifier_free_guidance,\r\n    vae,\r\n    vae_scale_factor,\r\n):\r\n    # resize the mask to latents shape as we concatenate the mask to the latents\r\n    # we do that before converting to dtype to avoid breaking in case we're using cpu_offload\r\n    # and half precision\r\n    mask = torch.nn.functional.interpolate(\r\n        mask, size=(height // vae_scale_factor, width // vae_scale_factor)\r\n    )\r\n    mask = mask.to(device=device, dtype=dtype)\r\n\r\n    masked_image = masked_image.to(device=device, dtype=dtype)\r\n\r\n    # encode the mask image into latents space so we can concatenate it to the latents\r\n    if isinstance(generator, list):\r\n        masked_image_latents = [\r\n            vae.encode(masked_image[i : i + 1]).latent_dist.sample(\r\n                generator=generator[i]\r\n            )\r\n            for i in range(batch_size)\r\n        ]\r\n        masked_image_latents = torch.cat(masked_image_latents, dim=0)\r\n    else:\r\n        masked_image_latents = vae.encode(masked_image).latent_dist.sample(\r\n            generator=generator\r\n        )\r\n    masked_image_latents = vae.config.scaling_factor * masked_image_latents\r\n\r\n    # duplicate mask and masked_image_latents for each generation per prompt, using mps friendly method\r\n    if mask.shape[0] < batch_size:\r\n        if not batch_size % mask.shape[0] == 0:\r\n            raise ValueError(\r\n                \"The passed mask and the required batch size don't match. Masks are supposed to be duplicated to\"\r\n                f\" a total batch size of {batch_size}, but {mask.shape[0]} masks were passed. Make sure the number\"\r\n                \" of masks that you pass is divisible by the total requested batch size.\"\r\n            )\r\n        mask = mask.repeat(batch_size // mask.shape[0], 1, 1, 1)\r\n    if masked_image_latents.shape[0] < batch_size:\r\n        if not batch_size % masked_image_latents.shape[0] == 0:\r\n            raise ValueError(\r\n                \"The passed images and the required batch size don't match. Images are supposed to be duplicated\"\r\n                f\" to a total batch size of {batch_size}, but {masked_image_latents.shape[0]} images were passed.\"\r\n                \" Make sure the number of images that you pass is divisible by the total requested batch size.\"\r\n            )\r\n        masked_image_latents = masked_image_latents.repeat(\r\n            batch_size // masked_image_latents.shape[0], 1, 1, 1\r\n        )\r\n\r\n    mask = torch.cat([mask] * 2) if do_classifier_free_guidance else mask\r\n    masked_image_latents = (\r\n        torch.cat([masked_image_latents] * 2)\r\n        if do_classifier_free_guidance\r\n        else masked_image_latents\r\n    )\r\n\r\n    # aligning device to prevent device errors when concating it with the latent model input\r\n    masked_image_latents = masked_image_latents.to(device=device, dtype=dtype)\r\n    return mask, masked_image_latents\r\n\r\n\r\nclass DDPM(pl.LightningModule):\r\n    # classic DDPM with Gaussian diffusion, in image space\r\n    def __init__(\r\n        self,\r\n        unet_config,\r\n        timesteps=1000,\r\n        beta_schedule=\"linear\",\r\n        loss_type=\"l2\",\r\n        ckpt_path=None,\r\n        ignore_keys=[],\r\n        load_only_unet=False,\r\n        monitor=\"val/loss\",\r\n        use_ema=True,\r\n        first_stage_key=\"image\",\r\n        image_size=256,\r\n        channels=3,\r\n        log_every_t=100,\r\n        clip_denoised=True,\r\n        linear_start=1e-4,\r\n        linear_end=2e-2,\r\n        cosine_s=8e-3,\r\n        given_betas=None,\r\n        original_elbo_weight=0.0,\r\n        v_posterior=0.0,  # weight for choosing posterior variance as sigma = (1-v) * beta_tilde + v * beta\r\n        l_simple_weight=1.0,\r\n        conditioning_key=None,\r\n        parameterization=\"eps\",  # all assuming fixed variance schedules\r\n        scheduler_config=None,\r\n        use_positional_encodings=False,\r\n        learn_logvar=False,\r\n        logvar_init=0.0,\r\n        make_it_fit=False,\r\n        ucg_training=None,\r\n        reset_ema=False,\r\n        reset_num_ema_updates=False,\r\n    ):\r\n        super().__init__()\r\n        assert parameterization in [\"eps\", \"x0\", \"v\"], (\r\n            'currently only supporting \"eps\" and \"x0\" and \"v\"'\r\n        )\r\n        self.parameterization = parameterization\r\n        print(\r\n            f\"{self.__class__.__name__}: Running in {self.parameterization}-prediction mode\"\r\n        )\r\n        self.cond_stage_model = None\r\n        self.clip_denoised = clip_denoised\r\n        self.log_every_t = log_every_t\r\n        self.first_stage_key = first_stage_key\r\n        self.image_size = image_size  # try conv?\r\n        self.channels = channels\r\n        self.use_positional_encodings = use_positional_encodings\r\n        self.model = DiffusionWrapper(unet_config, conditioning_key)\r\n        count_params(self.model, verbose=True)\r\n        self.use_ema = use_ema\r\n        if self.use_ema:\r\n            self.model_ema = LitEma(self.model)\r\n            print(f\"Keeping EMAs of {len(list(self.model_ema.buffers()))}.\")\r\n\r\n        self.use_scheduler = scheduler_config is not None\r\n        if self.use_scheduler:\r\n            self.scheduler_config = scheduler_config\r\n\r\n        self.v_posterior = v_posterior\r\n        self.original_elbo_weight = original_elbo_weight\r\n        self.l_simple_weight = l_simple_weight\r\n\r\n        if monitor is not None:\r\n            self.monitor = monitor\r\n        self.make_it_fit = make_it_fit\r\n        if reset_ema:\r\n            assert exists(ckpt_path)\r\n        if ckpt_path is not None:\r\n            self.init_from_ckpt(\r\n                ckpt_path, ignore_keys=ignore_keys, only_model=load_only_unet\r\n            )\r\n            if reset_ema:\r\n                assert self.use_ema\r\n                print(\r\n                    \"Resetting ema to pure model weights. This is useful when restoring from an ema-only checkpoint.\"\r\n                )\r\n                self.model_ema = LitEma(self.model)\r\n        if reset_num_ema_updates:\r\n            print(\r\n                \" +++++++++++ WARNING: RESETTING NUM_EMA UPDATES TO ZERO +++++++++++ \"\r\n            )\r\n            assert self.use_ema\r\n            self.model_ema.reset_num_updates()\r\n\r\n        self.register_schedule(\r\n            given_betas=given_betas,\r\n            beta_schedule=beta_schedule,\r\n            timesteps=timesteps,\r\n            linear_start=linear_start,\r\n            linear_end=linear_end,\r\n            cosine_s=cosine_s,\r\n        )\r\n\r\n        self.loss_type = loss_type\r\n\r\n        self.learn_logvar = learn_logvar\r\n        logvar = torch.full(fill_value=logvar_init, size=(self.num_timesteps,))\r\n        if self.learn_logvar:\r\n            self.logvar = nn.Parameter(logvar, requires_grad=True)\r\n        else:\r\n            self.register_buffer(\"logvar\", logvar)\r\n\r\n        self.ucg_training = ucg_training or dict()\r\n        if self.ucg_training:\r\n            self.ucg_prng = np.random.RandomState()\r\n\r\n    def register_schedule(\r\n        self,\r\n        given_betas=None,\r\n        beta_schedule=\"linear\",\r\n        timesteps=1000,\r\n        linear_start=1e-4,\r\n        linear_end=2e-2,\r\n        cosine_s=8e-3,\r\n    ):\r\n        if exists(given_betas):\r\n            betas = given_betas\r\n        else:\r\n            betas = make_beta_schedule(\r\n                beta_schedule,\r\n                timesteps,\r\n                linear_start=linear_start,\r\n                linear_end=linear_end,\r\n                cosine_s=cosine_s,\r\n            )\r\n        alphas = 1.0 - betas\r\n        alphas_cumprod = np.cumprod(alphas, axis=0)\r\n        alphas_cumprod_prev = np.append(1.0, alphas_cumprod[:-1])\r\n\r\n        (timesteps,) = betas.shape\r\n        self.num_timesteps = int(timesteps)\r\n        self.linear_start = linear_start\r\n        self.linear_end = linear_end\r\n        assert alphas_cumprod.shape[0] == self.num_timesteps, (\r\n            \"alphas have to be defined for each timestep\"\r\n        )\r\n\r\n        to_torch = partial(torch.tensor, dtype=torch.float32)\r\n\r\n        self.register_buffer(\"betas\", to_torch(betas))\r\n        self.register_buffer(\"alphas_cumprod\", to_torch(alphas_cumprod))\r\n        self.register_buffer(\"alphas_cumprod_prev\", to_torch(alphas_cumprod_prev))\r\n\r\n        # calculations for diffusion q(x_t | x_{t-1}) and others\r\n        self.register_buffer(\"sqrt_alphas_cumprod\", to_torch(np.sqrt(alphas_cumprod)))\r\n        self.register_buffer(\r\n            \"sqrt_one_minus_alphas_cumprod\", to_torch(np.sqrt(1.0 - alphas_cumprod))\r\n        )\r\n        self.register_buffer(\r\n            \"log_one_minus_alphas_cumprod\", to_torch(np.log(1.0 - alphas_cumprod))\r\n        )\r\n        self.register_buffer(\r\n            \"sqrt_recip_alphas_cumprod\", to_torch(np.sqrt(1.0 / alphas_cumprod))\r\n        )\r\n        self.register_buffer(\r\n            \"sqrt_recipm1_alphas_cumprod\", to_torch(np.sqrt(1.0 / alphas_cumprod - 1))\r\n        )\r\n\r\n        # calculations for posterior q(x_{t-1} | x_t, x_0)\r\n        posterior_variance = (1 - self.v_posterior) * betas * (\r\n            1.0 - alphas_cumprod_prev\r\n        ) / (1.0 - alphas_cumprod) + self.v_posterior * betas\r\n        # above: equal to 1. / (1. / (1. - alpha_cumprod_tm1) + alpha_t / beta_t)\r\n        self.register_buffer(\"posterior_variance\", to_torch(posterior_variance))\r\n        # below: log calculation clipped because the posterior variance is 0 at the beginning of the diffusion chain\r\n        self.register_buffer(\r\n            \"posterior_log_variance_clipped\",\r\n            to_torch(np.log(np.maximum(posterior_variance, 1e-20))),\r\n        )\r\n        self.register_buffer(\r\n            \"posterior_mean_coef1\",\r\n            to_torch(betas * np.sqrt(alphas_cumprod_prev) / (1.0 - alphas_cumprod)),\r\n        )\r\n        self.register_buffer(\r\n            \"posterior_mean_coef2\",\r\n            to_torch(\r\n                (1.0 - alphas_cumprod_prev) * np.sqrt(alphas) / (1.0 - alphas_cumprod)\r\n            ),\r\n        )\r\n\r\n        if self.parameterization == \"eps\":\r\n            lvlb_weights = self.betas**2 / (\r\n                2\r\n                * self.posterior_variance\r\n                * to_torch(alphas)\r\n                * (1 - self.alphas_cumprod)\r\n            )\r\n        elif self.parameterization == \"x0\":\r\n            lvlb_weights = (\r\n                0.5\r\n                * np.sqrt(torch.Tensor(alphas_cumprod))\r\n                / (2.0 * 1 - torch.Tensor(alphas_cumprod))\r\n            )\r\n        elif self.parameterization == \"v\":\r\n            lvlb_weights = torch.ones_like(\r\n                self.betas**2\r\n                / (\r\n                    2\r\n                    * self.posterior_variance\r\n                    * to_torch(alphas)\r\n                    * (1 - self.alphas_cumprod)\r\n                )\r\n            )\r\n        else:\r\n            raise NotImplementedError(\"mu not supported\")\r\n        lvlb_weights[0] = lvlb_weights[1]\r\n        self.register_buffer(\"lvlb_weights\", lvlb_weights, persistent=False)\r\n        assert not torch.isnan(self.lvlb_weights).all()\r\n\r\n    @contextmanager\r\n    def ema_scope(self, context=None):\r\n        if self.use_ema:\r\n            self.model_ema.store(self.model.parameters())\r\n            self.model_ema.copy_to(self.model)\r\n            if context is not None:\r\n                print(f\"{context}: Switched to EMA weights\")\r\n        try:\r\n            yield None\r\n        finally:\r\n            if self.use_ema:\r\n                self.model_ema.restore(self.model.parameters())\r\n                if context is not None:\r\n                    print(f\"{context}: Restored training weights\")\r\n\r\n    @torch.no_grad()\r\n    def init_from_ckpt(self, path, ignore_keys=list(), only_model=False):\r\n        sd = torch.load(path, map_location=\"cpu\")\r\n        if \"state_dict\" in list(sd.keys()):\r\n            sd = sd[\"state_dict\"]\r\n        keys = list(sd.keys())\r\n        for k in keys:\r\n            for ik in ignore_keys:\r\n                if k.startswith(ik):\r\n                    print(\"Deleting key {} from state_dict.\".format(k))\r\n                    del sd[k]\r\n        if self.make_it_fit:\r\n            n_params = len(\r\n                [\r\n                    name\r\n                    for name, _ in itertools.chain(\r\n                        self.named_parameters(), self.named_buffers()\r\n                    )\r\n                ]\r\n            )\r\n            for name, param in tqdm(\r\n                itertools.chain(self.named_parameters(), self.named_buffers()),\r\n                desc=\"Fitting old weights to new weights\",\r\n                total=n_params,\r\n            ):\r\n                if name not in sd:\r\n                    continue\r\n                old_shape = sd[name].shape\r\n                new_shape = param.shape\r\n                assert len(old_shape) == len(new_shape)\r\n                if len(new_shape) > 2:\r\n                    # we only modify first two axes\r\n                    assert new_shape[2:] == old_shape[2:]\r\n                # assumes first axis corresponds to output dim\r\n                if not new_shape == old_shape:\r\n                    new_param = param.clone()\r\n                    old_param = sd[name]\r\n                    if len(new_shape) == 1:\r\n                        for i in range(new_param.shape[0]):\r\n                            new_param[i] = old_param[i % old_shape[0]]\r\n                    elif len(new_shape) >= 2:\r\n                        for i in range(new_param.shape[0]):\r\n                            for j in range(new_param.shape[1]):\r\n                                new_param[i, j] = old_param[\r\n                                    i % old_shape[0], j % old_shape[1]\r\n                                ]\r\n\r\n                        n_used_old = torch.ones(old_shape[1])\r\n                        for j in range(new_param.shape[1]):\r\n                            n_used_old[j % old_shape[1]] += 1\r\n                        n_used_new = torch.zeros(new_shape[1])\r\n                        for j in range(new_param.shape[1]):\r\n                            n_used_new[j] = n_used_old[j % old_shape[1]]\r\n\r\n                        n_used_new = n_used_new[None, :]\r\n                        while len(n_used_new.shape) < len(new_shape):\r\n                            n_used_new = n_used_new.unsqueeze(-1)\r\n                        new_param /= n_used_new\r\n\r\n                    sd[name] = new_param\r\n\r\n        missing, unexpected = (\r\n            self.load_state_dict(sd, strict=False)\r\n            if not only_model\r\n            else self.model.load_state_dict(sd, strict=False)\r\n        )\r\n        print(\r\n            f\"Restored from {path} with {len(missing)} missing and {len(unexpected)} unexpected keys\"\r\n        )\r\n        if len(missing) > 0:\r\n            print(f\"Missing Keys:\\n {missing}\")\r\n        if len(unexpected) > 0:\r\n            print(f\"\\nUnexpected Keys:\\n {unexpected}\")\r\n\r\n    def q_mean_variance(self, x_start, t):\r\n        \"\"\"\r\n        Get the distribution q(x_t | x_0).\r\n        :param x_start: the [N x C x ...] tensor of noiseless inputs.\r\n        :param t: the number of diffusion steps (minus 1). Here, 0 means one step.\r\n        :return: A tuple (mean, variance, log_variance), all of x_start's shape.\r\n        \"\"\"\r\n        mean = extract_into_tensor(self.sqrt_alphas_cumprod, t, x_start.shape) * x_start\r\n        variance = extract_into_tensor(1.0 - self.alphas_cumprod, t, x_start.shape)\r\n        log_variance = extract_into_tensor(\r\n            self.log_one_minus_alphas_cumprod, t, x_start.shape\r\n        )\r\n        return mean, variance, log_variance\r\n\r\n    def predict_start_from_noise(self, x_t, t, noise):\r\n        return (\r\n            extract_into_tensor(self.sqrt_recip_alphas_cumprod, t, x_t.shape) * x_t\r\n            - extract_into_tensor(self.sqrt_recipm1_alphas_cumprod, t, x_t.shape)\r\n            * noise\r\n        )\r\n\r\n    def predict_start_from_z_and_v(self, x_t, t, v):\r\n        # self.register_buffer('sqrt_alphas_cumprod', to_torch(np.sqrt(alphas_cumprod)))\r\n        # self.register_buffer('sqrt_one_minus_alphas_cumprod', to_torch(np.sqrt(1. - alphas_cumprod)))\r\n        return (\r\n            extract_into_tensor(self.sqrt_alphas_cumprod, t, x_t.shape) * x_t\r\n            - extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, x_t.shape) * v\r\n        )\r\n\r\n    # def get_x_t_from_start_and_t(self, start, t, v):\r\n    #     return (\r\n    #             (start+extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, start.shape) * v)/extract_into_tensor(self.sqrt_alphas_cumprod, t, start.shape)\r\n    #     )\r\n\r\n    def predict_eps_from_z_and_v(self, x_t, t, v):\r\n        return (\r\n            extract_into_tensor(self.sqrt_alphas_cumprod, t, x_t.shape) * v\r\n            + extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, x_t.shape)\r\n            * x_t\r\n        )\r\n\r\n    def q_posterior(self, x_start, x_t, t):\r\n        posterior_mean = (\r\n            extract_into_tensor(self.posterior_mean_coef1, t, x_t.shape) * x_start\r\n            + extract_into_tensor(self.posterior_mean_coef2, t, x_t.shape) * x_t\r\n        )\r\n        posterior_variance = extract_into_tensor(self.posterior_variance, t, x_t.shape)\r\n        posterior_log_variance_clipped = extract_into_tensor(\r\n            self.posterior_log_variance_clipped, t, x_t.shape\r\n        )\r\n        return posterior_mean, posterior_variance, posterior_log_variance_clipped\r\n\r\n    def p_mean_variance(self, x, t, clip_denoised: bool):\r\n        model_out = self.model(x, t)\r\n        if self.parameterization == \"eps\":\r\n            x_recon = self.predict_start_from_noise(x, t=t, noise=model_out)\r\n        elif self.parameterization == \"x0\":\r\n            x_recon = model_out\r\n        if clip_denoised:\r\n            x_recon.clamp_(-1.0, 1.0)\r\n\r\n        model_mean, posterior_variance, posterior_log_variance = self.q_posterior(\r\n            x_start=x_recon, x_t=x, t=t\r\n        )\r\n        return model_mean, posterior_variance, posterior_log_variance\r\n\r\n    @torch.no_grad()\r\n    def p_sample(self, x, t, clip_denoised=True, repeat_noise=False):\r\n        b, *_, device = *x.shape, x.device\r\n        model_mean, _, model_log_variance = self.p_mean_variance(\r\n            x=x, t=t, clip_denoised=clip_denoised\r\n        )\r\n        noise = noise_like(x.shape, device, repeat_noise)\r\n        # no noise when t == 0\r\n        nonzero_mask = (1 - (t == 0).float()).reshape(b, *((1,) * (len(x.shape) - 1)))\r\n        return model_mean + nonzero_mask * (0.5 * model_log_variance).exp() * noise\r\n\r\n    @torch.no_grad()\r\n    def p_sample_loop(self, shape, return_intermediates=False):\r\n        device = self.betas.device\r\n        b = shape[0]\r\n        img = torch.randn(shape, device=device)\r\n        intermediates = [img]\r\n        for i in tqdm(\r\n            reversed(range(0, self.num_timesteps)),\r\n            desc=\"Sampling t\",\r\n            total=self.num_timesteps,\r\n        ):\r\n            img = self.p_sample(\r\n                img,\r\n                torch.full((b,), i, device=device, dtype=torch.long),\r\n                clip_denoised=self.clip_denoised,\r\n            )\r\n            if i % self.log_every_t == 0 or i == self.num_timesteps - 1:\r\n                intermediates.append(img)\r\n        if return_intermediates:\r\n            return img, intermediates\r\n        return img\r\n\r\n    @torch.no_grad()\r\n    def sample(self, batch_size=16, return_intermediates=False):\r\n        image_size = self.image_size\r\n        channels = self.channels\r\n        return self.p_sample_loop(\r\n            (batch_size, channels, image_size, image_size),\r\n            return_intermediates=return_intermediates,\r\n        )\r\n\r\n    def q_sample(self, x_start, t, noise=None):\r\n        noise = default(noise, lambda: torch.randn_like(x_start))\r\n        return (\r\n            extract_into_tensor(self.sqrt_alphas_cumprod, t, x_start.shape) * x_start\r\n            + extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, x_start.shape)\r\n            * noise\r\n        )\r\n\r\n    def get_v(self, x, noise, t):\r\n        return (\r\n            extract_into_tensor(self.sqrt_alphas_cumprod, t, x.shape) * noise\r\n            - extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, x.shape) * x\r\n        )\r\n\r\n    def get_loss(self, pred, target, mean=True):\r\n        if self.loss_type == \"l1\":\r\n            loss = (target - pred).abs()\r\n            if mean:\r\n                loss = loss.mean()\r\n        elif self.loss_type == \"l2\":\r\n            if mean:\r\n                loss = torch.nn.functional.mse_loss(target, pred)\r\n            else:\r\n                loss = torch.nn.functional.mse_loss(target, pred, reduction=\"none\")\r\n        else:\r\n            raise NotImplementedError(\"unknown loss type '{loss_type}'\")\r\n\r\n        return loss\r\n\r\n    def p_losses(self, x_start, t, noise=None):\r\n        noise = default(noise, lambda: torch.randn_like(x_start))\r\n        x_noisy = self.q_sample(x_start=x_start, t=t, noise=noise)\r\n        model_out = self.model(x_noisy, t)\r\n\r\n        loss_dict = {}\r\n        if self.parameterization == \"eps\":\r\n            target = noise\r\n        elif self.parameterization == \"x0\":\r\n            target = x_start\r\n        elif self.parameterization == \"v\":\r\n            target = self.get_v(x_start, noise, t)\r\n        else:\r\n            raise NotImplementedError(\r\n                f\"Parameterization {self.parameterization} not yet supported\"\r\n            )\r\n\r\n        loss = self.get_loss(model_out, target, mean=False).mean(dim=[1, 2, 3])\r\n\r\n        log_prefix = \"train\" if self.training else \"val\"\r\n\r\n        loss_dict.update({f\"{log_prefix}/loss_simple\": loss.mean()})\r\n        loss_simple = loss.mean() * self.l_simple_weight\r\n\r\n        loss_vlb = (self.lvlb_weights[t] * loss).mean()\r\n        loss_dict.update({f\"{log_prefix}/loss_vlb\": loss_vlb})\r\n\r\n        loss = loss_simple + self.original_elbo_weight * loss_vlb\r\n\r\n        loss_dict.update({f\"{log_prefix}/loss\": loss})\r\n\r\n        return loss, loss_dict\r\n\r\n    def forward(self, x, *args, **kwargs):\r\n        # b, c, h, w, device, img_size, = *x.shape, x.device, self.image_size\r\n        # assert h == img_size and w == img_size, f'height and width of image must be {img_size}'\r\n        t = torch.randint(\r\n            0, self.num_timesteps, (x.shape[0],), device=self.device\r\n        ).long()\r\n        return self.p_losses(x, t, *args, **kwargs)\r\n\r\n    def get_input(self, batch, k):\r\n        x = batch[k]\r\n        if len(x.shape) == 3:\r\n            x = x[..., None]\r\n        x = rearrange(x, \"b h w c -> b c h w\")\r\n        x = x.to(memory_format=torch.contiguous_format).float()\r\n        return x\r\n\r\n    def shared_step(self, batch):\r\n        x = self.get_input(batch, self.first_stage_key)\r\n        loss, loss_dict = self(x)\r\n        return loss, loss_dict\r\n\r\n    def training_step(self, batch, batch_idx):\r\n        for k in self.ucg_training:\r\n            p = self.ucg_training[k][\"p\"]\r\n            val = self.ucg_training[k][\"val\"]\r\n            if val is None:\r\n                val = \"\"\r\n            for i in range(len(batch[k])):\r\n                if self.ucg_prng.choice(2, p=[1 - p, p]):\r\n                    batch[k][i] = val\r\n\r\n        loss, loss_dict = self.shared_step(batch)\r\n\r\n        self.log_dict(\r\n            loss_dict, prog_bar=True, logger=True, on_step=True, on_epoch=True\r\n        )\r\n\r\n        self.log(\r\n            \"global_step\",\r\n            self.global_step,\r\n            prog_bar=True,\r\n            logger=True,\r\n            on_step=True,\r\n            on_epoch=False,\r\n        )\r\n\r\n        if self.use_scheduler:\r\n            lr = self.optimizers().param_groups[0][\"lr\"]\r\n            self.log(\r\n                \"lr_abs\", lr, prog_bar=True, logger=True, on_step=True, on_epoch=False\r\n            )\r\n\r\n        return loss\r\n\r\n    @torch.no_grad()\r\n    def validation_step(self, batch, batch_idx):\r\n        _, loss_dict_no_ema = self.shared_step(batch)\r\n        with self.ema_scope():\r\n            _, loss_dict_ema = self.shared_step(batch)\r\n            loss_dict_ema = {key + \"_ema\": loss_dict_ema[key] for key in loss_dict_ema}\r\n        self.log_dict(\r\n            loss_dict_no_ema, prog_bar=False, logger=True, on_step=False, on_epoch=True\r\n        )\r\n        self.log_dict(\r\n            loss_dict_ema, prog_bar=False, logger=True, on_step=False, on_epoch=True\r\n        )\r\n\r\n    def on_train_batch_end(self, *args, **kwargs):\r\n        if self.use_ema:\r\n            self.model_ema(self.model)\r\n\r\n    def _get_rows_from_list(self, samples):\r\n        n_imgs_per_row = len(samples)\r\n        denoise_grid = rearrange(samples, \"n b c h w -> b n c h w\")\r\n        denoise_grid = rearrange(denoise_grid, \"b n c h w -> (b n) c h w\")\r\n        denoise_grid = make_grid(denoise_grid, nrow=n_imgs_per_row)\r\n        return denoise_grid\r\n\r\n    @torch.no_grad()\r\n    def log_images(self, batch, N=8, n_row=2, sample=True, return_keys=None, **kwargs):\r\n        log = dict()\r\n        x = self.get_input(batch, self.first_stage_key)\r\n        N = min(x.shape[0], N)\r\n        n_row = min(x.shape[0], n_row)\r\n        x = x.to(self.device)[:N]\r\n        log[\"inputs\"] = x\r\n\r\n        # get diffusion row\r\n        diffusion_row = list()\r\n        x_start = x[:n_row]\r\n\r\n        for t in range(self.num_timesteps):\r\n            if t % self.log_every_t == 0 or t == self.num_timesteps - 1:\r\n                t = repeat(torch.tensor([t]), \"1 -> b\", b=n_row)\r\n                t = t.to(self.device).long()\r\n                noise = torch.randn_like(x_start)\r\n                x_noisy = self.q_sample(x_start=x_start, t=t, noise=noise)\r\n                diffusion_row.append(x_noisy)\r\n\r\n        log[\"diffusion_row\"] = self._get_rows_from_list(diffusion_row)\r\n\r\n        if sample:\r\n            # get denoise row\r\n            with self.ema_scope(\"Plotting\"):\r\n                samples, denoise_row = self.sample(\r\n                    batch_size=N, return_intermediates=True\r\n                )\r\n\r\n            log[\"samples\"] = samples\r\n            log[\"denoise_row\"] = self._get_rows_from_list(denoise_row)\r\n\r\n        if return_keys:\r\n            if np.intersect1d(list(log.keys()), return_keys).shape[0] == 0:\r\n                return log\r\n            else:\r\n                return {key: log[key] for key in return_keys}\r\n        return log\r\n\r\n    def configure_optimizers(self):\r\n        lr = self.learning_rate\r\n        params = list(self.model.parameters())\r\n        if self.learn_logvar:\r\n            params = params + [self.logvar]\r\n        opt = torch.optim.AdamW(params, lr=lr)\r\n        return opt\r\n\r\n\r\nclass LatentDiffusion(DDPM):\r\n    \"\"\"main class\"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        first_stage_config,\r\n        cond_stage_config,\r\n        contextual_stage_config,\r\n        num_timesteps_cond=None,\r\n        cond_stage_key=\"image\",\r\n        cond_stage_trainable=False,\r\n        concat_mode=True,\r\n        cond_stage_forward=None,\r\n        conditioning_key=None,\r\n        scale_factor=1.0,\r\n        scale_by_std=False,\r\n        force_null_conditioning=False,\r\n        masked_image=None,\r\n        mask=None,\r\n        load_loss=False,\r\n        *args,\r\n        **kwargs,\r\n    ):\r\n        self.masked_image = masked_image\r\n        self.mask = mask\r\n        self.load_loss = load_loss\r\n        self.force_null_conditioning = force_null_conditioning\r\n        self.num_timesteps_cond = default(num_timesteps_cond, 1)\r\n        self.scale_by_std = scale_by_std\r\n        assert self.num_timesteps_cond <= kwargs[\"timesteps\"]\r\n        # for backwards compatibility after implementation of DiffusionWrapper\r\n        if conditioning_key is None:\r\n            conditioning_key = \"concat\" if concat_mode else \"crossattn\"\r\n        if (\r\n            cond_stage_config == \"__is_unconditional__\"\r\n            and not self.force_null_conditioning\r\n        ):\r\n            conditioning_key = None\r\n        ckpt_path = kwargs.pop(\"ckpt_path\", None)\r\n        reset_ema = kwargs.pop(\"reset_ema\", False)\r\n        reset_num_ema_updates = kwargs.pop(\"reset_num_ema_updates\", False)\r\n        ignore_keys = kwargs.pop(\"ignore_keys\", [])\r\n        # print(conditioning_key)\r\n        super().__init__(conditioning_key=conditioning_key, *args, **kwargs)\r\n        self.concat_mode = concat_mode\r\n        self.cond_stage_trainable = cond_stage_trainable\r\n        self.cond_stage_key = cond_stage_key\r\n        try:\r\n            self.num_downs = len(first_stage_config.params.ddconfig.ch_mult) - 1\r\n        except (AttributeError, KeyError):\r\n            self.num_downs = 0\r\n        if not scale_by_std:\r\n            self.scale_factor = scale_factor\r\n        else:\r\n            self.register_buffer(\"scale_factor\", torch.tensor(scale_factor))\r\n        self.instantiate_first_stage(first_stage_config)\r\n        self.instantiate_cond_stage(cond_stage_config)\r\n        self.instantiate_contextual_stage(contextual_stage_config)\r\n        self.cond_stage_forward = cond_stage_forward\r\n        self.clip_denoised = False\r\n        self.bbox_tokenizer = None\r\n\r\n        self.restarted_from_ckpt = False\r\n        if ckpt_path is not None:\r\n            self.init_from_ckpt(ckpt_path, ignore_keys)\r\n            self.restarted_from_ckpt = True\r\n            if reset_ema:\r\n                assert self.use_ema\r\n                print(\r\n                    \"Resetting ema to pure model weights. This is useful when restoring from an ema-only checkpoint.\"\r\n                )\r\n                self.model_ema = LitEma(self.model)\r\n        if reset_num_ema_updates:\r\n            print(\r\n                \" +++++++++++ WARNING: RESETTING NUM_EMA UPDATES TO ZERO +++++++++++ \"\r\n            )\r\n            assert self.use_ema\r\n            self.model_ema.reset_num_updates()\r\n\r\n    def make_cond_schedule(\r\n        self,\r\n    ):\r\n        self.cond_ids = torch.full(\r\n            size=(self.num_timesteps,),\r\n            fill_value=self.num_timesteps - 1,\r\n            dtype=torch.long,\r\n        )\r\n        ids = torch.round(\r\n            torch.linspace(0, self.num_timesteps - 1, self.num_timesteps_cond)\r\n        ).long()\r\n        self.cond_ids[: self.num_timesteps_cond] = ids\r\n\r\n    @rank_zero_only\r\n    @torch.no_grad()\r\n    def on_train_batch_start(self, batch, batch_idx, dataloader_idx):\r\n        # only for very first batch\r\n        if (\r\n            self.scale_by_std\r\n            and self.current_epoch == 0\r\n            and self.global_step == 0\r\n            and batch_idx == 0\r\n            and not self.restarted_from_ckpt\r\n        ):\r\n            assert self.scale_factor == 1.0, (\r\n                \"rather not use custom rescaling and std-rescaling simultaneously\"\r\n            )\r\n            # set rescale weight to 1./std of encodings\r\n            print(\"### USING STD-RESCALING ###\")\r\n            x = super().get_input(batch, self.first_stage_key)\r\n            x = x.to(self.device)\r\n            encoder_posterior = self.encode_first_stage(x)\r\n            z = self.get_first_stage_encoding(encoder_posterior).detach()\r\n            del self.scale_factor\r\n            self.register_buffer(\"scale_factor\", 1.0 / z.flatten().std())\r\n            print(f\"setting self.scale_factor to {self.scale_factor}\")\r\n            print(\"### USING STD-RESCALING ###\")\r\n\r\n    def register_schedule(\r\n        self,\r\n        given_betas=None,\r\n        beta_schedule=\"linear\",\r\n        timesteps=1000,\r\n        linear_start=1e-4,\r\n        linear_end=2e-2,\r\n        cosine_s=8e-3,\r\n    ):\r\n        super().register_schedule(\r\n            given_betas, beta_schedule, timesteps, linear_start, linear_end, cosine_s\r\n        )\r\n\r\n        self.shorten_cond_schedule = self.num_timesteps_cond > 1\r\n        if self.shorten_cond_schedule:\r\n            self.make_cond_schedule()\r\n\r\n    def instantiate_first_stage(self, config):\r\n        model = instantiate_from_config(config)\r\n        self.first_stage_model = model.eval()\r\n        self.first_stage_model.train = disabled_train\r\n        for param in self.first_stage_model.parameters():\r\n            param.requires_grad = False\r\n\r\n    def instantiate_contextual_stage(self, config):\r\n        \"\"\"\r\n        Instantiate the contextual stage for contextual loss computation.\r\n\r\n        This method creates the VGG19 model and contextual loss function\r\n        configured for CtrlColor paper specifications.\r\n        \"\"\"\r\n        if self.load_loss:\r\n            try:\r\n                # Instantiate VGG19 model from config\r\n                model = instantiate_from_config(config)\r\n\r\n                # The model should be our VGG19_pytorch adapter\r\n                # No need to load custom weights - uses standard pretrained VGG19\r\n                print(\"VGG19 model instantiated with standard pretrained weights\")\r\n\r\n                # Set model to evaluation mode and freeze parameters\r\n                self.contextual_stage_model = model.eval()\r\n                for param in self.contextual_stage_model.parameters():\r\n                    param.requires_grad = False\r\n\r\n                # Create contextual loss with paper specifications\r\n                self.contextual_loss = ContextualLoss(\r\n                    layers_weights={\r\n                        \"conv_3_2\": 2.0,  # φ³, w₃=2\r\n                        \"conv_5_2\": 8.0,  # φ⁵, w₅=8\r\n                    },\r\n                    distance_type=Distance_Type.Cosine_Distance,  # Paper uses cosine\r\n                    h=0.01,  # Paper bandwidth parameter\r\n                    b=1.0,\r\n                ).to(self.device)\r\n\r\n                # Connect VGG model to contextual loss\r\n                self.contextual_loss.set_vgg_model(self.contextual_stage_model)\r\n\r\n                print(\"Contextual loss initialized with paper specifications:\")\r\n                print(\"  - Layers: conv_3_2 (w=2.0), conv_5_2 (w=8.0)\")\r\n                print(\"  - Distance: Cosine\")\r\n                print(\"  - Bandwidth h: 0.01\")\r\n\r\n            except Exception as e:\r\n                print(f\"Error instantiating contextual stage: {e}\")\r\n                print(\"Falling back to disabled contextual loss\")\r\n                self.contextual_stage_model = None\r\n                self.contextual_loss = None\r\n\r\n        elif not self.load_loss:\r\n            self.contextual_stage_model = None\r\n            self.contextual_loss = None\r\n            print(\"Contextual loss disabled (load_loss=False)\")\r\n        else:\r\n            print(\"ERROR!!!!!self.load_loss should be either True or False!!!\")\r\n\r\n    def instantiate_cond_stage(self, config):\r\n        if not self.cond_stage_trainable:\r\n            if config == \"__is_first_stage__\":\r\n                print(\"Using first stage also as cond stage.\")\r\n                self.cond_stage_model = self.first_stage_model\r\n            elif config == \"__is_unconditional__\":\r\n                print(f\"Training {self.__class__.__name__} as an unconditional model.\")\r\n                self.cond_stage_model = None\r\n                # self.be_unconditional = True\r\n            else:\r\n                model = instantiate_from_config(config)\r\n                self.cond_stage_model = model.eval()\r\n                self.cond_stage_model.train = disabled_train\r\n                for param in self.cond_stage_model.parameters():\r\n                    param.requires_grad = False\r\n        else:\r\n            assert config != \"__is_first_stage__\"\r\n            assert config != \"__is_unconditional__\"\r\n            model = instantiate_from_config(config)\r\n            self.cond_stage_model = model\r\n\r\n    def _get_denoise_row_from_list(\r\n        self, samples, desc=\"\", force_no_decoder_quantization=False\r\n    ):\r\n        denoise_row = []\r\n        for zd in tqdm(samples, desc=desc):\r\n            denoise_row.append(\r\n                self.decode_first_stage(\r\n                    zd.to(self.device), force_not_quantize=force_no_decoder_quantization\r\n                )\r\n            )\r\n        n_imgs_per_row = len(denoise_row)\r\n        denoise_row = torch.stack(denoise_row)  # n_log_step, n_row, C, H, W\r\n        denoise_grid = rearrange(denoise_row, \"n b c h w -> b n c h w\")\r\n        denoise_grid = rearrange(denoise_grid, \"b n c h w -> (b n) c h w\")\r\n        denoise_grid = make_grid(denoise_grid, nrow=n_imgs_per_row)\r\n        return denoise_grid\r\n\r\n    def get_first_stage_encoding(self, encoder_posterior):\r\n        if isinstance(encoder_posterior, DiagonalGaussianDistribution):\r\n            z = encoder_posterior.sample()\r\n        elif isinstance(encoder_posterior, torch.Tensor):\r\n            z = encoder_posterior\r\n        else:\r\n            raise NotImplementedError(\r\n                f\"encoder_posterior of type '{type(encoder_posterior)}' not yet implemented\"\r\n            )\r\n        return self.scale_factor * z\r\n\r\n    def get_learned_conditioning(self, c):\r\n        if self.cond_stage_forward is None:\r\n            if hasattr(self.cond_stage_model, \"encode\") and callable(\r\n                self.cond_stage_model.encode\r\n            ):\r\n                c = self.cond_stage_model.encode(c)\r\n                if isinstance(c, DiagonalGaussianDistribution):\r\n                    c = c.mode()\r\n            else:\r\n                c = self.cond_stage_model(c)\r\n        else:\r\n            assert hasattr(self.cond_stage_model, self.cond_stage_forward)\r\n            c = getattr(self.cond_stage_model, self.cond_stage_forward)(c)\r\n        return c\r\n\r\n    def meshgrid(self, h, w):\r\n        y = torch.arange(0, h).view(h, 1, 1).repeat(1, w, 1)\r\n        x = torch.arange(0, w).view(1, w, 1).repeat(h, 1, 1)\r\n\r\n        arr = torch.cat([y, x], dim=-1)\r\n        return arr\r\n\r\n    def delta_border(self, h, w):\r\n        \"\"\"\r\n        :param h: height\r\n        :param w: width\r\n        :return: normalized distance to image border,\r\n         wtith min distance = 0 at border and max dist = 0.5 at image center\r\n        \"\"\"\r\n        lower_right_corner = torch.tensor([h - 1, w - 1]).view(1, 1, 2)\r\n        arr = self.meshgrid(h, w) / lower_right_corner\r\n        dist_left_up = torch.min(arr, dim=-1, keepdims=True)[0]\r\n        dist_right_down = torch.min(1 - arr, dim=-1, keepdims=True)[0]\r\n        edge_dist = torch.min(\r\n            torch.cat([dist_left_up, dist_right_down], dim=-1), dim=-1\r\n        )[0]\r\n        return edge_dist\r\n\r\n    def get_weighting(self, h, w, Ly, Lx, device):\r\n        weighting = self.delta_border(h, w)\r\n        weighting = torch.clip(\r\n            weighting,\r\n            self.split_input_params[\"clip_min_weight\"],\r\n            self.split_input_params[\"clip_max_weight\"],\r\n        )\r\n        weighting = weighting.view(1, h * w, 1).repeat(1, 1, Ly * Lx).to(device)\r\n\r\n        if self.split_input_params[\"tie_braker\"]:\r\n            L_weighting = self.delta_border(Ly, Lx)\r\n            L_weighting = torch.clip(\r\n                L_weighting,\r\n                self.split_input_params[\"clip_min_tie_weight\"],\r\n                self.split_input_params[\"clip_max_tie_weight\"],\r\n            )\r\n\r\n            L_weighting = L_weighting.view(1, 1, Ly * Lx).to(device)\r\n            weighting = weighting * L_weighting\r\n        return weighting\r\n\r\n    def get_fold_unfold(\r\n        self, x, kernel_size, stride, uf=1, df=1\r\n    ):  # todo load once not every time, shorten code\r\n        \"\"\"\r\n        :param x: img of size (bs, c, h, w)\r\n        :return: n img crops of size (n, bs, c, kernel_size[0], kernel_size[1])\r\n        \"\"\"\r\n        bs, nc, h, w = x.shape\r\n\r\n        # number of crops in image\r\n        Ly = (h - kernel_size[0]) // stride[0] + 1\r\n        Lx = (w - kernel_size[1]) // stride[1] + 1\r\n\r\n        if uf == 1 and df == 1:\r\n            fold_params = dict(\r\n                kernel_size=kernel_size, dilation=1, padding=0, stride=stride\r\n            )\r\n            unfold = torch.nn.Unfold(**fold_params)\r\n\r\n            fold = torch.nn.Fold(output_size=x.shape[2:], **fold_params)\r\n\r\n            weighting = self.get_weighting(\r\n                kernel_size[0], kernel_size[1], Ly, Lx, x.device\r\n            ).to(x.dtype)\r\n            normalization = fold(weighting).view(1, 1, h, w)  # normalizes the overlap\r\n            weighting = weighting.view((1, 1, kernel_size[0], kernel_size[1], Ly * Lx))\r\n\r\n        elif uf > 1 and df == 1:\r\n            fold_params = dict(\r\n                kernel_size=kernel_size, dilation=1, padding=0, stride=stride\r\n            )\r\n            unfold = torch.nn.Unfold(**fold_params)\r\n\r\n            fold_params2 = dict(\r\n                kernel_size=(kernel_size[0] * uf, kernel_size[0] * uf),\r\n                dilation=1,\r\n                padding=0,\r\n                stride=(stride[0] * uf, stride[1] * uf),\r\n            )\r\n            fold = torch.nn.Fold(\r\n                output_size=(x.shape[2] * uf, x.shape[3] * uf), **fold_params2\r\n            )\r\n\r\n            weighting = self.get_weighting(\r\n                kernel_size[0] * uf, kernel_size[1] * uf, Ly, Lx, x.device\r\n            ).to(x.dtype)\r\n            normalization = fold(weighting).view(\r\n                1, 1, h * uf, w * uf\r\n            )  # normalizes the overlap\r\n            weighting = weighting.view(\r\n                (1, 1, kernel_size[0] * uf, kernel_size[1] * uf, Ly * Lx)\r\n            )\r\n\r\n        elif df > 1 and uf == 1:\r\n            fold_params = dict(\r\n                kernel_size=kernel_size, dilation=1, padding=0, stride=stride\r\n            )\r\n            unfold = torch.nn.Unfold(**fold_params)\r\n\r\n            fold_params2 = dict(\r\n                kernel_size=(kernel_size[0] // df, kernel_size[0] // df),\r\n                dilation=1,\r\n                padding=0,\r\n                stride=(stride[0] // df, stride[1] // df),\r\n            )\r\n            fold = torch.nn.Fold(\r\n                output_size=(x.shape[2] // df, x.shape[3] // df), **fold_params2\r\n            )\r\n\r\n            weighting = self.get_weighting(\r\n                kernel_size[0] // df, kernel_size[1] // df, Ly, Lx, x.device\r\n            ).to(x.dtype)\r\n            normalization = fold(weighting).view(\r\n                1, 1, h // df, w // df\r\n            )  # normalizes the overlap\r\n            weighting = weighting.view(\r\n                (1, 1, kernel_size[0] // df, kernel_size[1] // df, Ly * Lx)\r\n            )\r\n\r\n        else:\r\n            raise NotImplementedError\r\n\r\n        return fold, unfold, normalization, weighting\r\n\r\n    @torch.no_grad()\r\n    def get_input(\r\n        self,\r\n        batch,\r\n        k,\r\n        return_first_stage_outputs=False,\r\n        force_c_encode=False,\r\n        cond_key=None,\r\n        return_original_cond=False,\r\n        bs=None,\r\n        return_x=False,\r\n    ):\r\n        # print(\"batch\",batch)\r\n        # print(\"k\",k)\r\n        x = super().get_input(batch, k)\r\n        masked_image = batch[self.masked_image]\r\n        mask = batch[self.mask]\r\n        # print(mask.shape,masked_image.shape)\r\n        mask = torch.nn.functional.interpolate(\r\n            mask, size=(mask.shape[2] // 8, mask.shape[3] // 8)\r\n        )\r\n        # mask=torch.cat([mask] * 2) #if do_classifier_free_guidance else mask\r\n        mask = mask.to(device=\"cuda\", dtype=x.dtype)\r\n        do_classifier_free_guidance = False\r\n        # mask, masked_image_latents = self.prepare_mask_latents(\r\n        #     mask,\r\n        #     masked_image,\r\n        #     batch_size * num_images_per_prompt,\r\n        #     mask.shape[0],\r\n        #     mask.shape[1],\r\n        #     mask.dtype,\r\n        #     \"cuda\",\r\n        #     torch.manual_seed(859311133),#generator\r\n        #     do_classifier_free_guidance,\r\n        # )\r\n        # print(\"x\",x)\r\n        if bs is not None:\r\n            x = x[:bs]\r\n        x = x.to(self.device)\r\n\r\n        encoder_posterior = self.encode_first_stage(x)\r\n        z = self.get_first_stage_encoding(encoder_posterior).detach()\r\n\r\n        masked_image_latents = self.get_first_stage_encoding(\r\n            self.encode_first_stage(masked_image)\r\n        ).detach()\r\n\r\n        if self.model.conditioning_key is not None and not self.force_null_conditioning:\r\n            if cond_key is None:\r\n                cond_key = self.cond_stage_key\r\n            if cond_key != self.first_stage_key:\r\n                if cond_key in [\"caption\", \"coordinates_bbox\", \"txt\"]:\r\n                    xc = batch[cond_key]\r\n                elif cond_key in [\"class_label\", \"cls\"]:\r\n                    xc = batch\r\n                else:\r\n                    xc = super().get_input(batch, cond_key).to(self.device)\r\n            else:\r\n                xc = x\r\n            if not self.cond_stage_trainable or force_c_encode:\r\n                if isinstance(xc, dict) or isinstance(xc, list):\r\n                    c = self.get_learned_conditioning(xc)\r\n                else:\r\n                    c = self.get_learned_conditioning(xc.to(self.device))\r\n            else:\r\n                c = xc\r\n            if bs is not None:\r\n                c = c[:bs]\r\n\r\n            if self.use_positional_encodings:\r\n                pos_x, pos_y = self.compute_latent_shifts(batch)\r\n                ckey = __conditioning_keys__[self.model.conditioning_key]\r\n                c = {ckey: c, \"pos_x\": pos_x, \"pos_y\": pos_y}\r\n\r\n        else:\r\n            c = None\r\n            xc = None\r\n            if self.use_positional_encodings:\r\n                pos_x, pos_y = self.compute_latent_shifts(batch)\r\n                c = {\"pos_x\": pos_x, \"pos_y\": pos_y}\r\n        out = [z, mask, masked_image_latents, c]\r\n        if return_first_stage_outputs:\r\n            xrec = self.decode_first_stage(z)\r\n            out.extend([x, xrec])\r\n        if return_x:\r\n            out.extend([x])\r\n        if return_original_cond:\r\n            out.append(xc)\r\n        return out\r\n\r\n    @torch.no_grad()\r\n    def decode_first_stage(self, z, predict_cids=False, force_not_quantize=False):\r\n        if predict_cids:\r\n            if z.dim() == 4:\r\n                z = torch.argmax(z.exp(), dim=1).long()\r\n            z = self.first_stage_model.quantize.get_codebook_entry(z, shape=None)\r\n            z = rearrange(z, \"b h w c -> b c h w\").contiguous()\r\n\r\n        z = 1.0 / self.scale_factor * z\r\n        return self.first_stage_model.decode(z)\r\n\r\n    @torch.no_grad()\r\n    def encode_first_stage(self, x):\r\n        return self.first_stage_model.encode(x)\r\n\r\n    @torch.no_grad()\r\n    def decode_first_stage_before_vae(\r\n        self, z, predict_cids=False, force_not_quantize=False\r\n    ):\r\n        if predict_cids:\r\n            if z.dim() == 4:\r\n                z = torch.argmax(z.exp(), dim=1).long()\r\n            z = self.first_stage_model.quantize.get_codebook_entry(z, shape=None)\r\n            z = rearrange(z, \"b h w c -> b c h w\").contiguous()\r\n\r\n        z = 1.0 / self.scale_factor * z\r\n        return z\r\n\r\n    def shared_step(self, batch, **kwargs):\r\n        x, mask, masked_image_latents, c = self.get_input(batch, self.first_stage_key)\r\n        loss = self(x, mask, masked_image_latents, c)\r\n        return loss\r\n\r\n    def forward(self, x, mask, masked_image_latents, c, *args, **kwargs):\r\n        t = torch.randint(\r\n            0, self.num_timesteps, (x.shape[0],), device=self.device\r\n        ).long()\r\n        if self.model.conditioning_key is not None:\r\n            assert c is not None\r\n            if self.cond_stage_trainable:\r\n                c = self.get_learned_conditioning(c)\r\n            if self.shorten_cond_schedule:  # TODO: drop this option\r\n                tc = self.cond_ids[t].to(self.device)\r\n                c = self.q_sample(x_start=c, t=tc, noise=torch.randn_like(c.float()))\r\n        return self.p_losses(x, mask, masked_image_latents, c, t, *args, **kwargs)\r\n\r\n    def apply_model(self, x_noisy, t, cond, return_ids=False):\r\n        if isinstance(cond, dict):\r\n            # hybrid case, cond is expected to be a dict\r\n            pass\r\n        else:\r\n            if not isinstance(cond, list):\r\n                cond = [cond]\r\n            key = (\r\n                \"c_concat\" if self.model.conditioning_key == \"concat\" else \"c_crossattn\"\r\n            )\r\n            cond = {key: cond}\r\n\r\n        x_recon = self.model(x_noisy, t, **cond)\r\n\r\n        if isinstance(x_recon, tuple) and not return_ids:\r\n            return x_recon[0]\r\n        else:\r\n            return x_recon\r\n\r\n    def _predict_eps_from_xstart(self, x_t, t, pred_xstart):\r\n        return (\r\n            extract_into_tensor(self.sqrt_recip_alphas_cumprod, t, x_t.shape) * x_t\r\n            - pred_xstart\r\n        ) / extract_into_tensor(self.sqrt_recipm1_alphas_cumprod, t, x_t.shape)\r\n\r\n    def _prior_bpd(self, x_start):\r\n        \"\"\"\r\n        Get the prior KL term for the variational lower-bound, measured in\r\n        bits-per-dim.\r\n        This term can't be optimized, as it only depends on the encoder.\r\n        :param x_start: the [N x C x ...] tensor of inputs.\r\n        :return: a batch of [N] KL values (in bits), one per batch element.\r\n        \"\"\"\r\n        batch_size = x_start.shape[0]\r\n        t = torch.tensor([self.num_timesteps - 1] * batch_size, device=x_start.device)\r\n        qt_mean, _, qt_log_variance = self.q_mean_variance(x_start, t)\r\n        kl_prior = normal_kl(\r\n            mean1=qt_mean, logvar1=qt_log_variance, mean2=0.0, logvar2=0.0\r\n        )\r\n        return mean_flat(kl_prior) / np.log(2.0)\r\n\r\n    def p_losses(\r\n        self, x_start, mask, masked_image_latents, cond, t, noise=None\r\n    ):  # latent diffusion\r\n        noise = default(noise, lambda: torch.randn_like(x_start))\r\n        x_noisy = self.q_sample(x_start=x_start, t=t, noise=noise)\r\n        model_output = self.apply_model(x_noisy, mask, masked_image_latents, t, cond)\r\n        # print(\"before loss: \", model_output.shape)\r\n        loss_dict = {}\r\n        prefix = \"train\" if self.training else \"val\"\r\n\r\n        if self.parameterization == \"x0\":\r\n            target = x_start\r\n        elif self.parameterization == \"eps\":\r\n            target = noise\r\n        elif self.parameterization == \"v\":\r\n            target = self.get_v(x_start, noise, t)\r\n        else:\r\n            raise NotImplementedError()\r\n\r\n        loss_simple = self.get_loss(model_output, target, mean=False).mean([1, 2, 3])\r\n        loss_dict.update({f\"{prefix}/loss_simple\": loss_simple.mean()})\r\n\r\n        logvar_t = self.logvar[t].to(self.device)\r\n        loss = loss_simple / torch.exp(logvar_t) + logvar_t\r\n        # loss = loss_simple / torch.exp(self.logvar) + self.logvar\r\n        if self.learn_logvar:\r\n            loss_dict.update({f\"{prefix}/loss_gamma\": loss.mean()})\r\n            loss_dict.update({\"logvar\": self.logvar.data.mean()})\r\n\r\n        loss = self.l_simple_weight * loss.mean()\r\n\r\n        loss_vlb = self.get_loss(model_output, target, mean=False).mean(dim=(1, 2, 3))\r\n        loss_vlb = (self.lvlb_weights[t] * loss_vlb).mean()\r\n        loss_dict.update({f\"{prefix}/loss_vlb\": loss_vlb})\r\n        loss += self.original_elbo_weight * loss_vlb\r\n        loss_dict.update({f\"{prefix}/loss\": loss})\r\n\r\n        return loss, loss_dict\r\n\r\n    def p_mean_variance(\r\n        self,\r\n        x,\r\n        c,\r\n        t,\r\n        clip_denoised: bool,\r\n        return_codebook_ids=False,\r\n        quantize_denoised=False,\r\n        return_x0=False,\r\n        score_corrector=None,\r\n        corrector_kwargs=None,\r\n    ):\r\n        t_in = t\r\n        model_out = self.apply_model(x, t_in, c, return_ids=return_codebook_ids)\r\n\r\n        if score_corrector is not None:\r\n            assert self.parameterization == \"eps\"\r\n            model_out = score_corrector.modify_score(\r\n                self, model_out, x, t, c, **corrector_kwargs\r\n            )\r\n\r\n        if return_codebook_ids:\r\n            model_out, logits = model_out\r\n\r\n        if self.parameterization == \"eps\":\r\n            x_recon = self.predict_start_from_noise(x, t=t, noise=model_out)\r\n        elif self.parameterization == \"x0\":\r\n            x_recon = model_out\r\n        else:\r\n            raise NotImplementedError()\r\n\r\n        if clip_denoised:\r\n            x_recon.clamp_(-1.0, 1.0)\r\n        if quantize_denoised:\r\n            x_recon, _, [_, _, indices] = self.first_stage_model.quantize(x_recon)\r\n        model_mean, posterior_variance, posterior_log_variance = self.q_posterior(\r\n            x_start=x_recon, x_t=x, t=t\r\n        )\r\n        if return_codebook_ids:\r\n            return model_mean, posterior_variance, posterior_log_variance, logits\r\n        elif return_x0:\r\n            return model_mean, posterior_variance, posterior_log_variance, x_recon\r\n        else:\r\n            return model_mean, posterior_variance, posterior_log_variance\r\n\r\n    @torch.no_grad()\r\n    def p_sample(\r\n        self,\r\n        x,\r\n        c,\r\n        t,\r\n        clip_denoised=False,\r\n        repeat_noise=False,\r\n        return_codebook_ids=False,\r\n        quantize_denoised=False,\r\n        return_x0=False,\r\n        temperature=1.0,\r\n        noise_dropout=0.0,\r\n        score_corrector=None,\r\n        corrector_kwargs=None,\r\n    ):\r\n        b, *_, device = *x.shape, x.device\r\n        outputs = self.p_mean_variance(\r\n            x=x,\r\n            c=c,\r\n            t=t,\r\n            clip_denoised=clip_denoised,\r\n            return_codebook_ids=return_codebook_ids,\r\n            quantize_denoised=quantize_denoised,\r\n            return_x0=return_x0,\r\n            score_corrector=score_corrector,\r\n            corrector_kwargs=corrector_kwargs,\r\n        )\r\n        if return_codebook_ids:\r\n            raise DeprecationWarning(\"Support dropped.\")\r\n            model_mean, _, model_log_variance, logits = outputs\r\n        elif return_x0:\r\n            model_mean, _, model_log_variance, x0 = outputs\r\n        else:\r\n            model_mean, _, model_log_variance = outputs\r\n\r\n        noise = noise_like(x.shape, device, repeat_noise) * temperature\r\n        if noise_dropout > 0.0:\r\n            noise = torch.nn.functional.dropout(noise, p=noise_dropout)\r\n        # no noise when t == 0\r\n        nonzero_mask = (1 - (t == 0).float()).reshape(b, *((1,) * (len(x.shape) - 1)))\r\n\r\n        if return_codebook_ids:\r\n            return model_mean + nonzero_mask * (\r\n                0.5 * model_log_variance\r\n            ).exp() * noise, logits.argmax(dim=1)\r\n        if return_x0:\r\n            return model_mean + nonzero_mask * (\r\n                0.5 * model_log_variance\r\n            ).exp() * noise, x0\r\n        else:\r\n            return model_mean + nonzero_mask * (0.5 * model_log_variance).exp() * noise\r\n\r\n    @torch.no_grad()\r\n    def progressive_denoising(\r\n        self,\r\n        cond,\r\n        shape,\r\n        verbose=True,\r\n        callback=None,\r\n        quantize_denoised=False,\r\n        img_callback=None,\r\n        mask=None,\r\n        x0=None,\r\n        temperature=1.0,\r\n        noise_dropout=0.0,\r\n        score_corrector=None,\r\n        corrector_kwargs=None,\r\n        batch_size=None,\r\n        x_T=None,\r\n        start_T=None,\r\n        log_every_t=None,\r\n    ):\r\n        if not log_every_t:\r\n            log_every_t = self.log_every_t\r\n        timesteps = self.num_timesteps\r\n        if batch_size is not None:\r\n            b = batch_size if batch_size is not None else shape[0]\r\n            shape = [batch_size] + list(shape)\r\n        else:\r\n            b = batch_size = shape[0]\r\n        if x_T is None:\r\n            img = torch.randn(shape, device=self.device)\r\n        else:\r\n            img = x_T\r\n        intermediates = []\r\n        if cond is not None:\r\n            if isinstance(cond, dict):\r\n                cond = {\r\n                    key: cond[key][:batch_size]\r\n                    if not isinstance(cond[key], list)\r\n                    else list(map(lambda x: x[:batch_size], cond[key]))\r\n                    for key in cond\r\n                }\r\n            else:\r\n                cond = (\r\n                    [c[:batch_size] for c in cond]\r\n                    if isinstance(cond, list)\r\n                    else cond[:batch_size]\r\n                )\r\n\r\n        if start_T is not None:\r\n            timesteps = min(timesteps, start_T)\r\n        iterator = (\r\n            tqdm(\r\n                reversed(range(0, timesteps)),\r\n                desc=\"Progressive Generation\",\r\n                total=timesteps,\r\n            )\r\n            if verbose\r\n            else reversed(range(0, timesteps))\r\n        )\r\n        if isinstance(temperature, float):\r\n            temperature = [temperature] * timesteps\r\n\r\n        for i in iterator:\r\n            ts = torch.full((b,), i, device=self.device, dtype=torch.long)\r\n            if self.shorten_cond_schedule:\r\n                assert self.model.conditioning_key != \"hybrid\"\r\n                tc = self.cond_ids[ts].to(cond.device)\r\n                cond = self.q_sample(x_start=cond, t=tc, noise=torch.randn_like(cond))\r\n\r\n            img, x0_partial = self.p_sample(\r\n                img,\r\n                cond,\r\n                ts,\r\n                clip_denoised=self.clip_denoised,\r\n                quantize_denoised=quantize_denoised,\r\n                return_x0=True,\r\n                temperature=temperature[i],\r\n                noise_dropout=noise_dropout,\r\n                score_corrector=score_corrector,\r\n                corrector_kwargs=corrector_kwargs,\r\n            )\r\n            if mask is not None:\r\n                assert x0 is not None\r\n                img_orig = self.q_sample(x0, ts)\r\n                img = img_orig * mask + (1.0 - mask) * img\r\n\r\n            if i % log_every_t == 0 or i == timesteps - 1:\r\n                intermediates.append(x0_partial)\r\n            if callback:\r\n                callback(i)\r\n            if img_callback:\r\n                img_callback(img, i)\r\n        return img, intermediates\r\n\r\n    @torch.no_grad()\r\n    def p_sample_loop(\r\n        self,\r\n        cond,\r\n        shape,\r\n        return_intermediates=False,\r\n        x_T=None,\r\n        verbose=True,\r\n        callback=None,\r\n        timesteps=None,\r\n        quantize_denoised=False,\r\n        mask=None,\r\n        x0=None,\r\n        img_callback=None,\r\n        start_T=None,\r\n        log_every_t=None,\r\n    ):\r\n        if not log_every_t:\r\n            log_every_t = self.log_every_t\r\n        device = self.betas.device\r\n        b = shape[0]\r\n        if x_T is None:\r\n            img = torch.randn(shape, device=device)\r\n        else:\r\n            img = x_T\r\n\r\n        intermediates = [img]\r\n        if timesteps is None:\r\n            timesteps = self.num_timesteps\r\n\r\n        if start_T is not None:\r\n            timesteps = min(timesteps, start_T)\r\n        iterator = (\r\n            tqdm(reversed(range(0, timesteps)), desc=\"Sampling t\", total=timesteps)\r\n            if verbose\r\n            else reversed(range(0, timesteps))\r\n        )\r\n\r\n        if mask is not None:\r\n            assert x0 is not None\r\n            assert x0.shape[2:3] == mask.shape[2:3]  # spatial size has to match\r\n\r\n        for i in iterator:\r\n            ts = torch.full((b,), i, device=device, dtype=torch.long)\r\n            if self.shorten_cond_schedule:\r\n                assert self.model.conditioning_key != \"hybrid\"\r\n                tc = self.cond_ids[ts].to(cond.device)\r\n                cond = self.q_sample(x_start=cond, t=tc, noise=torch.randn_like(cond))\r\n\r\n            img = self.p_sample(\r\n                img,\r\n                cond,\r\n                ts,\r\n                clip_denoised=self.clip_denoised,\r\n                quantize_denoised=quantize_denoised,\r\n            )\r\n            if mask is not None:\r\n                img_orig = self.q_sample(x0, ts)\r\n                img = img_orig * mask + (1.0 - mask) * img\r\n\r\n            if i % log_every_t == 0 or i == timesteps - 1:\r\n                intermediates.append(img)\r\n            if callback:\r\n                callback(i)\r\n            if img_callback:\r\n                img_callback(img, i)\r\n\r\n        if return_intermediates:\r\n            return img, intermediates\r\n        return img\r\n\r\n    @torch.no_grad()\r\n    def sample(\r\n        self,\r\n        cond,\r\n        batch_size=16,\r\n        return_intermediates=False,\r\n        x_T=None,\r\n        verbose=True,\r\n        timesteps=None,\r\n        quantize_denoised=False,\r\n        mask=None,\r\n        x0=None,\r\n        shape=None,\r\n        **kwargs,\r\n    ):\r\n        if shape is None:\r\n            shape = (batch_size, self.channels, self.image_size, self.image_size)\r\n        if cond is not None:\r\n            if isinstance(cond, dict):\r\n                cond = {\r\n                    key: cond[key][:batch_size]\r\n                    if not isinstance(cond[key], list)\r\n                    else list(map(lambda x: x[:batch_size], cond[key]))\r\n                    for key in cond\r\n                }\r\n            else:\r\n                cond = (\r\n                    [c[:batch_size] for c in cond]\r\n                    if isinstance(cond, list)\r\n                    else cond[:batch_size]\r\n                )\r\n        return self.p_sample_loop(\r\n            cond,\r\n            shape,\r\n            return_intermediates=return_intermediates,\r\n            x_T=x_T,\r\n            verbose=verbose,\r\n            timesteps=timesteps,\r\n            quantize_denoised=quantize_denoised,\r\n            mask=mask,\r\n            x0=x0,\r\n        )\r\n\r\n    @torch.no_grad()\r\n    def sample_log(self, cond, batch_size, ddim, ddim_steps, **kwargs):\r\n        if ddim:\r\n            ddim_sampler = DDIMSampler(self)\r\n            shape = (self.channels, self.image_size, self.image_size)\r\n            samples, intermediates = ddim_sampler.sample(\r\n                ddim_steps, batch_size, shape, cond, verbose=False, **kwargs\r\n            )\r\n\r\n        else:\r\n            samples, intermediates = self.sample(\r\n                cond=cond, batch_size=batch_size, return_intermediates=True, **kwargs\r\n            )\r\n\r\n        return samples, intermediates\r\n\r\n    @torch.no_grad()\r\n    def get_unconditional_conditioning(self, batch_size, null_label=None):\r\n        if null_label is not None:\r\n            xc = null_label\r\n            if isinstance(xc, ListConfig):\r\n                xc = list(xc)\r\n            if isinstance(xc, dict) or isinstance(xc, list):\r\n                c = self.get_learned_conditioning(xc)\r\n            else:\r\n                if hasattr(xc, \"to\"):\r\n                    xc = xc.to(self.device)\r\n                c = self.get_learned_conditioning(xc)\r\n        else:\r\n            if self.cond_stage_key in [\"class_label\", \"cls\"]:\r\n                xc = self.cond_stage_model.get_unconditional_conditioning(\r\n                    batch_size, device=self.device\r\n                )\r\n                return self.get_learned_conditioning(xc)\r\n            else:\r\n                raise NotImplementedError(\"todo\")\r\n        if isinstance(c, list):  # in case the encoder gives us a list\r\n            for i in range(len(c)):\r\n                c[i] = repeat(c[i], \"1 ... -> b ...\", b=batch_size).to(self.device)\r\n        else:\r\n            c = repeat(c, \"1 ... -> b ...\", b=batch_size).to(self.device)\r\n        return c\r\n\r\n    @torch.no_grad()\r\n    def log_images(\r\n        self,\r\n        batch,\r\n        N=8,\r\n        n_row=4,\r\n        sample=True,\r\n        ddim_steps=50,\r\n        ddim_eta=0.0,\r\n        return_keys=None,\r\n        quantize_denoised=True,\r\n        inpaint=True,\r\n        plot_denoise_rows=False,\r\n        plot_progressive_rows=True,\r\n        plot_diffusion_rows=True,\r\n        unconditional_guidance_scale=1.0,\r\n        unconditional_guidance_label=None,\r\n        use_ema_scope=True,\r\n        **kwargs,\r\n    ):\r\n        ema_scope = self.ema_scope if use_ema_scope else nullcontext\r\n        use_ddim = ddim_steps is not None\r\n\r\n        log = dict()\r\n        z, c, x, xrec, xc = self.get_input(\r\n            batch,\r\n            self.first_stage_key,\r\n            return_first_stage_outputs=True,\r\n            force_c_encode=True,\r\n            return_original_cond=True,\r\n            bs=N,\r\n        )\r\n        N = min(x.shape[0], N)\r\n        n_row = min(x.shape[0], n_row)\r\n        log[\"inputs\"] = x\r\n        log[\"reconstruction\"] = xrec\r\n        if self.model.conditioning_key is not None:\r\n            if hasattr(self.cond_stage_model, \"decode\"):\r\n                xc = self.cond_stage_model.decode(c)\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"caption\", \"txt\"]:\r\n                xc = log_txt_as_img(\r\n                    (x.shape[2], x.shape[3]),\r\n                    batch[self.cond_stage_key],\r\n                    size=x.shape[2] // 25,\r\n                )\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"class_label\", \"cls\"]:\r\n                try:\r\n                    xc = log_txt_as_img(\r\n                        (x.shape[2], x.shape[3]),\r\n                        batch[\"human_label\"],\r\n                        size=x.shape[2] // 25,\r\n                    )\r\n                    log[\"conditioning\"] = xc\r\n                except KeyError:\r\n                    # probably no \"human_label\" in batch\r\n                    pass\r\n            elif isimage(xc):\r\n                log[\"conditioning\"] = xc\r\n            if ismap(xc):\r\n                log[\"original_conditioning\"] = self.to_rgb(xc)\r\n\r\n        if plot_diffusion_rows:\r\n            # get diffusion row\r\n            diffusion_row = list()\r\n            z_start = z[:n_row]\r\n            for t in range(self.num_timesteps):\r\n                if t % self.log_every_t == 0 or t == self.num_timesteps - 1:\r\n                    t = repeat(torch.tensor([t]), \"1 -> b\", b=n_row)\r\n                    t = t.to(self.device).long()\r\n                    noise = torch.randn_like(z_start)\r\n                    z_noisy = self.q_sample(x_start=z_start, t=t, noise=noise)\r\n                    diffusion_row.append(self.decode_first_stage(z_noisy))\r\n\r\n            diffusion_row = torch.stack(diffusion_row)  # n_log_step, n_row, C, H, W\r\n            diffusion_grid = rearrange(diffusion_row, \"n b c h w -> b n c h w\")\r\n            diffusion_grid = rearrange(diffusion_grid, \"b n c h w -> (b n) c h w\")\r\n            diffusion_grid = make_grid(diffusion_grid, nrow=diffusion_row.shape[0])\r\n            log[\"diffusion_row\"] = diffusion_grid\r\n\r\n        if sample:\r\n            # get denoise row\r\n            with ema_scope(\"Sampling\"):\r\n                samples, z_denoise_row = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                )\r\n                # samples, z_denoise_row = self.sample(cond=c, batch_size=N, return_intermediates=True)\r\n            x_samples = self.decode_first_stage(samples)\r\n            log[\"samples\"] = x_samples\r\n            if plot_denoise_rows:\r\n                denoise_grid = self._get_denoise_row_from_list(z_denoise_row)\r\n                log[\"denoise_row\"] = denoise_grid\r\n\r\n            if (\r\n                quantize_denoised\r\n                and not isinstance(self.first_stage_model, AutoencoderKL)\r\n                and not isinstance(self.first_stage_model, IdentityFirstStage)\r\n            ):\r\n                # also display when quantizing x0 while sampling\r\n                with ema_scope(\"Plotting Quantized Denoised\"):\r\n                    samples, z_denoise_row = self.sample_log(\r\n                        cond=c,\r\n                        batch_size=N,\r\n                        ddim=use_ddim,\r\n                        ddim_steps=ddim_steps,\r\n                        eta=ddim_eta,\r\n                        quantize_denoised=True,\r\n                    )\r\n                    # samples, z_denoise_row = self.sample(cond=c, batch_size=N, return_intermediates=True,\r\n                    #                                      quantize_denoised=True)\r\n                x_samples = self.decode_first_stage(samples.to(self.device))\r\n                log[\"samples_x0_quantized\"] = x_samples\r\n\r\n        if unconditional_guidance_scale > 1.0:\r\n            uc = self.get_unconditional_conditioning(N, unconditional_guidance_label)\r\n            if self.model.conditioning_key == \"crossattn-adm\":\r\n                uc = {\"c_crossattn\": [uc], \"c_adm\": c[\"c_adm\"]}\r\n            with ema_scope(\"Sampling with classifier-free guidance\"):\r\n                samples_cfg, _ = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                    unconditional_guidance_scale=unconditional_guidance_scale,\r\n                    unconditional_conditioning=uc,\r\n                )\r\n                x_samples_cfg = self.decode_first_stage(samples_cfg)\r\n                log[f\"samples_cfg_scale_{unconditional_guidance_scale:.2f}\"] = (\r\n                    x_samples_cfg\r\n                )\r\n\r\n        if inpaint:\r\n            # make a simple center square\r\n            b, h, w = z.shape[0], z.shape[2], z.shape[3]\r\n            mask = torch.ones(N, h, w).to(self.device)\r\n            # zeros will be filled in\r\n            mask[:, h // 4 : 3 * h // 4, w // 4 : 3 * w // 4] = 0.0\r\n            mask = mask[:, None, ...]\r\n            with ema_scope(\"Plotting Inpaint\"):\r\n                samples, _ = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    eta=ddim_eta,\r\n                    ddim_steps=ddim_steps,\r\n                    x0=z[:N],\r\n                    mask=mask,\r\n                )\r\n            x_samples = self.decode_first_stage(samples.to(self.device))\r\n            log[\"samples_inpainting\"] = x_samples\r\n            log[\"mask\"] = mask\r\n\r\n            # outpaint\r\n            mask = 1.0 - mask\r\n            with ema_scope(\"Plotting Outpaint\"):\r\n                samples, _ = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    eta=ddim_eta,\r\n                    ddim_steps=ddim_steps,\r\n                    x0=z[:N],\r\n                    mask=mask,\r\n                )\r\n            x_samples = self.decode_first_stage(samples.to(self.device))\r\n            log[\"samples_outpainting\"] = x_samples\r\n\r\n        if plot_progressive_rows:\r\n            with ema_scope(\"Plotting Progressives\"):\r\n                img, progressives = self.progressive_denoising(\r\n                    c,\r\n                    shape=(self.channels, self.image_size, self.image_size),\r\n                    batch_size=N,\r\n                )\r\n            prog_row = self._get_denoise_row_from_list(\r\n                progressives, desc=\"Progressive Generation\"\r\n            )\r\n            log[\"progressive_row\"] = prog_row\r\n\r\n        if return_keys:\r\n            if np.intersect1d(list(log.keys()), return_keys).shape[0] == 0:\r\n                return log\r\n            else:\r\n                return {key: log[key] for key in return_keys}\r\n        return log\r\n\r\n    def configure_optimizers(self):\r\n        lr = self.learning_rate\r\n        params = list(self.model.parameters())\r\n        if self.cond_stage_trainable:\r\n            print(f\"{self.__class__.__name__}: Also optimizing conditioner params!\")\r\n            params = params + list(self.cond_stage_model.parameters())\r\n        if self.learn_logvar:\r\n            print(\"Diffusion model optimizing logvar\")\r\n            params.append(self.logvar)\r\n        opt = torch.optim.AdamW(params, lr=lr)\r\n        if self.use_scheduler:\r\n            assert \"target\" in self.scheduler_config\r\n            scheduler = instantiate_from_config(self.scheduler_config)\r\n\r\n            print(\"Setting up LambdaLR scheduler...\")\r\n            scheduler = [\r\n                {\r\n                    \"scheduler\": LambdaLR(opt, lr_lambda=scheduler.schedule),\r\n                    \"interval\": \"step\",\r\n                    \"frequency\": 1,\r\n                }\r\n            ]\r\n            return [opt], scheduler\r\n        return opt\r\n\r\n    @torch.no_grad()\r\n    def to_rgb(self, x):\r\n        x = x.float()\r\n        if not hasattr(self, \"colorize\"):\r\n            self.colorize = torch.randn(3, x.shape[1], 1, 1).to(x)\r\n        x = nn.functional.conv2d(x, weight=self.colorize)\r\n        x = 2.0 * (x - x.min()) / (x.max() - x.min()) - 1.0\r\n        return x\r\n\r\n\r\nclass DiffusionWrapper(pl.LightningModule):\r\n    def __init__(self, diff_model_config, conditioning_key):\r\n        super().__init__()\r\n        self.sequential_cross_attn = diff_model_config.pop(\r\n            \"sequential_crossattn\", False\r\n        )\r\n        self.diffusion_model = instantiate_from_config(diff_model_config)\r\n        self.conditioning_key = conditioning_key\r\n        assert self.conditioning_key in [\r\n            None,\r\n            \"concat\",\r\n            \"crossattn\",\r\n            \"hybrid\",\r\n            \"adm\",\r\n            \"hybrid-adm\",\r\n            \"crossattn-adm\",\r\n        ]\r\n\r\n    def forward(\r\n        self, x, t, c_concat: list = None, c_crossattn: list = None, c_adm=None\r\n    ):\r\n        if self.conditioning_key is None:\r\n            out = self.diffusion_model(x, t)\r\n        elif self.conditioning_key == \"concat\":\r\n            xc = torch.cat([x] + c_concat, dim=1)\r\n            out = self.diffusion_model(xc, t)\r\n        elif self.conditioning_key == \"crossattn\":\r\n            if not self.sequential_cross_attn:\r\n                cc = torch.cat(c_crossattn, 1)\r\n            else:\r\n                cc = c_crossattn\r\n            out = self.diffusion_model(x, t, context=cc)\r\n        elif self.conditioning_key == \"hybrid\":\r\n            xc = torch.cat([x] + c_concat, dim=1)\r\n            cc = torch.cat(c_crossattn, 1)\r\n            out = self.diffusion_model(xc, t, context=cc)\r\n        elif self.conditioning_key == \"hybrid-adm\":\r\n            assert c_adm is not None\r\n            xc = torch.cat([x] + c_concat, dim=1)\r\n            cc = torch.cat(c_crossattn, 1)\r\n            out = self.diffusion_model(xc, t, context=cc, y=c_adm)\r\n        elif self.conditioning_key == \"crossattn-adm\":\r\n            assert c_adm is not None\r\n            cc = torch.cat(c_crossattn, 1)\r\n            out = self.diffusion_model(x, t, context=cc, y=c_adm)\r\n        elif self.conditioning_key == \"adm\":\r\n            cc = c_crossattn[0]\r\n            out = self.diffusion_model(x, t, y=cc)\r\n        else:\r\n            raise NotImplementedError()\r\n\r\n        return out\r\n\r\n\r\nclass LatentUpscaleDiffusion(LatentDiffusion):\r\n    def __init__(\r\n        self,\r\n        *args,\r\n        low_scale_config,\r\n        low_scale_key=\"LR\",\r\n        noise_level_key=None,\r\n        **kwargs,\r\n    ):\r\n        super().__init__(*args, **kwargs)\r\n        # assumes that neither the cond_stage nor the low_scale_model contain trainable params\r\n        assert not self.cond_stage_trainable\r\n        self.instantiate_low_stage(low_scale_config)\r\n        self.low_scale_key = low_scale_key\r\n        self.noise_level_key = noise_level_key\r\n\r\n    def instantiate_low_stage(self, config):\r\n        model = instantiate_from_config(config)\r\n        self.low_scale_model = model.eval()\r\n        self.low_scale_model.train = disabled_train\r\n        for param in self.low_scale_model.parameters():\r\n            param.requires_grad = False\r\n\r\n    @torch.no_grad()\r\n    def get_input(self, batch, k, cond_key=None, bs=None, log_mode=False):\r\n        if not log_mode:\r\n            z, c = super().get_input(batch, k, force_c_encode=True, bs=bs)\r\n        else:\r\n            z, c, x, xrec, xc = super().get_input(\r\n                batch,\r\n                self.first_stage_key,\r\n                return_first_stage_outputs=True,\r\n                force_c_encode=True,\r\n                return_original_cond=True,\r\n                bs=bs,\r\n            )\r\n        x_low = batch[self.low_scale_key][:bs]\r\n        x_low = rearrange(x_low, \"b h w c -> b c h w\")\r\n        x_low = x_low.to(memory_format=torch.contiguous_format).float()\r\n        zx, noise_level = self.low_scale_model(x_low)\r\n        if self.noise_level_key is not None:\r\n            # get noise level from batch instead, e.g. when extracting a custom noise level for bsr\r\n            raise NotImplementedError(\"TODO\")\r\n\r\n        all_conds = {\"c_concat\": [zx], \"c_crossattn\": [c], \"c_adm\": noise_level}\r\n        if log_mode:\r\n            # TODO: maybe disable if too expensive\r\n            x_low_rec = self.low_scale_model.decode(zx)\r\n            return z, all_conds, x, xrec, xc, x_low, x_low_rec, noise_level\r\n        return z, all_conds\r\n\r\n    @torch.no_grad()\r\n    def log_images(\r\n        self,\r\n        batch,\r\n        N=8,\r\n        n_row=4,\r\n        sample=True,\r\n        ddim_steps=200,\r\n        ddim_eta=1.0,\r\n        return_keys=None,\r\n        plot_denoise_rows=False,\r\n        plot_progressive_rows=True,\r\n        plot_diffusion_rows=True,\r\n        unconditional_guidance_scale=1.0,\r\n        unconditional_guidance_label=None,\r\n        use_ema_scope=True,\r\n        **kwargs,\r\n    ):\r\n        ema_scope = self.ema_scope if use_ema_scope else nullcontext\r\n        use_ddim = ddim_steps is not None\r\n\r\n        log = dict()\r\n        z, c, x, xrec, xc, x_low, x_low_rec, noise_level = self.get_input(\r\n            batch, self.first_stage_key, bs=N, log_mode=True\r\n        )\r\n        N = min(x.shape[0], N)\r\n        n_row = min(x.shape[0], n_row)\r\n        log[\"inputs\"] = x\r\n        log[\"reconstruction\"] = xrec\r\n        log[\"x_lr\"] = x_low\r\n        log[\r\n            f\"x_lr_rec_@noise_levels{'-'.join(map(lambda x: str(x), list(noise_level.cpu().numpy())))}\"\r\n        ] = x_low_rec\r\n        if self.model.conditioning_key is not None:\r\n            if hasattr(self.cond_stage_model, \"decode\"):\r\n                xc = self.cond_stage_model.decode(c)\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"caption\", \"txt\"]:\r\n                xc = log_txt_as_img(\r\n                    (x.shape[2], x.shape[3]),\r\n                    batch[self.cond_stage_key],\r\n                    size=x.shape[2] // 25,\r\n                )\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"class_label\", \"cls\"]:\r\n                xc = log_txt_as_img(\r\n                    (x.shape[2], x.shape[3]),\r\n                    batch[\"human_label\"],\r\n                    size=x.shape[2] // 25,\r\n                )\r\n                log[\"conditioning\"] = xc\r\n            elif isimage(xc):\r\n                log[\"conditioning\"] = xc\r\n            if ismap(xc):\r\n                log[\"original_conditioning\"] = self.to_rgb(xc)\r\n\r\n        if plot_diffusion_rows:\r\n            # get diffusion row\r\n            diffusion_row = list()\r\n            z_start = z[:n_row]\r\n            for t in range(self.num_timesteps):\r\n                if t % self.log_every_t == 0 or t == self.num_timesteps - 1:\r\n                    t = repeat(torch.tensor([t]), \"1 -> b\", b=n_row)\r\n                    t = t.to(self.device).long()\r\n                    noise = torch.randn_like(z_start)\r\n                    z_noisy = self.q_sample(x_start=z_start, t=t, noise=noise)\r\n                    diffusion_row.append(self.decode_first_stage(z_noisy))\r\n\r\n            diffusion_row = torch.stack(diffusion_row)  # n_log_step, n_row, C, H, W\r\n            diffusion_grid = rearrange(diffusion_row, \"n b c h w -> b n c h w\")\r\n            diffusion_grid = rearrange(diffusion_grid, \"b n c h w -> (b n) c h w\")\r\n            diffusion_grid = make_grid(diffusion_grid, nrow=diffusion_row.shape[0])\r\n            log[\"diffusion_row\"] = diffusion_grid\r\n\r\n        if sample:\r\n            # get denoise row\r\n            with ema_scope(\"Sampling\"):\r\n                samples, z_denoise_row = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                )\r\n                # samples, z_denoise_row = self.sample(cond=c, batch_size=N, return_intermediates=True)\r\n            x_samples = self.decode_first_stage(samples)\r\n            log[\"samples\"] = x_samples\r\n            if plot_denoise_rows:\r\n                denoise_grid = self._get_denoise_row_from_list(z_denoise_row)\r\n                log[\"denoise_row\"] = denoise_grid\r\n\r\n        if unconditional_guidance_scale > 1.0:\r\n            uc_tmp = self.get_unconditional_conditioning(\r\n                N, unconditional_guidance_label\r\n            )\r\n            # TODO explore better \"unconditional\" choices for the other keys\r\n            # maybe guide away from empty text label and highest noise level and maximally degraded zx?\r\n            uc = dict()\r\n            for k in c:\r\n                if k == \"c_crossattn\":\r\n                    assert isinstance(c[k], list) and len(c[k]) == 1\r\n                    uc[k] = [uc_tmp]\r\n                elif k == \"c_adm\":  # todo: only run with text-based guidance?\r\n                    assert isinstance(c[k], torch.Tensor)\r\n                    # uc[k] = torch.ones_like(c[k]) * self.low_scale_model.max_noise_level\r\n                    uc[k] = c[k]\r\n                elif isinstance(c[k], list):\r\n                    uc[k] = [c[k][i] for i in range(len(c[k]))]\r\n                else:\r\n                    uc[k] = c[k]\r\n\r\n            with ema_scope(\"Sampling with classifier-free guidance\"):\r\n                samples_cfg, _ = self.sample_log(\r\n                    cond=c,\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                    unconditional_guidance_scale=unconditional_guidance_scale,\r\n                    unconditional_conditioning=uc,\r\n                )\r\n                x_samples_cfg = self.decode_first_stage(samples_cfg)\r\n                log[f\"samples_cfg_scale_{unconditional_guidance_scale:.2f}\"] = (\r\n                    x_samples_cfg\r\n                )\r\n\r\n        if plot_progressive_rows:\r\n            with ema_scope(\"Plotting Progressives\"):\r\n                img, progressives = self.progressive_denoising(\r\n                    c,\r\n                    shape=(self.channels, self.image_size, self.image_size),\r\n                    batch_size=N,\r\n                )\r\n            prog_row = self._get_denoise_row_from_list(\r\n                progressives, desc=\"Progressive Generation\"\r\n            )\r\n            log[\"progressive_row\"] = prog_row\r\n\r\n        return log\r\n\r\n\r\nclass LatentFinetuneDiffusion(LatentDiffusion):\r\n    \"\"\"\r\n    Basis for different finetunas, such as inpainting or depth2image\r\n    To disable finetuning mode, set finetune_keys to None\r\n    \"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        concat_keys: tuple,\r\n        finetune_keys=(\r\n            \"model.diffusion_model.input_blocks.0.0.weight\",\r\n            \"model_ema.diffusion_modelinput_blocks00weight\",\r\n        ),\r\n        keep_finetune_dims=4,\r\n        # if model was trained without concat mode before and we would like to keep these channels\r\n        c_concat_log_start=None,  # to log reconstruction of c_concat codes\r\n        c_concat_log_end=None,\r\n        *args,\r\n        **kwargs,\r\n    ):\r\n        ckpt_path = kwargs.pop(\"ckpt_path\", None)\r\n        ignore_keys = kwargs.pop(\"ignore_keys\", list())\r\n        super().__init__(*args, **kwargs)\r\n        self.finetune_keys = finetune_keys\r\n        self.concat_keys = concat_keys\r\n        self.keep_dims = keep_finetune_dims\r\n        self.c_concat_log_start = c_concat_log_start\r\n        self.c_concat_log_end = c_concat_log_end\r\n        if exists(self.finetune_keys):\r\n            assert exists(ckpt_path), \"can only finetune from a given checkpoint\"\r\n        if exists(ckpt_path):\r\n            self.init_from_ckpt(ckpt_path, ignore_keys)\r\n\r\n    def init_from_ckpt(self, path, ignore_keys=list(), only_model=False):\r\n        sd = torch.load(path, map_location=\"cpu\")\r\n        if \"state_dict\" in list(sd.keys()):\r\n            sd = sd[\"state_dict\"]\r\n        keys = list(sd.keys())\r\n        for k in keys:\r\n            for ik in ignore_keys:\r\n                if k.startswith(ik):\r\n                    print(\"Deleting key {} from state_dict.\".format(k))\r\n                    del sd[k]\r\n\r\n            # make it explicit, finetune by including extra input channels\r\n            if exists(self.finetune_keys) and k in self.finetune_keys:\r\n                new_entry = None\r\n                for name, param in self.named_parameters():\r\n                    if name in self.finetune_keys:\r\n                        print(\r\n                            f\"modifying key '{name}' and keeping its original {self.keep_dims} (channels) dimensions only\"\r\n                        )\r\n                        new_entry = torch.zeros_like(param)  # zero init\r\n                assert exists(new_entry), \"did not find matching parameter to modify\"\r\n                new_entry[:, : self.keep_dims, ...] = sd[k]\r\n                sd[k] = new_entry\r\n\r\n        missing, unexpected = (\r\n            self.load_state_dict(sd, strict=False)\r\n            if not only_model\r\n            else self.model.load_state_dict(sd, strict=False)\r\n        )\r\n        print(\r\n            f\"Restored from {path} with {len(missing)} missing and {len(unexpected)} unexpected keys\"\r\n        )\r\n        if len(missing) > 0:\r\n            print(f\"Missing Keys: {missing}\")\r\n        if len(unexpected) > 0:\r\n            print(f\"Unexpected Keys: {unexpected}\")\r\n\r\n    @torch.no_grad()\r\n    def log_images(\r\n        self,\r\n        batch,\r\n        N=8,\r\n        n_row=4,\r\n        sample=True,\r\n        ddim_steps=200,\r\n        ddim_eta=1.0,\r\n        return_keys=None,\r\n        quantize_denoised=True,\r\n        inpaint=True,\r\n        plot_denoise_rows=False,\r\n        plot_progressive_rows=True,\r\n        plot_diffusion_rows=True,\r\n        unconditional_guidance_scale=1.0,\r\n        unconditional_guidance_label=None,\r\n        use_ema_scope=True,\r\n        **kwargs,\r\n    ):\r\n        ema_scope = self.ema_scope if use_ema_scope else nullcontext\r\n        use_ddim = ddim_steps is not None\r\n\r\n        log = dict()\r\n        z, c, x, xrec, xc = self.get_input(\r\n            batch, self.first_stage_key, bs=N, return_first_stage_outputs=True\r\n        )\r\n        c_cat, c = c[\"c_concat\"][0], c[\"c_crossattn\"][0]\r\n        N = min(x.shape[0], N)\r\n        n_row = min(x.shape[0], n_row)\r\n        log[\"inputs\"] = x\r\n        log[\"reconstruction\"] = xrec\r\n        if self.model.conditioning_key is not None:\r\n            if hasattr(self.cond_stage_model, \"decode\"):\r\n                xc = self.cond_stage_model.decode(c)\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"caption\", \"txt\"]:\r\n                xc = log_txt_as_img(\r\n                    (x.shape[2], x.shape[3]),\r\n                    batch[self.cond_stage_key],\r\n                    size=x.shape[2] // 25,\r\n                )\r\n                log[\"conditioning\"] = xc\r\n            elif self.cond_stage_key in [\"class_label\", \"cls\"]:\r\n                xc = log_txt_as_img(\r\n                    (x.shape[2], x.shape[3]),\r\n                    batch[\"human_label\"],\r\n                    size=x.shape[2] // 25,\r\n                )\r\n                log[\"conditioning\"] = xc\r\n            elif isimage(xc):\r\n                log[\"conditioning\"] = xc\r\n            if ismap(xc):\r\n                log[\"original_conditioning\"] = self.to_rgb(xc)\r\n\r\n        if not (self.c_concat_log_start is None and self.c_concat_log_end is None):\r\n            log[\"c_concat_decoded\"] = self.decode_first_stage(\r\n                c_cat[:, self.c_concat_log_start : self.c_concat_log_end]\r\n            )\r\n\r\n        if plot_diffusion_rows:\r\n            # get diffusion row\r\n            diffusion_row = list()\r\n            z_start = z[:n_row]\r\n            for t in range(self.num_timesteps):\r\n                if t % self.log_every_t == 0 or t == self.num_timesteps - 1:\r\n                    t = repeat(torch.tensor([t]), \"1 -> b\", b=n_row)\r\n                    t = t.to(self.device).long()\r\n                    noise = torch.randn_like(z_start)\r\n                    z_noisy = self.q_sample(x_start=z_start, t=t, noise=noise)\r\n                    diffusion_row.append(self.decode_first_stage(z_noisy))\r\n\r\n            diffusion_row = torch.stack(diffusion_row)  # n_log_step, n_row, C, H, W\r\n            diffusion_grid = rearrange(diffusion_row, \"n b c h w -> b n c h w\")\r\n            diffusion_grid = rearrange(diffusion_grid, \"b n c h w -> (b n) c h w\")\r\n            diffusion_grid = make_grid(diffusion_grid, nrow=diffusion_row.shape[0])\r\n            log[\"diffusion_row\"] = diffusion_grid\r\n\r\n        if sample:\r\n            # get denoise row\r\n            with ema_scope(\"Sampling\"):\r\n                samples, z_denoise_row = self.sample_log(\r\n                    cond={\"c_concat\": [c_cat], \"c_crossattn\": [c]},\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                )\r\n                # samples, z_denoise_row = self.sample(cond=c, batch_size=N, return_intermediates=True)\r\n            x_samples = self.decode_first_stage(samples)\r\n            log[\"samples\"] = x_samples\r\n            if plot_denoise_rows:\r\n                denoise_grid = self._get_denoise_row_from_list(z_denoise_row)\r\n                log[\"denoise_row\"] = denoise_grid\r\n\r\n        if unconditional_guidance_scale > 1.0:\r\n            uc_cross = self.get_unconditional_conditioning(\r\n                N, unconditional_guidance_label\r\n            )\r\n            uc_cat = c_cat\r\n            uc_full = {\"c_concat\": [uc_cat], \"c_crossattn\": [uc_cross]}\r\n            with ema_scope(\"Sampling with classifier-free guidance\"):\r\n                samples_cfg, _ = self.sample_log(\r\n                    cond={\"c_concat\": [c_cat], \"c_crossattn\": [c]},\r\n                    batch_size=N,\r\n                    ddim=use_ddim,\r\n                    ddim_steps=ddim_steps,\r\n                    eta=ddim_eta,\r\n                    unconditional_guidance_scale=unconditional_guidance_scale,\r\n                    unconditional_conditioning=uc_full,\r\n                )\r\n                x_samples_cfg = self.decode_first_stage(samples_cfg)\r\n                log[f\"samples_cfg_scale_{unconditional_guidance_scale:.2f}\"] = (\r\n                    x_samples_cfg\r\n                )\r\n\r\n        return log\r\n\r\n\r\nclass LatentInpaintDiffusion(LatentFinetuneDiffusion):\r\n    \"\"\"\r\n    can either run as pure inpainting model (only concat mode) or with mixed conditionings,\r\n    e.g. mask as concat and text via cross-attn.\r\n    To disable finetuning mode, set finetune_keys to None\r\n    \"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        concat_keys=(\"mask\", \"masked_image\"),\r\n        masked_image_key=\"masked_image\",\r\n        *args,\r\n        **kwargs,\r\n    ):\r\n        super().__init__(concat_keys, *args, **kwargs)\r\n        self.masked_image_key = masked_image_key\r\n        assert self.masked_image_key in concat_keys\r\n\r\n    @torch.no_grad()\r\n    def get_input(\r\n        self, batch, k, cond_key=None, bs=None, return_first_stage_outputs=False\r\n    ):\r\n        # note: restricted to non-trainable encoders currently\r\n        assert not self.cond_stage_trainable, (\r\n            \"trainable cond stages not yet supported for inpainting\"\r\n        )\r\n        z, c, x, xrec, xc = super().get_input(\r\n            batch,\r\n            self.first_stage_key,\r\n            return_first_stage_outputs=True,\r\n            force_c_encode=True,\r\n            return_original_cond=True,\r\n            bs=bs,\r\n        )\r\n\r\n        assert exists(self.concat_keys)\r\n        c_cat = list()\r\n        for ck in self.concat_keys:\r\n            cc = (\r\n                rearrange(batch[ck], \"b h w c -> b c h w\")\r\n                .to(memory_format=torch.contiguous_format)\r\n                .float()\r\n            )\r\n            if bs is not None:\r\n                cc = cc[:bs]\r\n                cc = cc.to(self.device)\r\n            bchw = z.shape\r\n            if ck != self.masked_image_key:\r\n                cc = torch.nn.functional.interpolate(cc, size=bchw[-2:])\r\n            else:\r\n                cc = self.get_first_stage_encoding(self.encode_first_stage(cc))\r\n            c_cat.append(cc)\r\n        c_cat = torch.cat(c_cat, dim=1)\r\n        all_conds = {\"c_concat\": [c_cat], \"c_crossattn\": [c]}\r\n        if return_first_stage_outputs:\r\n            return z, all_conds, x, xrec, xc\r\n        return z, all_conds\r\n\r\n    @torch.no_grad()\r\n    def log_images(self, *args, **kwargs):\r\n        log = super(LatentInpaintDiffusion, self).log_images(*args, **kwargs)\r\n        log[\"masked_image\"] = (\r\n            rearrange(args[0][\"masked_image\"], \"b h w c -> b c h w\")\r\n            .to(memory_format=torch.contiguous_format)\r\n            .float()\r\n        )\r\n        return log\r\n\r\n\r\nclass LatentDepth2ImageDiffusion(LatentFinetuneDiffusion):\r\n    \"\"\"\r\n    condition on monocular depth estimation\r\n    \"\"\"\r\n\r\n    def __init__(self, depth_stage_config, concat_keys=(\"midas_in\",), *args, **kwargs):\r\n        super().__init__(concat_keys=concat_keys, *args, **kwargs)\r\n        self.depth_model = instantiate_from_config(depth_stage_config)\r\n        self.depth_stage_key = concat_keys[0]\r\n\r\n    @torch.no_grad()\r\n    def get_input(\r\n        self, batch, k, cond_key=None, bs=None, return_first_stage_outputs=False\r\n    ):\r\n        # note: restricted to non-trainable encoders currently\r\n        assert not self.cond_stage_trainable, (\r\n            \"trainable cond stages not yet supported for depth2img\"\r\n        )\r\n        z, c, x, xrec, xc = super().get_input(\r\n            batch,\r\n            self.first_stage_key,\r\n            return_first_stage_outputs=True,\r\n            force_c_encode=True,\r\n            return_original_cond=True,\r\n            bs=bs,\r\n        )\r\n\r\n        assert exists(self.concat_keys)\r\n        assert len(self.concat_keys) == 1\r\n        c_cat = list()\r\n        for ck in self.concat_keys:\r\n            cc = batch[ck]\r\n            if bs is not None:\r\n                cc = cc[:bs]\r\n                cc = cc.to(self.device)\r\n            cc = self.depth_model(cc)\r\n            cc = torch.nn.functional.interpolate(\r\n                cc,\r\n                size=z.shape[2:],\r\n                mode=\"bicubic\",\r\n                align_corners=False,\r\n            )\r\n\r\n            depth_min, depth_max = (\r\n                torch.amin(cc, dim=[1, 2, 3], keepdim=True),\r\n                torch.amax(cc, dim=[1, 2, 3], keepdim=True),\r\n            )\r\n            cc = 2.0 * (cc - depth_min) / (depth_max - depth_min + 0.001) - 1.0\r\n            c_cat.append(cc)\r\n        c_cat = torch.cat(c_cat, dim=1)\r\n        all_conds = {\"c_concat\": [c_cat], \"c_crossattn\": [c]}\r\n        if return_first_stage_outputs:\r\n            return z, all_conds, x, xrec, xc\r\n        return z, all_conds\r\n\r\n    @torch.no_grad()\r\n    def log_images(self, *args, **kwargs):\r\n        log = super().log_images(*args, **kwargs)\r\n        depth = self.depth_model(args[0][self.depth_stage_key])\r\n        depth_min, depth_max = (\r\n            torch.amin(depth, dim=[1, 2, 3], keepdim=True),\r\n            torch.amax(depth, dim=[1, 2, 3], keepdim=True),\r\n        )\r\n        log[\"depth\"] = 2.0 * (depth - depth_min) / (depth_max - depth_min) - 1.0\r\n        return log\r\n\r\n\r\nclass LatentUpscaleFinetuneDiffusion(LatentFinetuneDiffusion):\r\n    \"\"\"\r\n    condition on low-res image (and optionally on some spatial noise augmentation)\r\n    \"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        concat_keys=(\"lr\",),\r\n        reshuffle_patch_size=None,\r\n        low_scale_config=None,\r\n        low_scale_key=None,\r\n        *args,\r\n        **kwargs,\r\n    ):\r\n        super().__init__(concat_keys=concat_keys, *args, **kwargs)\r\n        self.reshuffle_patch_size = reshuffle_patch_size\r\n        self.low_scale_model = None\r\n        if low_scale_config is not None:\r\n            print(\"Initializing a low-scale model\")\r\n            assert exists(low_scale_key)\r\n            self.instantiate_low_stage(low_scale_config)\r\n            self.low_scale_key = low_scale_key\r\n\r\n    def instantiate_low_stage(self, config):\r\n        model = instantiate_from_config(config)\r\n        self.low_scale_model = model.eval()\r\n        self.low_scale_model.train = disabled_train\r\n        for param in self.low_scale_model.parameters():\r\n            param.requires_grad = False\r\n\r\n    @torch.no_grad()\r\n    def get_input(\r\n        self, batch, k, cond_key=None, bs=None, return_first_stage_outputs=False\r\n    ):\r\n        # note: restricted to non-trainable encoders currently\r\n        assert not self.cond_stage_trainable, (\r\n            \"trainable cond stages not yet supported for upscaling-ft\"\r\n        )\r\n        z, c, x, xrec, xc = super().get_input(\r\n            batch,\r\n            self.first_stage_key,\r\n            return_first_stage_outputs=True,\r\n            force_c_encode=True,\r\n            return_original_cond=True,\r\n            bs=bs,\r\n        )\r\n\r\n        assert exists(self.concat_keys)\r\n        assert len(self.concat_keys) == 1\r\n        # optionally make spatial noise_level here\r\n        c_cat = list()\r\n        noise_level = None\r\n        for ck in self.concat_keys:\r\n            cc = batch[ck]\r\n            cc = rearrange(cc, \"b h w c -> b c h w\")\r\n            if exists(self.reshuffle_patch_size):\r\n                assert isinstance(self.reshuffle_patch_size, int)\r\n                cc = rearrange(\r\n                    cc,\r\n                    \"b c (p1 h) (p2 w) -> b (p1 p2 c) h w\",\r\n                    p1=self.reshuffle_patch_size,\r\n                    p2=self.reshuffle_patch_size,\r\n                )\r\n            if bs is not None:\r\n                cc = cc[:bs]\r\n                cc = cc.to(self.device)\r\n            if exists(self.low_scale_model) and ck == self.low_scale_key:\r\n                cc, noise_level = self.low_scale_model(cc)\r\n            c_cat.append(cc)\r\n        c_cat = torch.cat(c_cat, dim=1)\r\n        if exists(noise_level):\r\n            all_conds = {\"c_concat\": [c_cat], \"c_crossattn\": [c], \"c_adm\": noise_level}\r\n        else:\r\n            all_conds = {\"c_concat\": [c_cat], \"c_crossattn\": [c]}\r\n        if return_first_stage_outputs:\r\n            return z, all_conds, x, xrec, xc\r\n        return z, all_conds\r\n\r\n    @torch.no_grad()\r\n    def log_images(self, *args, **kwargs):\r\n        log = super().log_images(*args, **kwargs)\r\n        log[\"lr\"] = rearrange(args[0][\"lr\"], \"b h w c -> b c h w\")\r\n        return log\r\n"}