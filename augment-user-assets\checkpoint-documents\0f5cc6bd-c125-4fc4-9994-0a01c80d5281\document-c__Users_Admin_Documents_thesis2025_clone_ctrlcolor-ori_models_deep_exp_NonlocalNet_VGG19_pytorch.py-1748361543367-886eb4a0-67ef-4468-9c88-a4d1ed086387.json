{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/models_deep_exp/NonlocalNet/VGG19_pytorch.py"}, "modifiedCode": "\"\"\"\nVGG19 PyTorch implementation for CtrlColor contextual loss.\nAdapter module to match the config reference: models_deep_exp.NonlocalNet.VGG19_pytorch\n\nBased on the paper specifications:\n- VGG Model: Standard pretrained VGG19 (<PERSON><PERSON> & <PERSON>isserman 2014)\n- Layers Used: φ³ and φ⁵ (layers 3 and 5)\n- Distance: Cosine similarity\n- Parameter h: 0.01\n\"\"\"\n\nimport torch\nimport torch.nn as nn\nfrom torchvision.models import vgg19\nfrom collections import OrderedDict\n\n# VGG19 layer mapping - matches torchvision.models.vgg19.features\nVGG19_LAYER_MAP = {\n    'conv_1_1': 0, 'conv_1_2': 2, 'pool_1': 4, \n    'conv_2_1': 5, 'conv_2_2': 7, 'pool_2': 9, \n    'conv_3_1': 10, 'conv_3_2': 12, 'conv_3_3': 14, 'conv_3_4': 16, 'pool_3': 18, \n    'conv_4_1': 19, 'conv_4_2': 21, 'conv_4_3': 23, 'conv_4_4': 25, 'pool_4': 27, \n    'conv_5_1': 28, 'conv_5_2': 30, 'conv_5_3': 32, 'conv_5_4': 34, 'pool_5': 36\n}\n\nVGG19_LAYER_INV = {v: k for k, v in VGG19_LAYER_MAP.items()}\n\nclass VGG19_pytorch(nn.Module):\n    \"\"\"\n    VGG19 feature extractor for contextual loss computation.\n    \n    This class is designed to be compatible with the CtrlColor configuration\n    and extract features from specified VGG19 layers for contextual loss.\n    \n    Paper specifications:\n    - Uses layers 3 and 5 (conv_3_2 and conv_5_2)\n    - Standard pretrained VGG19 weights from torchvision\n    \"\"\"\n    \n    def __init__(self, listen_list=None):\n        \"\"\"\n        Initialize VGG19 feature extractor.\n        \n        Args:\n            listen_list: List of layer names to extract features from.\n                        If None, extracts from layers 3 and 5 (paper default).\n        \"\"\"\n        super(VGG19_pytorch, self).__init__()\n        \n        # Load pretrained VGG19\n        vgg = vgg19(pretrained=True)\n        self.vgg_model = vgg.features\n        \n        # Freeze all parameters\n        for param in self.vgg_model.parameters():\n            param.requires_grad = False\n        \n        # Set up layer listening\n        if listen_list is None:\n            # Default to paper specifications: layers 3 and 5\n            listen_list = ['conv_3_2', 'conv_5_2']\n        \n        if listen_list == []:\n            self.listen = []\n        else:\n            self.listen = set()\n            for layer in listen_list:\n                if layer in VGG19_LAYER_MAP:\n                    self.listen.add(VGG19_LAYER_MAP[layer])\n                else:\n                    print(f\"Warning: Layer {layer} not found in VGG19 mapping\")\n        \n        self.features = OrderedDict()\n        \n        # Set to evaluation mode\n        self.eval()\n    \n    def forward(self, x):\n        \"\"\"\n        Forward pass through VGG19 to extract features.\n        \n        Args:\n            x: Input tensor of shape (N, C, H, W)\n            \n        Returns:\n            OrderedDict of extracted features from specified layers\n        \"\"\"\n        self.features.clear()\n        \n        for index, layer in enumerate(self.vgg_model):\n            x = layer(x)\n            if index in self.listen:\n                layer_name = VGG19_LAYER_INV[index]\n                self.features[layer_name] = x.clone()\n        \n        return self.features\n    \n    def get_features_for_contextual_loss(self, x):\n        \"\"\"\n        Extract features specifically for contextual loss computation.\n        \n        Args:\n            x: Input tensor of shape (N, C, H, W)\n            \n        Returns:\n            Dictionary with features from layers 3 and 5\n        \"\"\"\n        features = self.forward(x)\n        \n        # Return features for contextual loss (layers 3 and 5)\n        contextual_features = {}\n        if 'conv_3_2' in features:\n            contextual_features['conv_3_2'] = features['conv_3_2']\n        if 'conv_5_2' in features:\n            contextual_features['conv_5_2'] = features['conv_5_2']\n            \n        return contextual_features\n\n\ndef create_vgg19_for_contextual_loss():\n    \"\"\"\n    Factory function to create VGG19 model configured for CtrlColor contextual loss.\n    \n    Returns:\n        VGG19_pytorch model configured for layers 3 and 5\n    \"\"\"\n    return VGG19_pytorch(listen_list=['conv_3_2', 'conv_5_2'])\n\n\n# For compatibility with CtrlColor's instantiate_from_config\nclass VGG19Config:\n    \"\"\"Configuration class for VGG19 model instantiation.\"\"\"\n    \n    def __init__(self):\n        self.target = \"models_deep_exp.NonlocalNet.VGG19_pytorch\"\n        self.params = {}\n\n\nif __name__ == \"__main__\":\n    # Test the VGG19 implementation\n    print(\"Testing VGG19_pytorch implementation...\")\n    \n    # Create model\n    model = VGG19_pytorch()\n    print(f\"Listening to layers: {[VGG19_LAYER_INV[idx] for idx in model.listen]}\")\n    \n    # Test forward pass\n    test_input = torch.randn(1, 3, 224, 224)\n    features = model(test_input)\n    \n    print(\"Extracted features:\")\n    for layer_name, feature in features.items():\n        print(f\"  {layer_name}: {feature.shape}\")\n    \n    print(\"VGG19_pytorch test completed successfully!\")\n"}