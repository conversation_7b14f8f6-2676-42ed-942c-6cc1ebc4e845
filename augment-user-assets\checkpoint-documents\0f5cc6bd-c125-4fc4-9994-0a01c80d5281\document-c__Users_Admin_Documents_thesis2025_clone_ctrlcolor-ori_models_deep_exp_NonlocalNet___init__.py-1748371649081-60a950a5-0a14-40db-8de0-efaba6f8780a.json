{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\models_deep_exp\\NonlocalNet\\__init__.py"}, "originalCode": "# NonlocalNet package\n", "modifiedCode": "# NonlocalNet package\n\nfrom .contextual_loss import ContextualL<PERSON>, Distance_Type, create_contextual_loss\nfrom .VGG19_pytorch import VGG19_pytorch, create_vgg19_for_contextual_loss\n\n__all__ = [\n    \"VGG19_pytorch\",\n    \"create_vgg19_for_contextual_loss\",\n    \"ContextualLoss\",\n    \"Distance_Type\",\n    \"create_contextual_loss\",\n]\n"}