{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/models_deep_exp/NonlocalNet/contextual_loss.py"}, "modifiedCode": "\"\"\"\nContextual Loss implementation for CtrlColor.\nBased on \"The Contextual Loss for Image Transformation with Non-Aligned Data\"\n\nThis module provides a sophisticated contextual loss implementation\nconfigured for CtrlColor paper specifications:\n- VGG19 layers 3 and 5 (conv_3_2, conv_5_2)\n- Cosine distance\n- h=0.01 bandwidth parameter\n- Layer weights: w₃=2, w₅=8\n\"\"\"\n\nimport torch\nimport torch.nn as nn\n\n\nclass Distance_Type:\n    \"\"\"Distance types for contextual loss computation.\"\"\"\n    L2_Distance = 0\n    L1_Distance = 1\n    Cosine_Distance = 2\n\n\nclass ContextualLoss(nn.Module):\n    \"\"\"\n    Sophisticated Contextual Loss implementation for CtrlColor.\n    Based on \"The Contextual Loss for Image Transformation with Non-Aligned Data\"\n    \n    Configured for CtrlColor paper specifications:\n    - VGG19 layers 3 and 5 (conv_3_2, conv_5_2)\n    - Cosine distance\n    - h=0.01 bandwidth parameter\n    - Layer weights: w₃=2, w₅=8\n    \"\"\"\n\n    def __init__(self, \n                 layers_weights=None, \n                 crop_quarter=False, \n                 max_1d_size=100, \n                 distance_type=Distance_Type.Cosine_Distance, \n                 b=1.0, \n                 h=0.01):\n        super(ContextualLoss, self).__init__()\n        \n        # Default to paper specifications if not provided\n        if layers_weights is None:\n            layers_weights = {\n                \"conv_3_2\": 2.0,  # φ³, w₃=2\n                \"conv_5_2\": 8.0   # φ⁵, w₅=8\n            }\n        \n        self.layers_weights = layers_weights\n        self.crop_quarter = crop_quarter\n        self.distance_type = distance_type\n        self.max_1d_size = max_1d_size\n        self.b = b\n        self.h = h  # Paper uses h=0.01\n        \n        # VGG model will be set by instantiate_contextual_stage\n        self.vgg_model = None\n\n    def set_vgg_model(self, vgg_model):\n        \"\"\"Set the VGG model for feature extraction.\"\"\"\n        self.vgg_model = vgg_model\n\n    def forward(self, images, gt):\n        \"\"\"\n        Compute contextual loss between images and ground truth.\n        \n        Args:\n            images: Input images tensor (B, C, H, W)\n            gt: Ground truth images tensor (B, C, H, W)\n            \n        Returns:\n            Contextual loss value\n        \"\"\"\n        if self.vgg_model is None:\n            raise ValueError(\"VGG model not set. Call set_vgg_model() first.\")\n        \n        device = images.device\n        loss = torch.zeros(1, device=device)\n        \n        # Extract VGG features\n        vgg_images = self.vgg_model(images)\n        vgg_gt = self.vgg_model(gt)\n        \n        # Compute contextual loss for each specified layer\n        for layer_name, weight in self.layers_weights.items():\n            if layer_name not in vgg_images or layer_name not in vgg_gt:\n                print(f\"Warning: Layer {layer_name} not found in VGG features\")\n                continue\n                \n            feat_images = vgg_images[layer_name]\n            feat_gt = vgg_gt[layer_name]\n            \n            N, C, H, W = feat_images.size()\n            \n            # Apply random pooling if feature map is too large\n            if H * W > self.max_1d_size**2:\n                feat_images = self._random_pooling(\n                    feat_images, output_1d_size=self.max_1d_size\n                )\n                feat_gt = self._random_pooling(feat_gt, output_1d_size=self.max_1d_size)\n            \n            # Compute contextual loss for this layer\n            layer_loss = self.calculate_CX_Loss(feat_images, feat_gt)\n            loss += layer_loss * weight\n            \n        return loss\n\n    @staticmethod\n    def _random_sampling(tensor, n, indices):\n        \"\"\"Random sampling of tensor elements.\"\"\"\n        N, C, H, W = tensor.size()\n        S = H * W\n        tensor = tensor.view(N, C, S)\n        if indices is None:\n            indices = torch.randperm(S)[:n].contiguous().type_as(tensor).long()\n            indices = indices.view(1, 1, -1).expand(N, C, -1)\n        indices = ContextualLoss._move_to_current_device(indices)\n        res = torch.gather(tensor, index=indices, dim=-1)\n        return res, indices\n\n    @staticmethod\n    def _move_to_current_device(tensor):\n        \"\"\"Move tensor to current device.\"\"\"\n        if tensor.device.type == \"cuda\":\n            id = torch.cuda.current_device()\n            return tensor.cuda(id)\n        return tensor\n\n    @staticmethod\n    def _random_pooling(feats, output_1d_size=100):\n        \"\"\"Apply random pooling to reduce feature map size.\"\"\"\n        single_input = type(feats) is torch.Tensor\n        \n        if single_input:\n            feats = [feats]\n        \n        N, C, H, W = feats[0].size()\n        feats_sample, indices = ContextualLoss._random_sampling(\n            feats[0], output_1d_size**2, None\n        )\n        res = [feats_sample]\n        \n        for i in range(1, len(feats)):\n            feats_sample, _ = ContextualLoss._random_sampling(feats[i], -1, indices)\n            res.append(feats_sample)\n        \n        res = [\n            feats_sample.view(N, C, output_1d_size, output_1d_size)\n            for feats_sample in res\n        ]\n        \n        if single_input:\n            return res[0]\n        return res\n\n    @staticmethod\n    def _create_using_L2(I_features, T_features):\n        \"\"\"Create distance matrix using L2 distance.\"\"\"\n        assert I_features.size() == T_features.size()\n        N, C, H, W = I_features.size()\n        \n        Ivecs = I_features.view(N, C, -1)\n        Tvecs = T_features.view(N, C, -1)\n        \n        square_I = torch.sum(Ivecs * Ivecs, dim=1, keepdim=False)\n        square_T = torch.sum(Tvecs * Tvecs, dim=1, keepdim=False)\n        \n        raw_distance = []\n        for i in range(N):\n            Ivec, Tvec, s_I, s_T = (\n                Ivecs[i, ...],\n                Tvecs[i, ...],\n                square_I[i, ...],\n                square_T[i, ...],\n            )\n            AB = Ivec.permute(1, 0) @ Tvec\n            dist = s_I.view(-1, 1) + s_T.view(1, -1) - 2 * AB\n            raw_distance.append(dist.view(1, H, W, H * W))\n        \n        raw_distance = torch.cat(raw_distance, dim=0)\n        raw_distance = torch.clamp(raw_distance, min=0.0)\n        return raw_distance\n\n    @staticmethod\n    def _create_using_L1(I_features, T_features):\n        \"\"\"Create distance matrix using L1 distance.\"\"\"\n        assert I_features.size() == T_features.size()\n        N, C, H, W = I_features.size()\n        \n        Ivecs = I_features.view(N, C, -1)\n        Tvecs = T_features.view(N, C, -1)\n        \n        raw_distance = []\n        for i in range(N):\n            Ivec, Tvec = Ivecs[i, ...], Tvecs[i, ...]\n            dist = torch.sum(\n                torch.abs(Ivec.view(C, -1, 1) - Tvec.view(C, 1, -1)),\n                dim=0,\n                keepdim=False,\n            )\n            raw_distance.append(dist.view(1, H, W, H * W))\n        \n        raw_distance = torch.cat(raw_distance, dim=0)\n        return raw_distance\n\n    @staticmethod\n    def _centered_by_T(I, T):\n        \"\"\"Center features by T's mean.\"\"\"\n        mean_T = (\n            T.mean(dim=0, keepdim=True)\n            .mean(dim=2, keepdim=True)\n            .mean(dim=3, keepdim=True)\n        )\n        return I - mean_T, T - mean_T\n\n    @staticmethod\n    def _normalized_L2_channelwise(tensor):\n        \"\"\"Normalize tensor channelwise using L2 norm.\"\"\"\n        norms = tensor.norm(p=2, dim=1, keepdim=True)\n        return tensor / norms\n"}