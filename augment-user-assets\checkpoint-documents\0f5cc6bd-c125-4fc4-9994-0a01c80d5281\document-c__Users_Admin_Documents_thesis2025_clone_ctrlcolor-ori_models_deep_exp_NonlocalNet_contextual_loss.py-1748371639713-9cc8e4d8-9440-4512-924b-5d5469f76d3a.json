{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\models_deep_exp\\NonlocalNet\\contextual_loss.py"}, "originalCode": "\"\"\"\nContextual Loss implementation for CtrlColor.\nBased on \"The Contextual Loss for Image Transformation with Non-Aligned Data\"\n\nThis module provides a sophisticated contextual loss implementation\nconfigured for CtrlColor paper specifications:\n- VGG19 layers 3 and 5 (conv_3_2, conv_5_2)\n- Cosine distance\n- h=0.01 bandwidth parameter\n- Layer weights: w₃=2, w₅=8\n\"\"\"\n\nimport torch\nimport torch.nn as nn\n\n\nclass Distance_Type:\n    \"\"\"Distance types for contextual loss computation.\"\"\"\n    L2_Distance = 0\n    L1_Distance = 1\n    Cosine_Distance = 2\n\n\nclass ContextualLoss(nn.Module):\n    \"\"\"\n    Sophisticated Contextual Loss implementation for CtrlColor.\n    Based on \"The Contextual Loss for Image Transformation with Non-Aligned Data\"\n\n    Configured for CtrlColor paper specifications:\n    - VGG19 layers 3 and 5 (conv_3_2, conv_5_2)\n    - Cosine distance\n    - h=0.01 bandwidth parameter\n    - Layer weights: w₃=2, w₅=8\n    \"\"\"\n\n    def __init__(self,\n                 layers_weights=None,\n                 crop_quarter=False,\n                 max_1d_size=100,\n                 distance_type=Distance_Type.Cosine_Distance,\n                 b=1.0,\n                 h=0.01):\n        super(ContextualLoss, self).__init__()\n\n        # Default to paper specifications if not provided\n        if layers_weights is None:\n            layers_weights = {\n                \"conv_3_2\": 2.0,  # φ³, w₃=2\n                \"conv_5_2\": 8.0   # φ⁵, w₅=8\n            }\n\n        self.layers_weights = layers_weights\n        self.crop_quarter = crop_quarter\n        self.distance_type = distance_type\n        self.max_1d_size = max_1d_size\n        self.b = b\n        self.h = h  # Paper uses h=0.01\n\n        # VGG model will be set by instantiate_contextual_stage\n        self.vgg_model = None\n\n    def set_vgg_model(self, vgg_model):\n        \"\"\"Set the VGG model for feature extraction.\"\"\"\n        self.vgg_model = vgg_model\n\n    def forward(self, images, gt):\n        \"\"\"\n        Compute contextual loss between images and ground truth.\n\n        Args:\n            images: Input images tensor (B, C, H, W)\n            gt: Ground truth images tensor (B, C, H, W)\n\n        Returns:\n            Contextual loss value\n        \"\"\"\n        if self.vgg_model is None:\n            raise ValueError(\"VGG model not set. Call set_vgg_model() first.\")\n\n        device = images.device\n        loss = torch.zeros(1, device=device)\n\n        # Extract VGG features\n        vgg_images = self.vgg_model(images)\n        vgg_gt = self.vgg_model(gt)\n\n        # Compute contextual loss for each specified layer\n        for layer_name, weight in self.layers_weights.items():\n            if layer_name not in vgg_images or layer_name not in vgg_gt:\n                print(f\"Warning: Layer {layer_name} not found in VGG features\")\n                continue\n\n            feat_images = vgg_images[layer_name]\n            feat_gt = vgg_gt[layer_name]\n\n            N, C, H, W = feat_images.size()\n\n            # Apply random pooling if feature map is too large\n            if H * W > self.max_1d_size**2:\n                feat_images = self._random_pooling(\n                    feat_images, output_1d_size=self.max_1d_size\n                )\n                feat_gt = self._random_pooling(feat_gt, output_1d_size=self.max_1d_size)\n\n            # Compute contextual loss for this layer\n            layer_loss = self.calculate_CX_Loss(feat_images, feat_gt)\n            loss += layer_loss * weight\n\n        return loss\n\n    @staticmethod\n    def _random_sampling(tensor, n, indices):\n        \"\"\"Random sampling of tensor elements.\"\"\"\n        N, C, H, W = tensor.size()\n        S = H * W\n        tensor = tensor.view(N, C, S)\n        if indices is None:\n            indices = torch.randperm(S)[:n].contiguous().type_as(tensor).long()\n            indices = indices.view(1, 1, -1).expand(N, C, -1)\n        indices = ContextualLoss._move_to_current_device(indices)\n        res = torch.gather(tensor, index=indices, dim=-1)\n        return res, indices\n\n    @staticmethod\n    def _move_to_current_device(tensor):\n        \"\"\"Move tensor to current device.\"\"\"\n        if tensor.device.type == \"cuda\":\n            id = torch.cuda.current_device()\n            return tensor.cuda(id)\n        return tensor\n\n    @staticmethod\n    def _random_pooling(feats, output_1d_size=100):\n        \"\"\"Apply random pooling to reduce feature map size.\"\"\"\n        single_input = type(feats) is torch.Tensor\n\n        if single_input:\n            feats = [feats]\n\n        N, C, H, W = feats[0].size()\n        feats_sample, indices = ContextualLoss._random_sampling(\n            feats[0], output_1d_size**2, None\n        )\n        res = [feats_sample]\n\n        for i in range(1, len(feats)):\n            feats_sample, _ = ContextualLoss._random_sampling(feats[i], -1, indices)\n            res.append(feats_sample)\n\n        res = [\n            feats_sample.view(N, C, output_1d_size, output_1d_size)\n            for feats_sample in res\n        ]\n\n        if single_input:\n            return res[0]\n        return res\n\n    @staticmethod\n    def _create_using_L2(I_features, T_features):\n        \"\"\"Create distance matrix using L2 distance.\"\"\"\n        assert I_features.size() == T_features.size()\n        N, C, H, W = I_features.size()\n\n        Ivecs = I_features.view(N, C, -1)\n        Tvecs = T_features.view(N, C, -1)\n\n        square_I = torch.sum(Ivecs * Ivecs, dim=1, keepdim=False)\n        square_T = torch.sum(Tvecs * Tvecs, dim=1, keepdim=False)\n\n        raw_distance = []\n        for i in range(N):\n            Ivec, Tvec, s_I, s_T = (\n                Ivecs[i, ...],\n                Tvecs[i, ...],\n                square_I[i, ...],\n                square_T[i, ...],\n            )\n            AB = Ivec.permute(1, 0) @ Tvec\n            dist = s_I.view(-1, 1) + s_T.view(1, -1) - 2 * AB\n            raw_distance.append(dist.view(1, H, W, H * W))\n\n        raw_distance = torch.cat(raw_distance, dim=0)\n        raw_distance = torch.clamp(raw_distance, min=0.0)\n        return raw_distance\n\n    @staticmethod\n    def _create_using_L1(I_features, T_features):\n        \"\"\"Create distance matrix using L1 distance.\"\"\"\n        assert I_features.size() == T_features.size()\n        N, C, H, W = I_features.size()\n\n        Ivecs = I_features.view(N, C, -1)\n        Tvecs = T_features.view(N, C, -1)\n\n        raw_distance = []\n        for i in range(N):\n            Ivec, Tvec = Ivecs[i, ...], Tvecs[i, ...]\n            dist = torch.sum(\n                torch.abs(Ivec.view(C, -1, 1) - Tvec.view(C, 1, -1)),\n                dim=0,\n                keepdim=False,\n            )\n            raw_distance.append(dist.view(1, H, W, H * W))\n\n        raw_distance = torch.cat(raw_distance, dim=0)\n        return raw_distance\n\n    @staticmethod\n    def _centered_by_T(I, T):\n        \"\"\"Center features by T's mean.\"\"\"\n        mean_T = (\n            T.mean(dim=0, keepdim=True)\n            .mean(dim=2, keepdim=True)\n            .mean(dim=3, keepdim=True)\n        )\n        return I - mean_T, T - mean_T\n\n    @staticmethod\n    def _normalized_L2_channelwise(tensor):\n        \"\"\"Normalize tensor channelwise using L2 norm.\"\"\"\n        norms = tensor.norm(p=2, dim=1, keepdim=True)\n        return tensor / norms\n", "modifiedCode": "\"\"\"\nContextual Loss implementation for CtrlColor.\nBased on \"The Contextual Loss for Image Transformation with Non-Aligned Data\"\n\nThis module provides a sophisticated contextual loss implementation\nconfigured for CtrlColor paper specifications:\n- VGG19 layers 3 and 5 (conv_3_2, conv_5_2)\n- Cosine distance\n- h=0.01 bandwidth parameter\n- Layer weights: w₃=2, w₅=8\n\"\"\"\n\nimport torch\nimport torch.nn as nn\n\n\nclass Distance_Type:\n    \"\"\"Distance types for contextual loss computation.\"\"\"\n\n    L2_Distance = 0\n    L1_Distance = 1\n    Cosine_Distance = 2\n\n\nclass ContextualLoss(nn.Module):\n    \"\"\"\n    Sophisticated Contextual Loss implementation for CtrlColor.\n    Based on \"The Contextual Loss for Image Transformation with Non-Aligned Data\"\n\n    Configured for CtrlColor paper specifications:\n    - VGG19 layers 3 and 5 (conv_3_2, conv_5_2)\n    - Cosine distance\n    - h=0.01 bandwidth parameter\n    - Layer weights: w₃=2, w₅=8\n    \"\"\"\n\n    def __init__(\n        self,\n        layers_weights=None,\n        crop_quarter=False,\n        max_1d_size=100,\n        distance_type=Distance_Type.Cosine_Distance,\n        b=1.0,\n        h=0.01,\n    ):\n        super(ContextualLoss, self).__init__()\n\n        # Default to paper specifications if not provided\n        if layers_weights is None:\n            layers_weights = {\n                \"conv_3_2\": 2.0,  # φ³, w₃=2\n                \"conv_5_2\": 8.0,  # φ⁵, w₅=8\n            }\n\n        self.layers_weights = layers_weights\n        self.crop_quarter = crop_quarter\n        self.distance_type = distance_type\n        self.max_1d_size = max_1d_size\n        self.b = b\n        self.h = h  # Paper uses h=0.01\n\n        # VGG model will be set by instantiate_contextual_stage\n        self.vgg_model = None\n\n    def set_vgg_model(self, vgg_model):\n        \"\"\"Set the VGG model for feature extraction.\"\"\"\n        self.vgg_model = vgg_model\n\n    def forward(self, images, gt):\n        \"\"\"\n        Compute contextual loss between images and ground truth.\n\n        Args:\n            images: Input images tensor (B, C, H, W)\n            gt: Ground truth images tensor (B, C, H, W)\n\n        Returns:\n            Contextual loss value\n        \"\"\"\n        if self.vgg_model is None:\n            raise ValueError(\"VGG model not set. Call set_vgg_model() first.\")\n\n        device = images.device\n        loss = torch.zeros(1, device=device)\n\n        # Extract VGG features\n        vgg_images = self.vgg_model(images)\n        vgg_gt = self.vgg_model(gt)\n\n        # Compute contextual loss for each specified layer\n        for layer_name, weight in self.layers_weights.items():\n            if layer_name not in vgg_images or layer_name not in vgg_gt:\n                print(f\"Warning: Layer {layer_name} not found in VGG features\")\n                continue\n\n            feat_images = vgg_images[layer_name]\n            feat_gt = vgg_gt[layer_name]\n\n            N, C, H, W = feat_images.size()\n\n            # Apply random pooling if feature map is too large\n            if H * W > self.max_1d_size**2:\n                feat_images = self._random_pooling(\n                    feat_images, output_1d_size=self.max_1d_size\n                )\n                feat_gt = self._random_pooling(feat_gt, output_1d_size=self.max_1d_size)\n\n            # Compute contextual loss for this layer\n            layer_loss = self.calculate_CX_Loss(feat_images, feat_gt)\n            loss += layer_loss * weight\n\n        return loss\n\n    @staticmethod\n    def _random_sampling(tensor, n, indices):\n        \"\"\"Random sampling of tensor elements.\"\"\"\n        N, C, H, W = tensor.size()\n        S = H * W\n        tensor = tensor.view(N, C, S)\n        if indices is None:\n            indices = torch.randperm(S)[:n].contiguous().type_as(tensor).long()\n            indices = indices.view(1, 1, -1).expand(N, C, -1)\n        indices = ContextualLoss._move_to_current_device(indices)\n        res = torch.gather(tensor, index=indices, dim=-1)\n        return res, indices\n\n    @staticmethod\n    def _move_to_current_device(tensor):\n        \"\"\"Move tensor to current device.\"\"\"\n        if tensor.device.type == \"cuda\":\n            id = torch.cuda.current_device()\n            return tensor.cuda(id)\n        return tensor\n\n    @staticmethod\n    def _random_pooling(feats, output_1d_size=100):\n        \"\"\"Apply random pooling to reduce feature map size.\"\"\"\n        single_input = type(feats) is torch.Tensor\n\n        if single_input:\n            feats = [feats]\n\n        N, C, H, W = feats[0].size()\n        feats_sample, indices = ContextualLoss._random_sampling(\n            feats[0], output_1d_size**2, None\n        )\n        res = [feats_sample]\n\n        for i in range(1, len(feats)):\n            feats_sample, _ = ContextualLoss._random_sampling(feats[i], -1, indices)\n            res.append(feats_sample)\n\n        res = [\n            feats_sample.view(N, C, output_1d_size, output_1d_size)\n            for feats_sample in res\n        ]\n\n        if single_input:\n            return res[0]\n        return res\n\n    @staticmethod\n    def _create_using_L2(I_features, T_features):\n        \"\"\"Create distance matrix using L2 distance.\"\"\"\n        assert I_features.size() == T_features.size()\n        N, C, H, W = I_features.size()\n\n        Ivecs = I_features.view(N, C, -1)\n        Tvecs = T_features.view(N, C, -1)\n\n        square_I = torch.sum(Ivecs * Ivecs, dim=1, keepdim=False)\n        square_T = torch.sum(Tvecs * Tvecs, dim=1, keepdim=False)\n\n        raw_distance = []\n        for i in range(N):\n            Ivec, Tvec, s_I, s_T = (\n                Ivecs[i, ...],\n                Tvecs[i, ...],\n                square_I[i, ...],\n                square_T[i, ...],\n            )\n            AB = Ivec.permute(1, 0) @ Tvec\n            dist = s_I.view(-1, 1) + s_T.view(1, -1) - 2 * AB\n            raw_distance.append(dist.view(1, H, W, H * W))\n\n        raw_distance = torch.cat(raw_distance, dim=0)\n        raw_distance = torch.clamp(raw_distance, min=0.0)\n        return raw_distance\n\n    @staticmethod\n    def _create_using_L1(I_features, T_features):\n        \"\"\"Create distance matrix using L1 distance.\"\"\"\n        assert I_features.size() == T_features.size()\n        N, C, H, W = I_features.size()\n\n        Ivecs = I_features.view(N, C, -1)\n        Tvecs = T_features.view(N, C, -1)\n\n        raw_distance = []\n        for i in range(N):\n            Ivec, Tvec = Ivecs[i, ...], Tvecs[i, ...]\n            dist = torch.sum(\n                torch.abs(Ivec.view(C, -1, 1) - Tvec.view(C, 1, -1)),\n                dim=0,\n                keepdim=False,\n            )\n            raw_distance.append(dist.view(1, H, W, H * W))\n\n        raw_distance = torch.cat(raw_distance, dim=0)\n        return raw_distance\n\n    @staticmethod\n    def _centered_by_T(I, T):\n        \"\"\"Center features by T's mean.\"\"\"\n        mean_T = (\n            T.mean(dim=0, keepdim=True)\n            .mean(dim=2, keepdim=True)\n            .mean(dim=3, keepdim=True)\n        )\n        return I - mean_T, T - mean_T\n\n    @staticmethod\n    def _normalized_L2_channelwise(tensor):\n        \"\"\"Normalize tensor channelwise using L2 norm.\"\"\"\n        norms = tensor.norm(p=2, dim=1, keepdim=True)\n        return tensor / norms\n\n    @staticmethod\n    def _create_using_dotP(I_features, T_features):\n        \"\"\"Create distance matrix using cosine distance (dot product).\"\"\"\n        assert I_features.size() == T_features.size()\n        I_features, T_features = ContextualLoss._centered_by_T(I_features, T_features)\n        I_features = ContextualLoss._normalized_L2_channelwise(I_features)\n        T_features = ContextualLoss._normalized_L2_channelwise(T_features)\n\n        N, C, H, W = I_features.size()\n        cosine_dist = []\n        for i in range(N):\n            T_features_i = (\n                T_features[i].view(1, 1, C, H * W).permute(3, 2, 0, 1).contiguous()\n            )\n            I_features_i = I_features[i].unsqueeze(0)\n            dist = (\n                torch.nn.functional.conv2d(I_features_i, T_features_i)\n                .permute(0, 2, 3, 1)\n                .contiguous()\n            )\n            cosine_dist.append(dist)\n\n        cosine_dist = torch.cat(cosine_dist, dim=0)\n        cosine_dist = (1 - cosine_dist) / 2\n        cosine_dist = cosine_dist.clamp(min=0.0)\n        return cosine_dist\n\n    @staticmethod\n    def _calculate_relative_distance(raw_distance, epsilon=1e-5):\n        \"\"\"Normalize distances as in Eq. (2) of the paper.\"\"\"\n        div = torch.min(raw_distance, dim=-1, keepdim=True)[0]\n        relative_dist = raw_distance / (div + epsilon)\n        return relative_dist\n\n    def calculate_CX_Loss(self, I_features, T_features):\n        \"\"\"Calculate the contextual loss between I and T features.\"\"\"\n        I_features = ContextualLoss._move_to_current_device(I_features)\n        T_features = ContextualLoss._move_to_current_device(T_features)\n\n        # Check for NaN or Inf\n        if torch.sum(torch.isnan(I_features)) == torch.numel(I_features) or torch.sum(\n            torch.isinf(I_features)\n        ) == torch.numel(I_features):\n            raise ValueError(\"NaN or Inf in I_features\")\n        if torch.sum(torch.isnan(T_features)) == torch.numel(T_features) or torch.sum(\n            torch.isinf(T_features)\n        ) == torch.numel(T_features):\n            raise ValueError(\"NaN or Inf in T_features\")\n\n        # Compute raw distance based on distance type\n        if self.distance_type == Distance_Type.L1_Distance:\n            raw_distance = ContextualLoss._create_using_L1(I_features, T_features)\n        elif self.distance_type == Distance_Type.L2_Distance:\n            raw_distance = ContextualLoss._create_using_L2(I_features, T_features)\n        else:  # Cosine distance (paper default)\n            raw_distance = ContextualLoss._create_using_dotP(I_features, T_features)\n\n        # Calculate relative distance\n        relative_distance = ContextualLoss._calculate_relative_distance(raw_distance)\n        del raw_distance\n\n        # Apply exponential weighting\n        exp_distance = torch.exp((self.b - relative_distance) / self.h)\n        del relative_distance\n\n        # Calculate contextual similarity\n        contextual_sim = exp_distance / torch.sum(exp_distance, dim=-1, keepdim=True)\n        del exp_distance\n\n        # Calculate final loss\n        max_gt_sim = torch.max(torch.max(contextual_sim, dim=1)[0], dim=1)[0]\n        del contextual_sim\n        CS = torch.mean(max_gt_sim, dim=1)\n        CX_loss = torch.mean(-torch.log(CS))\n\n        if torch.isnan(CX_loss):\n            raise ValueError(\"NaN in computing CX_loss\")\n\n        return CX_loss\n\n\ndef create_contextual_loss(\n    layers_weights=None, distance_type=Distance_Type.Cosine_Distance, h=0.01\n):\n    \"\"\"\n    Factory function to create ContextualLoss with CtrlColor paper specifications.\n\n    Args:\n        layers_weights: Dictionary of layer weights. If None, uses paper defaults.\n        distance_type: Distance type to use. Default is Cosine (paper specification).\n        h: Bandwidth parameter. Default is 0.01 (paper specification).\n\n    Returns:\n        ContextualLoss instance configured for CtrlColor\n    \"\"\"\n    if layers_weights is None:\n        layers_weights = {\n            \"conv_3_2\": 2.0,  # φ³, w₃=2\n            \"conv_5_2\": 8.0,  # φ⁵, w₅=8\n        }\n\n    return ContextualLoss(\n        layers_weights=layers_weights, distance_type=distance_type, h=h, b=1.0\n    )\n"}