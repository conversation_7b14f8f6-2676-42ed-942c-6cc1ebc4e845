{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025\\clone\\ctrlcolor-ori", "relPath": "summary.md"}, "modifiedCode": "# Control Color: Multimodal Diffusion-based Interactive Image Colorization\n\n## Project Overview\n\nControl Color (CtrlColor) is an advanced image colorization system that leverages diffusion models to provide highly controllable and multimodal image colorization. Developed by researchers from Nanyang Technological University's S-Lab, the project enables users to colorize grayscale images through various interactive methods, including:\n\n1. **Stroke-based colorization**: Users can draw colored strokes directly on the image\n2. **Text-guided colorization**: Users can provide text prompts to guide the colorization process\n3. **Iterative editing**: Users can refine colorization results through multiple iterations\n\n## Technical Architecture\n\n### Core Components\n\n1. **Diffusion Model**: The system is built on a modified Stable Diffusion architecture, adapted specifically for the colorization task.\n\n2. **ControlNet**: A specialized neural network that allows for controlled generation by conditioning the diffusion process on user inputs (strokes, text prompts).\n\n3. **Autoencoder (VAE)**: Handles the encoding and decoding of images to and from the latent space where the diffusion process operates.\n\n4. **BLIP Caption Model**: Automatically generates text descriptions for images when no user prompt is provided.\n\n5. **Gradio Interface**: Provides an interactive web-based UI for users to interact with the system.\n\n### Key Files and Their Functions\n\n- **test.py**: Main entry point that sets up the Gradio interface and handles user interactions.\n- **cldm/cldm.py**: Contains the ControlNet and ControlLDM implementations.\n- **ldm/models/diffusion/ddpm.py**: Implements the core diffusion model functionality.\n- **ldm/models/autoencoder.py**: Implements the VAE for encoding/decoding images.\n\n## Colorization Process\n\nThe colorization process follows these steps:\n\n1. **Input Processing**:\n   - A grayscale image is uploaded or a color image is converted to grayscale (L channel in LAB color space)\n   - User provides control signals (strokes, text prompts)\n\n2. **Conditioning**:\n   - The system prepares conditioning information from user inputs\n   - For strokes, it creates a mask to identify regions to be colorized\n\n3. **Diffusion Process**:\n   - The diffusion model generates colorized versions of the image guided by the conditioning\n   - The process uses a DDIM sampler for efficient sampling\n\n4. **Post-processing**:\n   - The generated image is decoded from the latent space\n   - The L channel from the original image is preserved and combined with the generated a,b channels (in LAB color space)\n   - The result is converted back to RGB\n\n## User Interface\n\nThe Gradio interface provides several interactive elements:\n\n1. **Image Upload**: Users can upload grayscale images to colorize\n2. **Drawing Tool**: Allows users to draw colored strokes directly on the image\n3. **Text Prompt**: Users can provide text descriptions to guide the colorization\n4. **Advanced Options**:\n   - Control strength: Adjusts how strongly the model follows user guidance\n   - Guidance scale: Controls the adherence to the text prompt\n   - Number of samples: Generates multiple variations\n   - Seed: Controls randomness for reproducibility\n   - SAG parameters: Self-attention guidance parameters for improved quality\n\n## Key Features\n\n1. **Region-specific Colorization**: Users can colorize specific regions of an image while leaving others unchanged.\n\n2. **Iterative Editing**: The system supports iterative refinement, allowing users to build up complex colorizations step by step.\n\n3. **Multimodal Control**: Combines different types of user inputs (strokes, text) for more precise control.\n\n4. **Content-aware Colorization**: The deformable VAE option helps prevent color overflow beyond object boundaries.\n\n5. **Automatic Captioning**: When no text prompt is provided, the system automatically generates a description using BLIP.\n\n## Technical Implementation Details\n\n### Color Space Handling\n\nThe system works primarily in the LAB color space, where:\n- L channel represents lightness (preserved from the original image)\n- a,b channels represent color information (generated by the model)\n\nThis separation allows the system to maintain the original image structure while adding color.\n\n### Mask Generation\n\n```python\ndef get_mask(input_image, hint_image):\n    mask = input_image.copy()\n    H, W, C = input_image.shape\n    for i in range(H):\n        for j in range(W):\n            if input_image[i,j,0] == hint_image[i,j,0]:\n                mask[i,j,:] = 255.\n            else:\n                mask[i,j,:] = 0.\n    kernel = cv2.getStructuringElement(cv2.MORPH_RECT,(3,3))\n    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)\n    return mask\n```\n\nThis function identifies regions where the user has drawn strokes by comparing the original image with the modified one.\n\n### Diffusion Process\n\nThe system uses a modified DDIM sampler for the diffusion process, with additional features like Self-Attention Guidance (SAG) to improve quality:\n\n```python\nsamples, intermediates = ddim_sampler.sample(\n    model, ddim_steps, num_samples,\n    shape, cond, mask=mask, \n    masked_image_latents=masked_image_latents,\n    verbose=False, eta=eta,\n    x_T=init_latents,\n    unconditional_guidance_scale=scale,\n    sag_scale=sag_scale,\n    SAG_influence_step=SAG_influence_step,\n    noise=noise,\n    unconditional_conditioning=un_cond\n)\n```\n\n## Installation and Usage\n\n### Requirements\n\nThe project requires several dependencies listed in `CtrlColor_environ.yaml`, including:\n- PyTorch 1.12.1\n- Gradio 3.31.0\n- OpenCV\n- Various other libraries for image processing and deep learning\n\n### Setup\n\n1. Clone the repository:\n   ```\n   git clone https://github.com/ZhexinLiang/Control-Color.git\n   cd Control_Color\n   ```\n\n2. Create and activate the conda environment:\n   ```\n   conda env create -f CtrlColor_environ.yaml\n   conda activate CtrlColor\n   ```\n\n3. Download model checkpoints from Google Drive and place them in the `./pretrained_models` folder.\n\n4. Run the demo:\n   ```\n   python test.py\n   ```\n\n## Conclusion\n\nControl Color represents a significant advancement in interactive image colorization, providing users with unprecedented control over the colorization process. By leveraging diffusion models and multimodal inputs, it enables both creative expression and realistic colorization of grayscale images.\n\nThe project demonstrates how modern generative AI techniques can be applied to enhance traditional image processing tasks, creating more intuitive and powerful tools for both professional and casual users.\n"}