{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\setup_logging.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nSetup Logging for CtrlColor\n===========================\n\nSets up automatic command logging for CtrlColor.\nCreates aliases and shortcuts for easy logging.\n\"\"\"\n\nimport os\nimport stat\nfrom pathlib import Path\n\n\ndef create_log_aliases():\n    \"\"\"Create convenient aliases for logging\"\"\"\n\n    aliases_content = \"\"\"#!/bin/bash\n# CtrlColor Logging Aliases\n# Source this file to get convenient logging commands\n\n# Create logs directory\nmkdir -p command_logs\n\n# Logging function\nlog_cmd() {\n    python auto_logger.py \"$@\"\n}\n\n# Convenient aliases\nalias log-test=\"log_cmd test.py\"\nalias log-quick=\"log_cmd quick_test.py\"\nalias log-debug=\"log_cmd debug_test.py\"\nalias log-safe=\"log_cmd test_safe.py\"\nalias log-working=\"log_cmd working_test.py\"\nalias log-minimal=\"log_cmd minimal_test.py\"\nalias log-cpu=\"log_cmd cpu_test.py\"\n\n# Test scenarios\nalias log-basic=\"log_cmd run_full_tests.py --scenario basic\"\nalias log-advanced=\"log_cmd run_full_tests.py --scenario advanced\"\nalias log-full=\"log_cmd run_full_tests.py --scenario full\"\n\n# Log viewing\nalias logs=\"python log_viewer.py\"\nalias logs-latest=\"python log_viewer.py latest\"\nalias logs-summary=\"python log_viewer.py summary\"\nalias logs-tail=\"python log_viewer.py tail\"\n\necho \"CtrlColor logging aliases loaded!\"\necho \"Usage examples:\"\necho \"  log-test          # Run test.py with logging\"\necho \"  log-quick         # Run quick_test.py with logging\"\necho \"  log-basic         # Run basic test scenario with logging\"\necho \"  logs              # List all log files\"\necho \"  logs-latest       # Show latest log\"\necho \"  logs-summary      # Show log summary\"\n\"\"\"\n\n    with open(\"logging_aliases.sh\", \"w\") as f:\n        f.write(aliases_content)\n\n    # Make executable\n    os.chmod(\"logging_aliases.sh\", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)\n\n    print(\"Created logging_aliases.sh\")\n\n\ndef create_quick_commands():\n    \"\"\"Create quick command scripts\"\"\"\n\n    # Quick test with logging\n    quick_test_logged = \"\"\"#!/bin/bash\necho \"Quick Test with Logging\"\npython auto_logger.py quick_test.py\n\"\"\"\n\n    with open(\"quick_test_logged.sh\", \"w\") as f:\n        f.write(quick_test_logged)\n    os.chmod(\"quick_test_logged.sh\", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)\n\n    # Test with logging\n    test_logged = \"\"\"#!/bin/bash\necho \"CtrlColor Test with Logging\"\npython auto_logger.py test.py\n\"\"\"\n\n    with open(\"test_logged.sh\", \"w\") as f:\n        f.write(test_logged)\n    os.chmod(\"test_logged.sh\", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)\n\n    # Debug with logging\n    debug_logged = \"\"\"#!/bin/bash\necho \"Debug Test with Logging\"\npython auto_logger.py debug_test.py\n\"\"\"\n\n    with open(\"debug_logged.sh\", \"w\") as f:\n        f.write(debug_logged)\n    os.chmod(\"debug_logged.sh\", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)\n\n    print(\"Created quick command scripts\")\n\n\ndef create_usage_guide():\n    \"\"\"Create usage guide\"\"\"\n\n    guide_content = \"\"\"# CtrlColor Auto Logging System\n\n## Quick Start\n\n### 1. Basic Usage\n```bash\n# Log any Python command\npython auto_logger.py test.py\npython auto_logger.py quick_test.py\npython auto_logger.py run_full_tests.py --scenario basic\n\n# Or use the shell script\n./run_logged.sh python test.py\n```\n\n### 2. Use Convenient Aliases\n```bash\n# Load aliases\nsource logging_aliases.sh\n\n# Use shortcuts\nlog-test          # Run test.py with logging\nlog-quick         # Run quick_test.py with logging\nlog-basic         # Run basic test scenario\nlog-advanced      # Run advanced test scenario\n```\n\n### 3. View Logs\n```bash\n# List all logs\npython log_viewer.py\n\n# Show latest log\npython log_viewer.py latest\n\n# Show specific log\npython log_viewer.py 20231201_120000_test.log\n\n# Search in logs\npython log_viewer.py search \"error\"\npython log_viewer.py search \"segmentation fault\"\n\n# Show summary\npython log_viewer.py summary\n\n# Show last 20 lines of latest log\npython log_viewer.py tail\n```\n\n## File Structure\n```\ncommand_logs/\n├── 20231201_120000_test.log\n├── 20231201_120500_quick_test.log\n├── 20231201_121000_run_full_tests_basic.log\n└── ...\n```\n\n## Log File Format\nEach log file contains:\n- Command that was run\n- Start/end timestamps\n- Working directory\n- Python executable path\n- All stdout/stderr output with timestamps\n- Exit code\n\n## Examples\n\n### Running Tests with Logging\n```bash\n# Test the main interface\npython auto_logger.py test.py\n\n# Quick environment test\npython auto_logger.py quick_test.py\n\n# Debug segmentation fault\npython auto_logger.py debug_test.py\n\n# Run comprehensive tests\npython auto_logger.py run_full_tests.py --scenario full\n```\n\n### Viewing Results\n```bash\n# See all available logs\npython log_viewer.py\n\n# Check latest test results\npython log_viewer.py latest\n\n# Search for errors\npython log_viewer.py search \"error\"\npython log_viewer.py search \"failed\"\npython log_viewer.py search \"segmentation\"\n\n# Get overview\npython log_viewer.py summary\n```\n\n## Benefits\n\n1. **Automatic Capture**: All output automatically saved\n2. **Timestamped**: Every line has a timestamp\n3. **Organized**: Files named by command and timestamp\n4. **Searchable**: Easy to find specific errors or patterns\n5. **Complete**: Captures both stdout and stderr\n6. **Exit Codes**: Records success/failure status\n7. **Easy Viewing**: Built-in log viewer with search\n\n## Troubleshooting\n\n- **Permission denied**: Run `chmod +x *.sh` to make scripts executable\n- **Logs not found**: Check that `command_logs/` directory exists\n- **Large log files**: Use `python log_viewer.py tail` to see recent output\n- **Search not working**: Use simple patterns, regex supported\n\nThis system captures everything for debugging without changing your existing workflow!\n\"\"\"\n\n    with open(\"AUTO_LOGGING_GUIDE.md\", \"w\") as f:\n        f.write(guide_content)\n\n    print(\"Created AUTO_LOGGING_GUIDE.md\")\n\n\ndef setup_logging_system():\n    \"\"\"Setup the complete logging system\"\"\"\n    print(\"Setting up CtrlColor Auto Logging System\")\n    print(\"=\" * 50)\n\n    # Create logs directory\n    Path(\"command_logs\").mkdir(exist_ok=True)\n    print(\"Created command_logs directory\")\n\n    # Make run_logged.sh executable\n    if Path(\"run_logged.sh\").exists():\n        os.chmod(\"run_logged.sh\", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)\n        print(\"Made run_logged.sh executable\")\n\n    # Create convenience files\n    create_log_aliases()\n    create_quick_commands()\n    create_usage_guide()\n\n    print(\"\\nAuto Logging System Setup Complete!\")\n    print(\"=\" * 50)\n    print(\"Files created:\")\n    print(\"  auto_logger.py - Main logging script\")\n    print(\"  run_logged.sh - Shell script wrapper\")\n    print(\"  log_viewer.py - Log viewing tool\")\n    print(\"  logging_aliases.sh - Convenient aliases\")\n    print(\"  *_logged.sh - Quick command scripts\")\n    print(\"  AUTO_LOGGING_GUIDE.md - Usage guide\")\n    print(\"  command_logs/ - Log storage directory\")\n\n    print(\"\\nQuick Start:\")\n    print(\"  # Load aliases\")\n    print(\"  source logging_aliases.sh\")\n    print(\"\")\n    print(\"  # Run commands with logging\")\n    print(\"  log-test              # test.py with logging\")\n    print(\"  log-quick             # quick_test.py with logging\")\n    print(\"  python auto_logger.py <any_command>\")\n    print(\"\")\n    print(\"  # View logs\")\n    print(\"  logs                  # list all logs\")\n    print(\"  logs-latest           # show latest log\")\n    print(\"  logs-summary          # show summary\")\n    print(\"\")\n    print(\"Read AUTO_LOGGING_GUIDE.md for complete documentation\")\n\n\nif __name__ == \"__main__\":\n    setup_logging_system()\n", "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nSetup Logging for CtrlColor\n===========================\n\nSets up automatic command logging for CtrlColor.\nCreates aliases and shortcuts for easy logging.\n\"\"\"\n\nimport os\nimport stat\nfrom pathlib import Path\n\n\ndef create_log_aliases():\n    \"\"\"Create convenient aliases for logging\"\"\"\n\n    aliases_content = \"\"\"#!/bin/bash\n# CtrlColor Logging Aliases\n# Source this file to get convenient logging commands\n\n# Create logs directory\nmkdir -p command_logs\n\n# Logging function\nlog_cmd() {\n    python auto_logger.py \"$@\"\n}\n\n# Convenient aliases\nalias log-test=\"log_cmd test.py\"\nalias log-quick=\"log_cmd quick_test.py\"\nalias log-debug=\"log_cmd debug_test.py\"\nalias log-safe=\"log_cmd test_safe.py\"\nalias log-working=\"log_cmd working_test.py\"\nalias log-minimal=\"log_cmd minimal_test.py\"\nalias log-cpu=\"log_cmd cpu_test.py\"\n\n# Test scenarios\nalias log-basic=\"log_cmd run_full_tests.py --scenario basic\"\nalias log-advanced=\"log_cmd run_full_tests.py --scenario advanced\"\nalias log-full=\"log_cmd run_full_tests.py --scenario full\"\n\n# Log viewing\nalias logs=\"python log_viewer.py\"\nalias logs-latest=\"python log_viewer.py latest\"\nalias logs-summary=\"python log_viewer.py summary\"\nalias logs-tail=\"python log_viewer.py tail\"\n\necho \"CtrlColor logging aliases loaded!\"\necho \"Usage examples:\"\necho \"  log-test          # Run test.py with logging\"\necho \"  log-quick         # Run quick_test.py with logging\"\necho \"  log-basic         # Run basic test scenario with logging\"\necho \"  logs              # List all log files\"\necho \"  logs-latest       # Show latest log\"\necho \"  logs-summary      # Show log summary\"\n\"\"\"\n\n    with open(\"logging_aliases.sh\", \"w\") as f:\n        f.write(aliases_content)\n\n    # Make executable\n    os.chmod(\"logging_aliases.sh\", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)\n\n    print(\"Created logging_aliases.sh\")\n\n\ndef create_quick_commands():\n    \"\"\"Create quick command scripts\"\"\"\n\n    # Quick test with logging\n    quick_test_logged = \"\"\"#!/bin/bash\necho \"Quick Test with Logging\"\npython auto_logger.py quick_test.py\n\"\"\"\n\n    with open(\"quick_test_logged.sh\", \"w\") as f:\n        f.write(quick_test_logged)\n    os.chmod(\"quick_test_logged.sh\", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)\n\n    # Test with logging\n    test_logged = \"\"\"#!/bin/bash\necho \"CtrlColor Test with Logging\"\npython auto_logger.py test.py\n\"\"\"\n\n    with open(\"test_logged.sh\", \"w\") as f:\n        f.write(test_logged)\n    os.chmod(\"test_logged.sh\", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)\n\n    # Debug with logging\n    debug_logged = \"\"\"#!/bin/bash\necho \"Debug Test with Logging\"\npython auto_logger.py debug_test.py\n\"\"\"\n\n    with open(\"debug_logged.sh\", \"w\") as f:\n        f.write(debug_logged)\n    os.chmod(\"debug_logged.sh\", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)\n\n    print(\"Created quick command scripts\")\n\n\ndef create_usage_guide():\n    \"\"\"Create usage guide\"\"\"\n\n    guide_content = \"\"\"# CtrlColor Auto Logging System\n\n## Quick Start\n\n### 1. Basic Usage\n```bash\n# Log any Python command\npython auto_logger.py test.py\npython auto_logger.py quick_test.py\npython auto_logger.py run_full_tests.py --scenario basic\n\n# Or use the shell script\n./run_logged.sh python test.py\n```\n\n### 2. Use Convenient Aliases\n```bash\n# Load aliases\nsource logging_aliases.sh\n\n# Use shortcuts\nlog-test          # Run test.py with logging\nlog-quick         # Run quick_test.py with logging\nlog-basic         # Run basic test scenario\nlog-advanced      # Run advanced test scenario\n```\n\n### 3. View Logs\n```bash\n# List all logs\npython log_viewer.py\n\n# Show latest log\npython log_viewer.py latest\n\n# Show specific log\npython log_viewer.py 20231201_120000_test.log\n\n# Search in logs\npython log_viewer.py search \"error\"\npython log_viewer.py search \"segmentation fault\"\n\n# Show summary\npython log_viewer.py summary\n\n# Show last 20 lines of latest log\npython log_viewer.py tail\n```\n\n## File Structure\n```\ncommand_logs/\n├── 20231201_120000_test.log\n├── 20231201_120500_quick_test.log\n├── 20231201_121000_run_full_tests_basic.log\n└── ...\n```\n\n## Log File Format\nEach log file contains:\n- Command that was run\n- Start/end timestamps\n- Working directory\n- Python executable path\n- All stdout/stderr output with timestamps\n- Exit code\n\n## Examples\n\n### Running Tests with Logging\n```bash\n# Test the main interface\npython auto_logger.py test.py\n\n# Quick environment test\npython auto_logger.py quick_test.py\n\n# Debug segmentation fault\npython auto_logger.py debug_test.py\n\n# Run comprehensive tests\npython auto_logger.py run_full_tests.py --scenario full\n```\n\n### Viewing Results\n```bash\n# See all available logs\npython log_viewer.py\n\n# Check latest test results\npython log_viewer.py latest\n\n# Search for errors\npython log_viewer.py search \"error\"\npython log_viewer.py search \"failed\"\npython log_viewer.py search \"segmentation\"\n\n# Get overview\npython log_viewer.py summary\n```\n\n## Benefits\n\n1. **Automatic Capture**: All output automatically saved\n2. **Timestamped**: Every line has a timestamp\n3. **Organized**: Files named by command and timestamp\n4. **Searchable**: Easy to find specific errors or patterns\n5. **Complete**: Captures both stdout and stderr\n6. **Exit Codes**: Records success/failure status\n7. **Easy Viewing**: Built-in log viewer with search\n\n## Troubleshooting\n\n- **Permission denied**: Run `chmod +x *.sh` to make scripts executable\n- **Logs not found**: Check that `command_logs/` directory exists\n- **Large log files**: Use `python log_viewer.py tail` to see recent output\n- **Search not working**: Use simple patterns, regex supported\n\nThis system captures everything for debugging without changing your existing workflow!\n\"\"\"\n\n    with open(\"AUTO_LOGGING_GUIDE.md\", \"w\") as f:\n        f.write(guide_content)\n\n    print(\"Created AUTO_LOGGING_GUIDE.md\")\n\n\ndef setup_logging_system():\n    \"\"\"Setup the complete logging system\"\"\"\n    print(\"Setting up CtrlColor Auto Logging System\")\n    print(\"=\" * 50)\n\n    # Create logs directory\n    Path(\"command_logs\").mkdir(exist_ok=True)\n    print(\"Created command_logs directory\")\n\n    # Make run_logged.sh executable\n    if Path(\"run_logged.sh\").exists():\n        os.chmod(\"run_logged.sh\", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)\n        print(\"Made run_logged.sh executable\")\n\n    # Create convenience files\n    create_log_aliases()\n    create_quick_commands()\n    create_usage_guide()\n\n    print(\"\\nAuto Logging System Setup Complete!\")\n    print(\"=\" * 50)\n    print(\"Files created:\")\n    print(\"  auto_logger.py - Main logging script\")\n    print(\"  run_logged.sh - Shell script wrapper\")\n    print(\"  log_viewer.py - Log viewing tool\")\n    print(\"  logging_aliases.sh - Convenient aliases\")\n    print(\"  *_logged.sh - Quick command scripts\")\n    print(\"  AUTO_LOGGING_GUIDE.md - Usage guide\")\n    print(\"  command_logs/ - Log storage directory\")\n\n    print(\"\\nQuick Start:\")\n    print(\"  # Load aliases\")\n    print(\"  source logging_aliases.sh\")\n    print(\"\")\n    print(\"  # Run commands with logging\")\n    print(\"  log-test              # test.py with logging\")\n    print(\"  log-quick             # quick_test.py with logging\")\n    print(\"  python auto_logger.py <any_command>\")\n    print(\"\")\n    print(\"  # View logs\")\n    print(\"  logs                  # list all logs\")\n    print(\"  logs-latest           # show latest log\")\n    print(\"  logs-summary          # show summary\")\n    print(\"\")\n    print(\"Read AUTO_LOGGING_GUIDE.md for complete documentation\")\n\n\nif __name__ == \"__main__\":\n    setup_logging_system()\n"}