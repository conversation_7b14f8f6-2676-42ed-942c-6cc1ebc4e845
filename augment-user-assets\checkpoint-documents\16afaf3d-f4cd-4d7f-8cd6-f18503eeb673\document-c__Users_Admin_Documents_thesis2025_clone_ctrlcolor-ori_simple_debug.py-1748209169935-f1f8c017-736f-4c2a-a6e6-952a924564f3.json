{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/simple_debug.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nSimple Debug Test for CtrlColor\n===============================\n\nA simplified debug script to identify the segmentation fault issue.\n\"\"\"\n\nimport os\nimport sys\nimport traceback\n\ndef test_step_by_step():\n    \"\"\"Test imports step by step to isolate the segfault\"\"\"\n    print(\"🔍 Testing Step by Step...\")\n    \n    steps = [\n        (\"numpy\", \"import numpy as np\"),\n        (\"cv2\", \"import cv2\"),\n        (\"torch\", \"import torch\"),\n        (\"matplotlib\", \"import matplotlib; matplotlib.use('Agg')\"),\n        (\"einops\", \"import einops\"),\n        (\"transformers\", \"import transformers\"),\n        (\"omegaconf\", \"from omegaconf import OmegaConf\"),\n        (\"config\", \"import config\"),\n        (\"share\", \"import share\"),\n        (\"cldm.model\", \"from cldm.model import create_model\"),\n        (\"ldm.util\", \"from ldm.util import instantiate_from_config\"),\n        (\"test module\", \"import test\"),\n    ]\n    \n    for step_name, import_cmd in steps:\n        try:\n            print(f\"  📦 {step_name}... \", end=\"\", flush=True)\n            exec(import_cmd)\n            print(\"✅\")\n        except Exception as e:\n            print(f\"❌ Error: {e}\")\n            print(f\"💥 Segfault likely occurs at: {step_name}\")\n            traceback.print_exc()\n            return False\n    \n    print(\"🎉 All imports successful!\")\n    return True\n\ndef test_torch_cuda():\n    \"\"\"Test PyTorch and CUDA\"\"\"\n    print(\"\\n🔧 Testing PyTorch and CUDA...\")\n    \n    try:\n        import torch\n        print(f\"  ✅ PyTorch version: {torch.__version__}\")\n        print(f\"  ✅ CUDA available: {torch.cuda.is_available()}\")\n        \n        if torch.cuda.is_available():\n            print(f\"  ✅ CUDA version: {torch.version.cuda}\")\n            print(f\"  ✅ GPU count: {torch.cuda.device_count()}\")\n            print(f\"  ✅ Current device: {torch.cuda.current_device()}\")\n            print(f\"  ✅ Device name: {torch.cuda.get_device_name(0)}\")\n            \n            # Test basic CUDA operation\n            x = torch.randn(10, 10).cuda()\n            y = torch.mm(x, x.t())\n            print(f\"  ✅ Basic CUDA operation successful\")\n            \n            # Clean up\n            del x, y\n            torch.cuda.empty_cache()\n        \n        return True\n        \n    except Exception as e:\n        print(f\"  ❌ PyTorch/CUDA error: {e}\")\n        return False\n\ndef test_model_files():\n    \"\"\"Test model file accessibility\"\"\"\n    print(\"\\n📁 Testing Model Files...\")\n    \n    model_files = [\n        \"pretrained_models/main_model.ckpt\",\n        \"pretrained_models/content-guided_deformable_vae.ckpt\"\n    ]\n    \n    for model_file in model_files:\n        if os.path.exists(model_file):\n            size_mb = os.path.getsize(model_file) / (1024 * 1024)\n            print(f\"  ✅ {model_file} ({size_mb:.1f} MB)\")\n        else:\n            print(f\"  ❌ {model_file} - NOT FOUND\")\n            return False\n    \n    return True\n\ndef test_environment():\n    \"\"\"Test environment variables and settings\"\"\"\n    print(\"\\n🌍 Testing Environment...\")\n    \n    env_vars = [\n        \"CUDA_VISIBLE_DEVICES\",\n        \"CUDA_LAUNCH_BLOCKING\", \n        \"PYTORCH_CUDA_ALLOC_CONF\",\n        \"MPLBACKEND\"\n    ]\n    \n    for var in env_vars:\n        value = os.environ.get(var, \"Not set\")\n        print(f\"  • {var}: {value}\")\n    \n    return True\n\ndef main():\n    \"\"\"Main debug function\"\"\"\n    print(\"🔍 Simple CtrlColor Debug\")\n    print(\"=\" * 40)\n    print(\"This will help identify where the segmentation fault occurs.\\n\")\n    \n    # Set safe environment\n    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'\n    os.environ['MPLBACKEND'] = 'Agg'\n    \n    tests = [\n        (\"Environment\", test_environment),\n        (\"Model Files\", test_model_files),\n        (\"PyTorch/CUDA\", test_torch_cuda),\n        (\"Step-by-Step Imports\", test_step_by_step),\n    ]\n    \n    for test_name, test_func in tests:\n        print(f\"\\n{'='*15} {test_name} {'='*15}\")\n        try:\n            success = test_func()\n            if not success:\n                print(f\"\\n💥 Test failed at: {test_name}\")\n                break\n        except Exception as e:\n            print(f\"\\n💥 Exception in {test_name}: {e}\")\n            traceback.print_exc()\n            break\n    \n    print(\"\\n\" + \"=\" * 40)\n    print(\"🔍 Debug completed. Check the output above to see where it failed.\")\n    print(\"=\" * 40)\n\nif __name__ == \"__main__\":\n    main()\n"}