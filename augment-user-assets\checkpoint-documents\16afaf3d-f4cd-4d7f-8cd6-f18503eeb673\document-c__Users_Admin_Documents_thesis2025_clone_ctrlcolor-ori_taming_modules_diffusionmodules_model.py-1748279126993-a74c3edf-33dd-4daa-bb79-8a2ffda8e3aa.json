{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\diffusionmodules\\model.py"}, "originalCode": "# pytorch_diffusion + derived encoder decoder\r\nimport math\r\n\r\nimport numpy as np\r\nimport torch\r\nimport torch.nn as nn\r\n\r\n\r\ndef get_timestep_embedding(timesteps, embedding_dim):\r\n    \"\"\"\r\n    This matches the implementation in Denoising Diffusion Probabilistic Models:\r\n    From Fairseq.\r\n    Build sinusoidal embeddings.\r\n    This matches the implementation in tensor2tensor, but differs slightly\r\n    from the description in Section 3.5 of \"Attention Is All You Need\".\r\n    \"\"\"\r\n    assert len(timesteps.shape) == 1\r\n\r\n    half_dim = embedding_dim // 2\r\n    emb = math.log(10000) / (half_dim - 1)\r\n    emb = torch.exp(torch.arange(half_dim, dtype=torch.float32) * -emb)\r\n    emb = emb.to(device=timesteps.device)\r\n    emb = timesteps.float()[:, None] * emb[None, :]\r\n    emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=1)\r\n    if embedding_dim % 2 == 1:  # zero pad\r\n        emb = torch.nn.functional.pad(emb, (0, 1, 0, 0))\r\n    return emb\r\n\r\n\r\ndef nonlinearity(x):\r\n    # swish\r\n    return x * torch.sigmoid(x)\r\n\r\n\r\ndef Normalize(in_channels):\r\n    return torch.nn.GroupNorm(\r\n        num_groups=32, num_channels=in_channels, eps=1e-6, affine=True\r\n    )\r\n\r\n\r\nclass Upsample(nn.Module):\r\n    def __init__(self, in_channels, with_conv):\r\n        super().__init__()\r\n        self.with_conv = with_conv\r\n        if self.with_conv:\r\n            self.conv = torch.nn.Conv2d(\r\n                in_channels, in_channels, kernel_size=3, stride=1, padding=1\r\n            )\r\n\r\n    def forward(self, x):\r\n        x = torch.nn.functional.interpolate(x, scale_factor=2.0, mode=\"nearest\")\r\n        if self.with_conv:\r\n            x = self.conv(x)\r\n        return x\r\n\r\n\r\nclass Downsample(nn.Module):\r\n    def __init__(self, in_channels, with_conv):\r\n        super().__init__()\r\n        self.with_conv = with_conv\r\n        if self.with_conv:\r\n            # no asymmetric padding in torch conv, must do it ourselves\r\n            self.conv = torch.nn.Conv2d(\r\n                in_channels, in_channels, kernel_size=3, stride=2, padding=0\r\n            )\r\n\r\n    def forward(self, x):\r\n        if self.with_conv:\r\n            pad = (0, 1, 0, 1)\r\n            x = torch.nn.functional.pad(x, pad, mode=\"constant\", value=0)\r\n            x = self.conv(x)\r\n        else:\r\n            x = torch.nn.functional.avg_pool2d(x, kernel_size=2, stride=2)\r\n        return x\r\n\r\n\r\nclass ResnetBlock(nn.Module):\r\n    def __init__(\r\n        self,\r\n        *,\r\n        in_channels,\r\n        out_channels=None,\r\n        conv_shortcut=False,\r\n        dropout,\r\n        temb_channels=512,\r\n    ):\r\n        super().__init__()\r\n        self.in_channels = in_channels\r\n        out_channels = in_channels if out_channels is None else out_channels\r\n        self.out_channels = out_channels\r\n        self.use_conv_shortcut = conv_shortcut\r\n\r\n        self.norm1 = Normalize(in_channels)\r\n        self.conv1 = torch.nn.Conv2d(\r\n            in_channels, out_channels, kernel_size=3, stride=1, padding=1\r\n        )\r\n        if temb_channels > 0:\r\n            self.temb_proj = torch.nn.Linear(temb_channels, out_channels)\r\n        self.norm2 = Normalize(out_channels)\r\n        self.dropout = torch.nn.Dropout(dropout)\r\n        self.conv2 = torch.nn.Conv2d(\r\n            out_channels, out_channels, kernel_size=3, stride=1, padding=1\r\n        )\r\n        if self.in_channels != self.out_channels:\r\n            if self.use_conv_shortcut:\r\n                self.conv_shortcut = torch.nn.Conv2d(\r\n                    in_channels, out_channels, kernel_size=3, stride=1, padding=1\r\n                )\r\n            else:\r\n                self.nin_shortcut = torch.nn.Conv2d(\r\n                    in_channels, out_channels, kernel_size=1, stride=1, padding=0\r\n                )\r\n\r\n    def forward(self, x, temb):\r\n        h = x\r\n        h = self.norm1(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv1(h)\r\n\r\n        if temb is not None:\r\n            h = h + self.temb_proj(nonlinearity(temb))[:, :, None, None]\r\n\r\n        h = self.norm2(h)\r\n        h = nonlinearity(h)\r\n        h = self.dropout(h)\r\n        h = self.conv2(h)\r\n\r\n        if self.in_channels != self.out_channels:\r\n            if self.use_conv_shortcut:\r\n                x = self.conv_shortcut(x)\r\n            else:\r\n                x = self.nin_shortcut(x)\r\n\r\n        return x + h\r\n\r\n\r\nclass AttnBlock(nn.Module):\r\n    def __init__(self, in_channels):\r\n        super().__init__()\r\n        self.in_channels = in_channels\r\n\r\n        self.norm = Normalize(in_channels)\r\n        self.q = torch.nn.Conv2d(\r\n            in_channels, in_channels, kernel_size=1, stride=1, padding=0\r\n        )\r\n        self.k = torch.nn.Conv2d(\r\n            in_channels, in_channels, kernel_size=1, stride=1, padding=0\r\n        )\r\n        self.v = torch.nn.Conv2d(\r\n            in_channels, in_channels, kernel_size=1, stride=1, padding=0\r\n        )\r\n        self.proj_out = torch.nn.Conv2d(\r\n            in_channels, in_channels, kernel_size=1, stride=1, padding=0\r\n        )\r\n\r\n    def forward(self, x):\r\n        h_ = x\r\n        h_ = self.norm(h_)\r\n        q = self.q(h_)\r\n        k = self.k(h_)\r\n        v = self.v(h_)\r\n\r\n        # compute attention\r\n        b, c, h, w = q.shape\r\n        q = q.reshape(b, c, h * w)\r\n        q = q.permute(0, 2, 1)  # b,hw,c\r\n        k = k.reshape(b, c, h * w)  # b,c,hw\r\n        w_ = torch.bmm(q, k)  # b,hw,hw    w[b,i,j]=sum_c q[b,i,c]k[b,c,j]\r\n        w_ = w_ * (int(c) ** (-0.5))\r\n        w_ = torch.nn.functional.softmax(w_, dim=2)\r\n\r\n        # attend to values\r\n        v = v.reshape(b, c, h * w)\r\n        w_ = w_.permute(0, 2, 1)  # b,hw,hw (first hw of k, second of q)\r\n        h_ = torch.bmm(v, w_)  # b, c,hw (hw of q) h_[b,c,j] = sum_i v[b,c,i] w_[b,i,j]\r\n        h_ = h_.reshape(b, c, h, w)\r\n\r\n        h_ = self.proj_out(h_)\r\n\r\n        return x + h_\r\n\r\n\r\nclass Model(nn.Module):\r\n    def __init__(\r\n        self,\r\n        *,\r\n        ch,\r\n        out_ch,\r\n        ch_mult=(1, 2, 4, 8),\r\n        num_res_blocks,\r\n        attn_resolutions,\r\n        dropout=0.0,\r\n        resamp_with_conv=True,\r\n        in_channels,\r\n        resolution,\r\n        use_timestep=True,\r\n    ):\r\n        super().__init__()\r\n        self.ch = ch\r\n        self.temb_ch = self.ch * 4\r\n        self.num_resolutions = len(ch_mult)\r\n        self.num_res_blocks = num_res_blocks\r\n        self.resolution = resolution\r\n        self.in_channels = in_channels\r\n\r\n        self.use_timestep = use_timestep\r\n        if self.use_timestep:\r\n            # timestep embedding\r\n            self.temb = nn.Module()\r\n            self.temb.dense = nn.ModuleList(\r\n                [\r\n                    torch.nn.Linear(self.ch, self.temb_ch),\r\n                    torch.nn.Linear(self.temb_ch, self.temb_ch),\r\n                ]\r\n            )\r\n\r\n        # downsampling\r\n        self.conv_in = torch.nn.Conv2d(\r\n            in_channels, self.ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n        curr_res = resolution\r\n        in_ch_mult = (1,) + tuple(ch_mult)\r\n        self.down = nn.ModuleList()\r\n        for i_level in range(self.num_resolutions):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_in = ch * in_ch_mult[i_level]\r\n            block_out = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks):\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            down = nn.Module()\r\n            down.block = block\r\n            down.attn = attn\r\n            if i_level != self.num_resolutions - 1:\r\n                down.downsample = Downsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res // 2\r\n            self.down.append(down)\r\n\r\n        # middle\r\n        self.mid = nn.Module()\r\n        self.mid.block_1 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n        self.mid.attn_1 = AttnBlock(block_in)\r\n        self.mid.block_2 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n\r\n        # upsampling\r\n        self.up = nn.ModuleList()\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_out = ch * ch_mult[i_level]\r\n            skip_in = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                if i_block == self.num_res_blocks:\r\n                    skip_in = ch * in_ch_mult[i_level]\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in + skip_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            up = nn.Module()\r\n            up.block = block\r\n            up.attn = attn\r\n            if i_level != 0:\r\n                up.upsample = Upsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res * 2\r\n            self.up.insert(0, up)  # prepend to get consistent order\r\n\r\n        # end\r\n        self.norm_out = Normalize(block_in)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            block_in, out_ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n    def forward(self, x, t=None):\r\n        # assert x.shape[2] == x.shape[3] == self.resolution\r\n\r\n        if self.use_timestep:\r\n            # timestep embedding\r\n            assert t is not None\r\n            temb = get_timestep_embedding(t, self.ch)\r\n            temb = self.temb.dense[0](temb)\r\n            temb = nonlinearity(temb)\r\n            temb = self.temb.dense[1](temb)\r\n        else:\r\n            temb = None\r\n\r\n        # downsampling\r\n        hs = [self.conv_in(x)]\r\n        for i_level in range(self.num_resolutions):\r\n            for i_block in range(self.num_res_blocks):\r\n                h = self.down[i_level].block[i_block](hs[-1], temb)\r\n                if len(self.down[i_level].attn) > 0:\r\n                    h = self.down[i_level].attn[i_block](h)\r\n                hs.append(h)\r\n            if i_level != self.num_resolutions - 1:\r\n                hs.append(self.down[i_level].downsample(hs[-1]))\r\n\r\n        # middle\r\n        h = hs[-1]\r\n        h = self.mid.block_1(h, temb)\r\n        h = self.mid.attn_1(h)\r\n        h = self.mid.block_2(h, temb)\r\n\r\n        # upsampling\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                h = self.up[i_level].block[i_block](\r\n                    torch.cat([h, hs.pop()], dim=1), temb\r\n                )\r\n                if len(self.up[i_level].attn) > 0:\r\n                    h = self.up[i_level].attn[i_block](h)\r\n            if i_level != 0:\r\n                h = self.up[i_level].upsample(h)\r\n\r\n        # end\r\n        h = self.norm_out(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv_out(h)\r\n        return h\r\n\r\n\r\nclass Encoder(nn.Module):\r\n    def __init__(\r\n        self,\r\n        *,\r\n        ch,\r\n        out_ch,\r\n        ch_mult=(1, 2, 4, 8),\r\n        num_res_blocks,\r\n        attn_resolutions,\r\n        dropout=0.0,\r\n        resamp_with_conv=True,\r\n        in_channels,\r\n        resolution,\r\n        z_channels,\r\n        double_z=True,\r\n        **ignore_kwargs,\r\n    ):\r\n        super().__init__()\r\n        self.ch = ch\r\n        self.temb_ch = 0\r\n        self.num_resolutions = len(ch_mult)\r\n        self.num_res_blocks = num_res_blocks\r\n        self.resolution = resolution\r\n        self.in_channels = in_channels\r\n\r\n        # downsampling\r\n        self.conv_in = torch.nn.Conv2d(\r\n            in_channels, self.ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n        curr_res = resolution\r\n        in_ch_mult = (1,) + tuple(ch_mult)\r\n        self.down = nn.ModuleList()\r\n        for i_level in range(self.num_resolutions):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_in = ch * in_ch_mult[i_level]\r\n            block_out = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks):\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            down = nn.Module()\r\n            down.block = block\r\n            down.attn = attn\r\n            if i_level != self.num_resolutions - 1:\r\n                down.downsample = Downsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res // 2\r\n            self.down.append(down)\r\n\r\n        # middle\r\n        self.mid = nn.Module()\r\n        self.mid.block_1 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n        self.mid.attn_1 = AttnBlock(block_in)\r\n        self.mid.block_2 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n\r\n        # end\r\n        self.norm_out = Normalize(block_in)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            block_in,\r\n            2 * z_channels if double_z else z_channels,\r\n            kernel_size=3,\r\n            stride=1,\r\n            padding=1,\r\n        )\r\n\r\n    def forward(self, x):\r\n        # assert x.shape[2] == x.shape[3] == self.resolution, \"{}, {}, {}\".format(x.shape[2], x.shape[3], self.resolution)\r\n\r\n        # timestep embedding\r\n        temb = None\r\n\r\n        # downsampling\r\n        hs = [self.conv_in(x)]\r\n        for i_level in range(self.num_resolutions):\r\n            for i_block in range(self.num_res_blocks):\r\n                h = self.down[i_level].block[i_block](hs[-1], temb)\r\n                if len(self.down[i_level].attn) > 0:\r\n                    h = self.down[i_level].attn[i_block](h)\r\n                hs.append(h)\r\n            if i_level != self.num_resolutions - 1:\r\n                hs.append(self.down[i_level].downsample(hs[-1]))\r\n\r\n        # middle\r\n        h = hs[-1]\r\n        h = self.mid.block_1(h, temb)\r\n        h = self.mid.attn_1(h)\r\n        h = self.mid.block_2(h, temb)\r\n\r\n        # end\r\n        h = self.norm_out(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv_out(h)\r\n        return h\r\n\r\n\r\nclass Decoder(nn.Module):\r\n    def __init__(\r\n        self,\r\n        *,\r\n        ch,\r\n        out_ch,\r\n        ch_mult=(1, 2, 4, 8),\r\n        num_res_blocks,\r\n        attn_resolutions,\r\n        dropout=0.0,\r\n        resamp_with_conv=True,\r\n        in_channels,\r\n        resolution,\r\n        z_channels,\r\n        give_pre_end=False,\r\n        **ignorekwargs,\r\n    ):\r\n        super().__init__()\r\n        self.ch = ch\r\n        self.temb_ch = 0\r\n        self.num_resolutions = len(ch_mult)\r\n        self.num_res_blocks = num_res_blocks\r\n        self.resolution = resolution\r\n        self.in_channels = in_channels\r\n        self.give_pre_end = give_pre_end\r\n\r\n        # compute in_ch_mult, block_in and curr_res at lowest res\r\n        in_ch_mult = (1,) + tuple(ch_mult)\r\n        block_in = ch * ch_mult[self.num_resolutions - 1]\r\n        curr_res = resolution // 2 ** (self.num_resolutions - 1)\r\n        self.z_shape = (1, z_channels, curr_res, curr_res)\r\n        print(\r\n            \"Working with z of shape {} = {} dimensions.\".format(\r\n                self.z_shape, np.prod(self.z_shape)\r\n            )\r\n        )\r\n\r\n        # z to block_in\r\n        self.conv_in = torch.nn.Conv2d(\r\n            z_channels, block_in, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n        # middle\r\n        self.mid = nn.Module()\r\n        self.mid.block_1 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n        self.mid.attn_1 = AttnBlock(block_in)\r\n        self.mid.block_2 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n\r\n        # upsampling\r\n        self.up = nn.ModuleList()\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_out = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            up = nn.Module()\r\n            up.block = block\r\n            up.attn = attn\r\n            if i_level != 0:\r\n                up.upsample = Upsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res * 2\r\n            self.up.insert(0, up)  # prepend to get consistent order\r\n\r\n        # end\r\n        self.norm_out = Normalize(block_in)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            block_in, out_ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n    def forward(self, z):\r\n        # assert z.shape[1:] == self.z_shape[1:]\r\n        self.last_z_shape = z.shape\r\n\r\n        # timestep embedding\r\n        temb = None\r\n\r\n        # z to block_in\r\n        h = self.conv_in(z)\r\n\r\n        # middle\r\n        h = self.mid.block_1(h, temb)\r\n        h = self.mid.attn_1(h)\r\n        h = self.mid.block_2(h, temb)\r\n\r\n        # upsampling\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                h = self.up[i_level].block[i_block](h, temb)\r\n                if len(self.up[i_level].attn) > 0:\r\n                    h = self.up[i_level].attn[i_block](h)\r\n            if i_level != 0:\r\n                h = self.up[i_level].upsample(h)\r\n\r\n        # end\r\n        if self.give_pre_end:\r\n            return h\r\n\r\n        h = self.norm_out(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv_out(h)\r\n        return h\r\n\r\n\r\nclass VUNet(nn.Module):\r\n    def __init__(\r\n        self,\r\n        *,\r\n        ch,\r\n        out_ch,\r\n        ch_mult=(1, 2, 4, 8),\r\n        num_res_blocks,\r\n        attn_resolutions,\r\n        dropout=0.0,\r\n        resamp_with_conv=True,\r\n        in_channels,\r\n        c_channels,\r\n        resolution,\r\n        z_channels,\r\n        use_timestep=False,\r\n        **ignore_kwargs,\r\n    ):\r\n        super().__init__()\r\n        self.ch = ch\r\n        self.temb_ch = self.ch * 4\r\n        self.num_resolutions = len(ch_mult)\r\n        self.num_res_blocks = num_res_blocks\r\n        self.resolution = resolution\r\n\r\n        self.use_timestep = use_timestep\r\n        if self.use_timestep:\r\n            # timestep embedding\r\n            self.temb = nn.Module()\r\n            self.temb.dense = nn.ModuleList(\r\n                [\r\n                    torch.nn.Linear(self.ch, self.temb_ch),\r\n                    torch.nn.Linear(self.temb_ch, self.temb_ch),\r\n                ]\r\n            )\r\n\r\n        # downsampling\r\n        self.conv_in = torch.nn.Conv2d(\r\n            c_channels, self.ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n        curr_res = resolution\r\n        in_ch_mult = (1,) + tuple(ch_mult)\r\n        self.down = nn.ModuleList()\r\n        for i_level in range(self.num_resolutions):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_in = ch * in_ch_mult[i_level]\r\n            block_out = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks):\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            down = nn.Module()\r\n            down.block = block\r\n            down.attn = attn\r\n            if i_level != self.num_resolutions - 1:\r\n                down.downsample = Downsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res // 2\r\n            self.down.append(down)\r\n\r\n        self.z_in = torch.nn.Conv2d(\r\n            z_channels, block_in, kernel_size=1, stride=1, padding=0\r\n        )\r\n        # middle\r\n        self.mid = nn.Module()\r\n        self.mid.block_1 = ResnetBlock(\r\n            in_channels=2 * block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n        self.mid.attn_1 = AttnBlock(block_in)\r\n        self.mid.block_2 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n\r\n        # upsampling\r\n        self.up = nn.ModuleList()\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_out = ch * ch_mult[i_level]\r\n            skip_in = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                if i_block == self.num_res_blocks:\r\n                    skip_in = ch * in_ch_mult[i_level]\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in + skip_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            up = nn.Module()\r\n            up.block = block\r\n            up.attn = attn\r\n            if i_level != 0:\r\n                up.upsample = Upsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res * 2\r\n            self.up.insert(0, up)  # prepend to get consistent order\r\n\r\n        # end\r\n        self.norm_out = Normalize(block_in)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            block_in, out_ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n    def forward(self, x, z, t=None):\r\n        # assert x.shape[2] == x.shape[3] == self.resolution\r\n\r\n        if self.use_timestep:\r\n            # timestep embedding\r\n            assert t is not None\r\n            temb = get_timestep_embedding(t, self.ch)\r\n            temb = self.temb.dense[0](temb)\r\n            temb = nonlinearity(temb)\r\n            temb = self.temb.dense[1](temb)\r\n        else:\r\n            temb = None\r\n\r\n        # downsampling\r\n        hs = [self.conv_in(x)]\r\n        for i_level in range(self.num_resolutions):\r\n            for i_block in range(self.num_res_blocks):\r\n                h = self.down[i_level].block[i_block](hs[-1], temb)\r\n                if len(self.down[i_level].attn) > 0:\r\n                    h = self.down[i_level].attn[i_block](h)\r\n                hs.append(h)\r\n            if i_level != self.num_resolutions - 1:\r\n                hs.append(self.down[i_level].downsample(hs[-1]))\r\n\r\n        # middle\r\n        h = hs[-1]\r\n        z = self.z_in(z)\r\n        h = torch.cat((h, z), dim=1)\r\n        h = self.mid.block_1(h, temb)\r\n        h = self.mid.attn_1(h)\r\n        h = self.mid.block_2(h, temb)\r\n\r\n        # upsampling\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                h = self.up[i_level].block[i_block](\r\n                    torch.cat([h, hs.pop()], dim=1), temb\r\n                )\r\n                if len(self.up[i_level].attn) > 0:\r\n                    h = self.up[i_level].attn[i_block](h)\r\n            if i_level != 0:\r\n                h = self.up[i_level].upsample(h)\r\n\r\n        # end\r\n        h = self.norm_out(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv_out(h)\r\n        return h\r\n\r\n\r\nclass SimpleDecoder(nn.Module):\r\n    def __init__(self, in_channels, out_channels, *args, **kwargs):\r\n        super().__init__()\r\n        self.model = nn.ModuleList(\r\n            [\r\n                nn.Conv2d(in_channels, in_channels, 1),\r\n                ResnetBlock(\r\n                    in_channels=in_channels,\r\n                    out_channels=2 * in_channels,\r\n                    temb_channels=0,\r\n                    dropout=0.0,\r\n                ),\r\n                ResnetBlock(\r\n                    in_channels=2 * in_channels,\r\n                    out_channels=4 * in_channels,\r\n                    temb_channels=0,\r\n                    dropout=0.0,\r\n                ),\r\n                ResnetBlock(\r\n                    in_channels=4 * in_channels,\r\n                    out_channels=2 * in_channels,\r\n                    temb_channels=0,\r\n                    dropout=0.0,\r\n                ),\r\n                nn.Conv2d(2 * in_channels, in_channels, 1),\r\n                Upsample(in_channels, with_conv=True),\r\n            ]\r\n        )\r\n        # end\r\n        self.norm_out = Normalize(in_channels)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            in_channels, out_channels, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n    def forward(self, x):\r\n        for i, layer in enumerate(self.model):\r\n            if i in [1, 2, 3]:\r\n                x = layer(x, None)\r\n            else:\r\n                x = layer(x)\r\n\r\n        h = self.norm_out(x)\r\n        h = nonlinearity(h)\r\n        x = self.conv_out(h)\r\n        return x\r\n\r\n\r\nclass UpsampleDecoder(nn.Module):\r\n    def __init__(\r\n        self,\r\n        in_channels,\r\n        out_channels,\r\n        ch,\r\n        num_res_blocks,\r\n        resolution,\r\n        ch_mult=(2, 2),\r\n        dropout=0.0,\r\n    ):\r\n        super().__init__()\r\n        # upsampling\r\n        self.temb_ch = 0\r\n        self.num_resolutions = len(ch_mult)\r\n        self.num_res_blocks = num_res_blocks\r\n        block_in = in_channels\r\n        curr_res = resolution // 2 ** (self.num_resolutions - 1)\r\n        self.res_blocks = nn.ModuleList()\r\n        self.upsample_blocks = nn.ModuleList()\r\n        for i_level in range(self.num_resolutions):\r\n            res_block = []\r\n            block_out = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                res_block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n            self.res_blocks.append(nn.ModuleList(res_block))\r\n            if i_level != self.num_resolutions - 1:\r\n                self.upsample_blocks.append(Upsample(block_in, True))\r\n                curr_res = curr_res * 2\r\n\r\n        # end\r\n        self.norm_out = Normalize(block_in)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            block_in, out_channels, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n    def forward(self, x):\r\n        # upsampling\r\n        h = x\r\n        for k, i_level in enumerate(range(self.num_resolutions)):\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                h = self.res_blocks[i_level][i_block](h, None)\r\n            if i_level != self.num_resolutions - 1:\r\n                h = self.upsample_blocks[k](h)\r\n        h = self.norm_out(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv_out(h)\r\n        return h\r\n", "modifiedCode": "# pytorch_diffusion + derived encoder decoder\r\nimport math\r\n\r\nimport numpy as np\r\nimport torch\r\nimport torch.nn as nn\r\n\r\n\r\ndef get_timestep_embedding(timesteps, embedding_dim):\r\n    \"\"\"\r\n    This matches the implementation in Denoising Diffusion Probabilistic Models:\r\n    From Fairseq.\r\n    Build sinusoidal embeddings.\r\n    This matches the implementation in tensor2tensor, but differs slightly\r\n    from the description in Section 3.5 of \"Attention Is All You Need\".\r\n    \"\"\"\r\n    assert len(timesteps.shape) == 1\r\n\r\n    half_dim = embedding_dim // 2\r\n    emb = math.log(10000) / (half_dim - 1)\r\n    emb = torch.exp(torch.arange(half_dim, dtype=torch.float32) * -emb)\r\n    emb = emb.to(device=timesteps.device)\r\n    emb = timesteps.float()[:, None] * emb[None, :]\r\n    emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=1)\r\n    if embedding_dim % 2 == 1:  # zero pad\r\n        emb = torch.nn.functional.pad(emb, (0, 1, 0, 0))\r\n    return emb\r\n\r\n\r\ndef nonlinearity(x):\r\n    # swish\r\n    return x * torch.sigmoid(x)\r\n\r\n\r\ndef Normalize(in_channels):\r\n    return torch.nn.GroupNorm(\r\n        num_groups=32, num_channels=in_channels, eps=1e-6, affine=True\r\n    )\r\n\r\n\r\nclass Upsample(nn.Module):\r\n    def __init__(self, in_channels, with_conv):\r\n        super().__init__()\r\n        self.with_conv = with_conv\r\n        if self.with_conv:\r\n            self.conv = torch.nn.Conv2d(\r\n                in_channels, in_channels, kernel_size=3, stride=1, padding=1\r\n            )\r\n\r\n    def forward(self, x):\r\n        x = torch.nn.functional.interpolate(x, scale_factor=2.0, mode=\"nearest\")\r\n        if self.with_conv:\r\n            x = self.conv(x)\r\n        return x\r\n\r\n\r\nclass Downsample(nn.Module):\r\n    def __init__(self, in_channels, with_conv):\r\n        super().__init__()\r\n        self.with_conv = with_conv\r\n        if self.with_conv:\r\n            # no asymmetric padding in torch conv, must do it ourselves\r\n            self.conv = torch.nn.Conv2d(\r\n                in_channels, in_channels, kernel_size=3, stride=2, padding=0\r\n            )\r\n\r\n    def forward(self, x):\r\n        if self.with_conv:\r\n            pad = (0, 1, 0, 1)\r\n            x = torch.nn.functional.pad(x, pad, mode=\"constant\", value=0)\r\n            x = self.conv(x)\r\n        else:\r\n            x = torch.nn.functional.avg_pool2d(x, kernel_size=2, stride=2)\r\n        return x\r\n\r\n\r\nclass ResnetBlock(nn.Module):\r\n    def __init__(\r\n        self,\r\n        *,\r\n        in_channels,\r\n        out_channels=None,\r\n        conv_shortcut=False,\r\n        dropout,\r\n        temb_channels=512,\r\n    ):\r\n        super().__init__()\r\n        self.in_channels = in_channels\r\n        out_channels = in_channels if out_channels is None else out_channels\r\n        self.out_channels = out_channels\r\n        self.use_conv_shortcut = conv_shortcut\r\n\r\n        self.norm1 = Normalize(in_channels)\r\n        self.conv1 = torch.nn.Conv2d(\r\n            in_channels, out_channels, kernel_size=3, stride=1, padding=1\r\n        )\r\n        if temb_channels > 0:\r\n            self.temb_proj = torch.nn.Linear(temb_channels, out_channels)\r\n        self.norm2 = Normalize(out_channels)\r\n        self.dropout = torch.nn.Dropout(dropout)\r\n        self.conv2 = torch.nn.Conv2d(\r\n            out_channels, out_channels, kernel_size=3, stride=1, padding=1\r\n        )\r\n        if self.in_channels != self.out_channels:\r\n            if self.use_conv_shortcut:\r\n                self.conv_shortcut = torch.nn.Conv2d(\r\n                    in_channels, out_channels, kernel_size=3, stride=1, padding=1\r\n                )\r\n            else:\r\n                self.nin_shortcut = torch.nn.Conv2d(\r\n                    in_channels, out_channels, kernel_size=1, stride=1, padding=0\r\n                )\r\n\r\n    def forward(self, x, temb):\r\n        h = x\r\n        h = self.norm1(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv1(h)\r\n\r\n        if temb is not None:\r\n            h = h + self.temb_proj(nonlinearity(temb))[:, :, None, None]\r\n\r\n        h = self.norm2(h)\r\n        h = nonlinearity(h)\r\n        h = self.dropout(h)\r\n        h = self.conv2(h)\r\n\r\n        if self.in_channels != self.out_channels:\r\n            if self.use_conv_shortcut:\r\n                x = self.conv_shortcut(x)\r\n            else:\r\n                x = self.nin_shortcut(x)\r\n\r\n        return x + h\r\n\r\n\r\nclass AttnBlock(nn.Module):\r\n    def __init__(self, in_channels):\r\n        super().__init__()\r\n        self.in_channels = in_channels\r\n\r\n        self.norm = Normalize(in_channels)\r\n        self.q = torch.nn.Conv2d(\r\n            in_channels, in_channels, kernel_size=1, stride=1, padding=0\r\n        )\r\n        self.k = torch.nn.Conv2d(\r\n            in_channels, in_channels, kernel_size=1, stride=1, padding=0\r\n        )\r\n        self.v = torch.nn.Conv2d(\r\n            in_channels, in_channels, kernel_size=1, stride=1, padding=0\r\n        )\r\n        self.proj_out = torch.nn.Conv2d(\r\n            in_channels, in_channels, kernel_size=1, stride=1, padding=0\r\n        )\r\n\r\n    def forward(self, x):\r\n        h_ = x\r\n        h_ = self.norm(h_)\r\n        q = self.q(h_)\r\n        k = self.k(h_)\r\n        v = self.v(h_)\r\n\r\n        # compute attention\r\n        b, c, h, w = q.shape\r\n        q = q.reshape(b, c, h * w)\r\n        q = q.permute(0, 2, 1)  # b,hw,c\r\n        k = k.reshape(b, c, h * w)  # b,c,hw\r\n        w_ = torch.bmm(q, k)  # b,hw,hw    w[b,i,j]=sum_c q[b,i,c]k[b,c,j]\r\n        w_ = w_ * (int(c) ** (-0.5))\r\n        w_ = torch.nn.functional.softmax(w_, dim=2)\r\n\r\n        # attend to values\r\n        v = v.reshape(b, c, h * w)\r\n        w_ = w_.permute(0, 2, 1)  # b,hw,hw (first hw of k, second of q)\r\n        h_ = torch.bmm(v, w_)  # b, c,hw (hw of q) h_[b,c,j] = sum_i v[b,c,i] w_[b,i,j]\r\n        h_ = h_.reshape(b, c, h, w)\r\n\r\n        h_ = self.proj_out(h_)\r\n\r\n        return x + h_\r\n\r\n\r\nclass Model(nn.Module):\r\n    def __init__(\r\n        self,\r\n        *,\r\n        ch,\r\n        out_ch,\r\n        ch_mult=(1, 2, 4, 8),\r\n        num_res_blocks,\r\n        attn_resolutions,\r\n        dropout=0.0,\r\n        resamp_with_conv=True,\r\n        in_channels,\r\n        resolution,\r\n        use_timestep=True,\r\n    ):\r\n        super().__init__()\r\n        self.ch = ch\r\n        self.temb_ch = self.ch * 4\r\n        self.num_resolutions = len(ch_mult)\r\n        self.num_res_blocks = num_res_blocks\r\n        self.resolution = resolution\r\n        self.in_channels = in_channels\r\n\r\n        self.use_timestep = use_timestep\r\n        if self.use_timestep:\r\n            # timestep embedding\r\n            self.temb = nn.Module()\r\n            self.temb.dense = nn.ModuleList(\r\n                [\r\n                    torch.nn.Linear(self.ch, self.temb_ch),\r\n                    torch.nn.Linear(self.temb_ch, self.temb_ch),\r\n                ]\r\n            )\r\n\r\n        # downsampling\r\n        self.conv_in = torch.nn.Conv2d(\r\n            in_channels, self.ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n        curr_res = resolution\r\n        in_ch_mult = (1,) + tuple(ch_mult)\r\n        self.down = nn.ModuleList()\r\n        for i_level in range(self.num_resolutions):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_in = ch * in_ch_mult[i_level]\r\n            block_out = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks):\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            down = nn.Module()\r\n            down.block = block\r\n            down.attn = attn\r\n            if i_level != self.num_resolutions - 1:\r\n                down.downsample = Downsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res // 2\r\n            self.down.append(down)\r\n\r\n        # middle\r\n        self.mid = nn.Module()\r\n        self.mid.block_1 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n        self.mid.attn_1 = AttnBlock(block_in)\r\n        self.mid.block_2 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n\r\n        # upsampling\r\n        self.up = nn.ModuleList()\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_out = ch * ch_mult[i_level]\r\n            skip_in = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                if i_block == self.num_res_blocks:\r\n                    skip_in = ch * in_ch_mult[i_level]\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in + skip_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            up = nn.Module()\r\n            up.block = block\r\n            up.attn = attn\r\n            if i_level != 0:\r\n                up.upsample = Upsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res * 2\r\n            self.up.insert(0, up)  # prepend to get consistent order\r\n\r\n        # end\r\n        self.norm_out = Normalize(block_in)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            block_in, out_ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n    def forward(self, x, t=None):\r\n        # assert x.shape[2] == x.shape[3] == self.resolution\r\n\r\n        if self.use_timestep:\r\n            # timestep embedding\r\n            assert t is not None\r\n            temb = get_timestep_embedding(t, self.ch)\r\n            temb = self.temb.dense[0](temb)\r\n            temb = nonlinearity(temb)\r\n            temb = self.temb.dense[1](temb)\r\n        else:\r\n            temb = None\r\n\r\n        # downsampling\r\n        hs = [self.conv_in(x)]\r\n        for i_level in range(self.num_resolutions):\r\n            for i_block in range(self.num_res_blocks):\r\n                h = self.down[i_level].block[i_block](hs[-1], temb)\r\n                if len(self.down[i_level].attn) > 0:\r\n                    h = self.down[i_level].attn[i_block](h)\r\n                hs.append(h)\r\n            if i_level != self.num_resolutions - 1:\r\n                hs.append(self.down[i_level].downsample(hs[-1]))\r\n\r\n        # middle\r\n        h = hs[-1]\r\n        h = self.mid.block_1(h, temb)\r\n        h = self.mid.attn_1(h)\r\n        h = self.mid.block_2(h, temb)\r\n\r\n        # upsampling\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                h = self.up[i_level].block[i_block](\r\n                    torch.cat([h, hs.pop()], dim=1), temb\r\n                )\r\n                if len(self.up[i_level].attn) > 0:\r\n                    h = self.up[i_level].attn[i_block](h)\r\n            if i_level != 0:\r\n                h = self.up[i_level].upsample(h)\r\n\r\n        # end\r\n        h = self.norm_out(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv_out(h)\r\n        return h\r\n\r\n\r\nclass Encoder(nn.Module):\r\n    def __init__(\r\n        self,\r\n        *,\r\n        ch,\r\n        out_ch,\r\n        ch_mult=(1, 2, 4, 8),\r\n        num_res_blocks,\r\n        attn_resolutions,\r\n        dropout=0.0,\r\n        resamp_with_conv=True,\r\n        in_channels,\r\n        resolution,\r\n        z_channels,\r\n        double_z=True,\r\n        **ignore_kwargs,\r\n    ):\r\n        super().__init__()\r\n        self.ch = ch\r\n        self.temb_ch = 0\r\n        self.num_resolutions = len(ch_mult)\r\n        self.num_res_blocks = num_res_blocks\r\n        self.resolution = resolution\r\n        self.in_channels = in_channels\r\n\r\n        # downsampling\r\n        self.conv_in = torch.nn.Conv2d(\r\n            in_channels, self.ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n        curr_res = resolution\r\n        in_ch_mult = (1,) + tuple(ch_mult)\r\n        self.down = nn.ModuleList()\r\n        for i_level in range(self.num_resolutions):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_in = ch * in_ch_mult[i_level]\r\n            block_out = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks):\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            down = nn.Module()\r\n            down.block = block\r\n            down.attn = attn\r\n            if i_level != self.num_resolutions - 1:\r\n                down.downsample = Downsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res // 2\r\n            self.down.append(down)\r\n\r\n        # middle\r\n        self.mid = nn.Module()\r\n        self.mid.block_1 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n        self.mid.attn_1 = AttnBlock(block_in)\r\n        self.mid.block_2 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n\r\n        # end\r\n        self.norm_out = Normalize(block_in)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            block_in,\r\n            2 * z_channels if double_z else z_channels,\r\n            kernel_size=3,\r\n            stride=1,\r\n            padding=1,\r\n        )\r\n\r\n    def forward(self, x):\r\n        # assert x.shape[2] == x.shape[3] == self.resolution, \"{}, {}, {}\".format(x.shape[2], x.shape[3], self.resolution)\r\n\r\n        # timestep embedding\r\n        temb = None\r\n\r\n        # downsampling\r\n        hs = [self.conv_in(x)]\r\n        for i_level in range(self.num_resolutions):\r\n            for i_block in range(self.num_res_blocks):\r\n                h = self.down[i_level].block[i_block](hs[-1], temb)\r\n                if len(self.down[i_level].attn) > 0:\r\n                    h = self.down[i_level].attn[i_block](h)\r\n                hs.append(h)\r\n            if i_level != self.num_resolutions - 1:\r\n                hs.append(self.down[i_level].downsample(hs[-1]))\r\n\r\n        # middle\r\n        h = hs[-1]\r\n        h = self.mid.block_1(h, temb)\r\n        h = self.mid.attn_1(h)\r\n        h = self.mid.block_2(h, temb)\r\n\r\n        # end\r\n        h = self.norm_out(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv_out(h)\r\n        return h\r\n\r\n\r\nclass Decoder(nn.Module):\r\n    def __init__(\r\n        self,\r\n        *,\r\n        ch,\r\n        out_ch,\r\n        ch_mult=(1, 2, 4, 8),\r\n        num_res_blocks,\r\n        attn_resolutions,\r\n        dropout=0.0,\r\n        resamp_with_conv=True,\r\n        in_channels,\r\n        resolution,\r\n        z_channels,\r\n        give_pre_end=False,\r\n        **ignorekwargs,\r\n    ):\r\n        super().__init__()\r\n        self.ch = ch\r\n        self.temb_ch = 0\r\n        self.num_resolutions = len(ch_mult)\r\n        self.num_res_blocks = num_res_blocks\r\n        self.resolution = resolution\r\n        self.in_channels = in_channels\r\n        self.give_pre_end = give_pre_end\r\n\r\n        # compute in_ch_mult, block_in and curr_res at lowest res\r\n        in_ch_mult = (1,) + tuple(ch_mult)\r\n        block_in = ch * ch_mult[self.num_resolutions - 1]\r\n        curr_res = resolution // 2 ** (self.num_resolutions - 1)\r\n        self.z_shape = (1, z_channels, curr_res, curr_res)\r\n        print(\r\n            \"Working with z of shape {} = {} dimensions.\".format(\r\n                self.z_shape, np.prod(self.z_shape)\r\n            )\r\n        )\r\n\r\n        # z to block_in\r\n        self.conv_in = torch.nn.Conv2d(\r\n            z_channels, block_in, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n        # middle\r\n        self.mid = nn.Module()\r\n        self.mid.block_1 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n        self.mid.attn_1 = AttnBlock(block_in)\r\n        self.mid.block_2 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n\r\n        # upsampling\r\n        self.up = nn.ModuleList()\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_out = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            up = nn.Module()\r\n            up.block = block\r\n            up.attn = attn\r\n            if i_level != 0:\r\n                up.upsample = Upsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res * 2\r\n            self.up.insert(0, up)  # prepend to get consistent order\r\n\r\n        # end\r\n        self.norm_out = Normalize(block_in)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            block_in, out_ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n    def forward(self, z):\r\n        # assert z.shape[1:] == self.z_shape[1:]\r\n        self.last_z_shape = z.shape\r\n\r\n        # timestep embedding\r\n        temb = None\r\n\r\n        # z to block_in\r\n        h = self.conv_in(z)\r\n\r\n        # middle\r\n        h = self.mid.block_1(h, temb)\r\n        h = self.mid.attn_1(h)\r\n        h = self.mid.block_2(h, temb)\r\n\r\n        # upsampling\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                h = self.up[i_level].block[i_block](h, temb)\r\n                if len(self.up[i_level].attn) > 0:\r\n                    h = self.up[i_level].attn[i_block](h)\r\n            if i_level != 0:\r\n                h = self.up[i_level].upsample(h)\r\n\r\n        # end\r\n        if self.give_pre_end:\r\n            return h\r\n\r\n        h = self.norm_out(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv_out(h)\r\n        return h\r\n\r\n\r\nclass VUNet(nn.Module):\r\n    def __init__(\r\n        self,\r\n        *,\r\n        ch,\r\n        out_ch,\r\n        ch_mult=(1, 2, 4, 8),\r\n        num_res_blocks,\r\n        attn_resolutions,\r\n        dropout=0.0,\r\n        resamp_with_conv=True,\r\n        in_channels,\r\n        c_channels,\r\n        resolution,\r\n        z_channels,\r\n        use_timestep=False,\r\n        **ignore_kwargs,\r\n    ):\r\n        super().__init__()\r\n        self.ch = ch\r\n        self.temb_ch = self.ch * 4\r\n        self.num_resolutions = len(ch_mult)\r\n        self.num_res_blocks = num_res_blocks\r\n        self.resolution = resolution\r\n\r\n        self.use_timestep = use_timestep\r\n        if self.use_timestep:\r\n            # timestep embedding\r\n            self.temb = nn.Module()\r\n            self.temb.dense = nn.ModuleList(\r\n                [\r\n                    torch.nn.Linear(self.ch, self.temb_ch),\r\n                    torch.nn.Linear(self.temb_ch, self.temb_ch),\r\n                ]\r\n            )\r\n\r\n        # downsampling\r\n        self.conv_in = torch.nn.Conv2d(\r\n            c_channels, self.ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n        curr_res = resolution\r\n        in_ch_mult = (1,) + tuple(ch_mult)\r\n        self.down = nn.ModuleList()\r\n        for i_level in range(self.num_resolutions):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_in = ch * in_ch_mult[i_level]\r\n            block_out = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks):\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            down = nn.Module()\r\n            down.block = block\r\n            down.attn = attn\r\n            if i_level != self.num_resolutions - 1:\r\n                down.downsample = Downsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res // 2\r\n            self.down.append(down)\r\n\r\n        self.z_in = torch.nn.Conv2d(\r\n            z_channels, block_in, kernel_size=1, stride=1, padding=0\r\n        )\r\n        # middle\r\n        self.mid = nn.Module()\r\n        self.mid.block_1 = ResnetBlock(\r\n            in_channels=2 * block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n        self.mid.attn_1 = AttnBlock(block_in)\r\n        self.mid.block_2 = ResnetBlock(\r\n            in_channels=block_in,\r\n            out_channels=block_in,\r\n            temb_channels=self.temb_ch,\r\n            dropout=dropout,\r\n        )\r\n\r\n        # upsampling\r\n        self.up = nn.ModuleList()\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            block = nn.ModuleList()\r\n            attn = nn.ModuleList()\r\n            block_out = ch * ch_mult[i_level]\r\n            skip_in = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                if i_block == self.num_res_blocks:\r\n                    skip_in = ch * in_ch_mult[i_level]\r\n                block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in + skip_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n                if curr_res in attn_resolutions:\r\n                    attn.append(AttnBlock(block_in))\r\n            up = nn.Module()\r\n            up.block = block\r\n            up.attn = attn\r\n            if i_level != 0:\r\n                up.upsample = Upsample(block_in, resamp_with_conv)\r\n                curr_res = curr_res * 2\r\n            self.up.insert(0, up)  # prepend to get consistent order\r\n\r\n        # end\r\n        self.norm_out = Normalize(block_in)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            block_in, out_ch, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n    def forward(self, x, z, t=None):\r\n        # assert x.shape[2] == x.shape[3] == self.resolution\r\n\r\n        if self.use_timestep:\r\n            # timestep embedding\r\n            assert t is not None\r\n            temb = get_timestep_embedding(t, self.ch)\r\n            temb = self.temb.dense[0](temb)\r\n            temb = nonlinearity(temb)\r\n            temb = self.temb.dense[1](temb)\r\n        else:\r\n            temb = None\r\n\r\n        # downsampling\r\n        hs = [self.conv_in(x)]\r\n        for i_level in range(self.num_resolutions):\r\n            for i_block in range(self.num_res_blocks):\r\n                h = self.down[i_level].block[i_block](hs[-1], temb)\r\n                if len(self.down[i_level].attn) > 0:\r\n                    h = self.down[i_level].attn[i_block](h)\r\n                hs.append(h)\r\n            if i_level != self.num_resolutions - 1:\r\n                hs.append(self.down[i_level].downsample(hs[-1]))\r\n\r\n        # middle\r\n        h = hs[-1]\r\n        z = self.z_in(z)\r\n        h = torch.cat((h, z), dim=1)\r\n        h = self.mid.block_1(h, temb)\r\n        h = self.mid.attn_1(h)\r\n        h = self.mid.block_2(h, temb)\r\n\r\n        # upsampling\r\n        for i_level in reversed(range(self.num_resolutions)):\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                h = self.up[i_level].block[i_block](\r\n                    torch.cat([h, hs.pop()], dim=1), temb\r\n                )\r\n                if len(self.up[i_level].attn) > 0:\r\n                    h = self.up[i_level].attn[i_block](h)\r\n            if i_level != 0:\r\n                h = self.up[i_level].upsample(h)\r\n\r\n        # end\r\n        h = self.norm_out(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv_out(h)\r\n        return h\r\n\r\n\r\nclass SimpleDecoder(nn.Module):\r\n    def __init__(self, in_channels, out_channels, *args, **kwargs):\r\n        super().__init__()\r\n        self.model = nn.ModuleList(\r\n            [\r\n                nn.Conv2d(in_channels, in_channels, 1),\r\n                ResnetBlock(\r\n                    in_channels=in_channels,\r\n                    out_channels=2 * in_channels,\r\n                    temb_channels=0,\r\n                    dropout=0.0,\r\n                ),\r\n                ResnetBlock(\r\n                    in_channels=2 * in_channels,\r\n                    out_channels=4 * in_channels,\r\n                    temb_channels=0,\r\n                    dropout=0.0,\r\n                ),\r\n                ResnetBlock(\r\n                    in_channels=4 * in_channels,\r\n                    out_channels=2 * in_channels,\r\n                    temb_channels=0,\r\n                    dropout=0.0,\r\n                ),\r\n                nn.Conv2d(2 * in_channels, in_channels, 1),\r\n                Upsample(in_channels, with_conv=True),\r\n            ]\r\n        )\r\n        # end\r\n        self.norm_out = Normalize(in_channels)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            in_channels, out_channels, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n    def forward(self, x):\r\n        for i, layer in enumerate(self.model):\r\n            if i in [1, 2, 3]:\r\n                x = layer(x, None)\r\n            else:\r\n                x = layer(x)\r\n\r\n        h = self.norm_out(x)\r\n        h = nonlinearity(h)\r\n        x = self.conv_out(h)\r\n        return x\r\n\r\n\r\nclass UpsampleDecoder(nn.Module):\r\n    def __init__(\r\n        self,\r\n        in_channels,\r\n        out_channels,\r\n        ch,\r\n        num_res_blocks,\r\n        resolution,\r\n        ch_mult=(2, 2),\r\n        dropout=0.0,\r\n    ):\r\n        super().__init__()\r\n        # upsampling\r\n        self.temb_ch = 0\r\n        self.num_resolutions = len(ch_mult)\r\n        self.num_res_blocks = num_res_blocks\r\n        block_in = in_channels\r\n        curr_res = resolution // 2 ** (self.num_resolutions - 1)\r\n        self.res_blocks = nn.ModuleList()\r\n        self.upsample_blocks = nn.ModuleList()\r\n        for i_level in range(self.num_resolutions):\r\n            res_block = []\r\n            block_out = ch * ch_mult[i_level]\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                res_block.append(\r\n                    ResnetBlock(\r\n                        in_channels=block_in,\r\n                        out_channels=block_out,\r\n                        temb_channels=self.temb_ch,\r\n                        dropout=dropout,\r\n                    )\r\n                )\r\n                block_in = block_out\r\n            self.res_blocks.append(nn.ModuleList(res_block))\r\n            if i_level != self.num_resolutions - 1:\r\n                self.upsample_blocks.append(Upsample(block_in, True))\r\n                curr_res = curr_res * 2\r\n\r\n        # end\r\n        self.norm_out = Normalize(block_in)\r\n        self.conv_out = torch.nn.Conv2d(\r\n            block_in, out_channels, kernel_size=3, stride=1, padding=1\r\n        )\r\n\r\n    def forward(self, x):\r\n        # upsampling\r\n        h = x\r\n        for k, i_level in enumerate(range(self.num_resolutions)):\r\n            for i_block in range(self.num_res_blocks + 1):\r\n                h = self.res_blocks[i_level][i_block](h, None)\r\n            if i_level != self.num_resolutions - 1:\r\n                h = self.upsample_blocks[k](h)\r\n        h = self.norm_out(h)\r\n        h = nonlinearity(h)\r\n        h = self.conv_out(h)\r\n        return h\r\n"}