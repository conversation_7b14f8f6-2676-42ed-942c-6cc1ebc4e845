{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\taming\\modules\\vqvae\\quantize.py"}, "originalCode": "import torch\r\nimport torch.nn as nn\r\nimport torch.nn.functional as F\r\nimport numpy as np\r\nfrom torch import einsum\r\nfrom einops import rearrange\r\n\r\n\r\nclass VectorQuantizer(nn.Module):\r\n    \"\"\"\r\n    see https://github.com/MishaLaskin/vqvae/blob/d761a999e2267766400dc646d82d3ac3657771d4/models/quantizer.py\r\n    ____________________________________________\r\n    Discretization bottleneck part of the VQ-VAE.\r\n    Inputs:\r\n    - n_e : number of embeddings\r\n    - e_dim : dimension of embedding\r\n    - beta : commitment cost used in loss term, beta * ||z_e(x)-sg[e]||^2\r\n    _____________________________________________\r\n    \"\"\"\r\n\r\n    # NOTE: this class contains a bug regarding beta; see VectorQuantizer2 for\r\n    # a fix and use legacy=False to apply that fix. VectorQuantizer2 can be\r\n    # used wherever VectorQuantizer has been used before and is additionally\r\n    # more efficient.\r\n    def __init__(self, n_e, e_dim, beta):\r\n        super(VectorQuantizer, self).__init__()\r\n        self.n_e = n_e\r\n        self.e_dim = e_dim\r\n        self.beta = beta\r\n\r\n        self.embedding = nn.Embedding(self.n_e, self.e_dim)\r\n        self.embedding.weight.data.uniform_(-1.0 / self.n_e, 1.0 / self.n_e)\r\n\r\n    def forward(self, z):\r\n        \"\"\"\r\n        Inputs the output of the encoder network z and maps it to a discrete\r\n        one-hot vector that is the index of the closest embedding vector e_j\r\n        z (continuous) -> z_q (discrete)\r\n        z.shape = (batch, channel, height, width)\r\n        quantization pipeline:\r\n            1. get encoder input (B,C,H,W)\r\n            2. flatten input to (B*H*W,C)\r\n        \"\"\"\r\n        # reshape z -> (batch, height, width, channel) and flatten\r\n        z = z.permute(0, 2, 3, 1).contiguous()\r\n        z_flattened = z.view(-1, self.e_dim)\r\n        # distances from z to embeddings e_j (z - e)^2 = z^2 + e^2 - 2 e * z\r\n\r\n        d = torch.sum(z_flattened ** 2, dim=1, keepdim=True) + \\\r\n            torch.sum(self.embedding.weight**2, dim=1) - 2 * \\\r\n            torch.matmul(z_flattened, self.embedding.weight.t())\r\n\r\n        ## could possible replace this here\r\n        # #\\start...\r\n        # find closest encodings\r\n        min_encoding_indices = torch.argmin(d, dim=1).unsqueeze(1)\r\n\r\n        min_encodings = torch.zeros(\r\n            min_encoding_indices.shape[0], self.n_e).to(z)\r\n        min_encodings.scatter_(1, min_encoding_indices, 1)\r\n\r\n        # dtype min encodings: torch.float32\r\n        # min_encodings shape: torch.Size([2048, 512])\r\n        # min_encoding_indices.shape: torch.Size([2048, 1])\r\n\r\n        # get quantized latent vectors\r\n        z_q = torch.matmul(min_encodings, self.embedding.weight).view(z.shape)\r\n        #.........\\end\r\n\r\n        # with:\r\n        # .........\\start\r\n        #min_encoding_indices = torch.argmin(d, dim=1)\r\n        #z_q = self.embedding(min_encoding_indices)\r\n        # ......\\end......... (TODO)\r\n\r\n        # compute loss for embedding\r\n        loss = torch.mean((z_q.detach()-z)**2) + self.beta * \\\r\n            torch.mean((z_q - z.detach()) ** 2)\r\n\r\n        # preserve gradients\r\n        z_q = z + (z_q - z).detach()\r\n\r\n        # perplexity\r\n        e_mean = torch.mean(min_encodings, dim=0)\r\n        perplexity = torch.exp(-torch.sum(e_mean * torch.log(e_mean + 1e-10)))\r\n\r\n        # reshape back to match original input shape\r\n        z_q = z_q.permute(0, 3, 1, 2).contiguous()\r\n\r\n        return z_q, loss, (perplexity, min_encodings, min_encoding_indices)\r\n\r\n    def get_codebook_entry(self, indices, shape):\r\n        # shape specifying (batch, height, width, channel)\r\n        # TODO: check for more easy handling with nn.Embedding\r\n        min_encodings = torch.zeros(indices.shape[0], self.n_e).to(indices)\r\n        min_encodings.scatter_(1, indices[:,None], 1)\r\n\r\n        # get quantized latent vectors\r\n        z_q = torch.matmul(min_encodings.float(), self.embedding.weight)\r\n\r\n        if shape is not None:\r\n            z_q = z_q.view(shape)\r\n\r\n            # reshape back to match original input shape\r\n            z_q = z_q.permute(0, 3, 1, 2).contiguous()\r\n\r\n        return z_q\r\n\r\n\r\nclass GumbelQuantize(nn.Module):\r\n    \"\"\"\r\n    credit to @karpathy: https://github.com/karpathy/deep-vector-quantization/blob/main/model.py (thanks!)\r\n    Gumbel Softmax trick quantizer\r\n    Categorical Reparameterization with Gumbel-Softmax, Jang et al. 2016\r\n    https://arxiv.org/abs/1611.01144\r\n    \"\"\"\r\n    def __init__(self, num_hiddens, embedding_dim, n_embed, straight_through=True,\r\n                 kl_weight=5e-4, temp_init=1.0, use_vqinterface=True,\r\n                 remap=None, unknown_index=\"random\"):\r\n        super().__init__()\r\n\r\n        self.embedding_dim = embedding_dim\r\n        self.n_embed = n_embed\r\n\r\n        self.straight_through = straight_through\r\n        self.temperature = temp_init\r\n        self.kl_weight = kl_weight\r\n\r\n        self.proj = nn.Conv2d(num_hiddens, n_embed, 1)\r\n        self.embed = nn.Embedding(n_embed, embedding_dim)\r\n\r\n        self.use_vqinterface = use_vqinterface\r\n\r\n        self.remap = remap\r\n        if self.remap is not None:\r\n            self.register_buffer(\"used\", torch.tensor(np.load(self.remap)))\r\n            self.re_embed = self.used.shape[0]\r\n            self.unknown_index = unknown_index # \"random\" or \"extra\" or integer\r\n            if self.unknown_index == \"extra\":\r\n                self.unknown_index = self.re_embed\r\n                self.re_embed = self.re_embed+1\r\n            print(f\"Remapping {self.n_embed} indices to {self.re_embed} indices. \"\r\n                  f\"Using {self.unknown_index} for unknown indices.\")\r\n        else:\r\n            self.re_embed = n_embed\r\n\r\n    def remap_to_used(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape)>1\r\n        inds = inds.reshape(ishape[0],-1)\r\n        used = self.used.to(inds)\r\n        match = (inds[:,:,None]==used[None,None,...]).long()\r\n        new = match.argmax(-1)\r\n        unknown = match.sum(2)<1\r\n        if self.unknown_index == \"random\":\r\n            new[unknown]=torch.randint(0,self.re_embed,size=new[unknown].shape).to(device=new.device)\r\n        else:\r\n            new[unknown] = self.unknown_index\r\n        return new.reshape(ishape)\r\n\r\n    def unmap_to_all(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape)>1\r\n        inds = inds.reshape(ishape[0],-1)\r\n        used = self.used.to(inds)\r\n        if self.re_embed > self.used.shape[0]: # extra token\r\n            inds[inds>=self.used.shape[0]] = 0 # simply set to zero\r\n        back=torch.gather(used[None,:][inds.shape[0]*[0],:], 1, inds)\r\n        return back.reshape(ishape)\r\n\r\n    def forward(self, z, temp=None, return_logits=False):\r\n        # force hard = True when we are in eval mode, as we must quantize. actually, always true seems to work\r\n        hard = self.straight_through if self.training else True\r\n        temp = self.temperature if temp is None else temp\r\n\r\n        logits = self.proj(z)\r\n        if self.remap is not None:\r\n            # continue only with used logits\r\n            full_zeros = torch.zeros_like(logits)\r\n            logits = logits[:,self.used,...]\r\n\r\n        soft_one_hot = F.gumbel_softmax(logits, tau=temp, dim=1, hard=hard)\r\n        if self.remap is not None:\r\n            # go back to all entries but unused set to zero\r\n            full_zeros[:,self.used,...] = soft_one_hot\r\n            soft_one_hot = full_zeros\r\n        z_q = einsum('b n h w, n d -> b d h w', soft_one_hot, self.embed.weight)\r\n\r\n        # + kl divergence to the prior loss\r\n        qy = F.softmax(logits, dim=1)\r\n        diff = self.kl_weight * torch.sum(qy * torch.log(qy * self.n_embed + 1e-10), dim=1).mean()\r\n\r\n        ind = soft_one_hot.argmax(dim=1)\r\n        if self.remap is not None:\r\n            ind = self.remap_to_used(ind)\r\n        if self.use_vqinterface:\r\n            if return_logits:\r\n                return z_q, diff, (None, None, ind), logits\r\n            return z_q, diff, (None, None, ind)\r\n        return z_q, diff, ind\r\n\r\n    def get_codebook_entry(self, indices, shape):\r\n        b, h, w, c = shape\r\n        assert b*h*w == indices.shape[0]\r\n        indices = rearrange(indices, '(b h w) -> b h w', b=b, h=h, w=w)\r\n        if self.remap is not None:\r\n            indices = self.unmap_to_all(indices)\r\n        one_hot = F.one_hot(indices, num_classes=self.n_embed).permute(0, 3, 1, 2).float()\r\n        z_q = einsum('b n h w, n d -> b d h w', one_hot, self.embed.weight)\r\n        return z_q\r\n\r\n\r\nclass VectorQuantizer2(nn.Module):\r\n    \"\"\"\r\n    Improved version over VectorQuantizer, can be used as a drop-in replacement. Mostly\r\n    avoids costly matrix multiplications and allows for post-hoc remapping of indices.\r\n    \"\"\"\r\n    # NOTE: due to a bug the beta term was applied to the wrong term. for\r\n    # backwards compatibility we use the buggy version by default, but you can\r\n    # specify legacy=False to fix it.\r\n    def __init__(self, n_e, e_dim, beta, remap=None, unknown_index=\"random\",\r\n                 sane_index_shape=False, legacy=True):\r\n        super().__init__()\r\n        self.n_e = n_e\r\n        self.e_dim = e_dim\r\n        self.beta = beta\r\n        self.legacy = legacy\r\n\r\n        self.embedding = nn.Embedding(self.n_e, self.e_dim)\r\n        self.embedding.weight.data.uniform_(-1.0 / self.n_e, 1.0 / self.n_e)\r\n\r\n        self.remap = remap\r\n        if self.remap is not None:\r\n            self.register_buffer(\"used\", torch.tensor(np.load(self.remap)))\r\n            self.re_embed = self.used.shape[0]\r\n            self.unknown_index = unknown_index # \"random\" or \"extra\" or integer\r\n            if self.unknown_index == \"extra\":\r\n                self.unknown_index = self.re_embed\r\n                self.re_embed = self.re_embed+1\r\n            print(f\"Remapping {self.n_e} indices to {self.re_embed} indices. \"\r\n                  f\"Using {self.unknown_index} for unknown indices.\")\r\n        else:\r\n            self.re_embed = n_e\r\n\r\n        self.sane_index_shape = sane_index_shape\r\n\r\n    def remap_to_used(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape)>1\r\n        inds = inds.reshape(ishape[0],-1)\r\n        used = self.used.to(inds)\r\n        match = (inds[:,:,None]==used[None,None,...]).long()\r\n        new = match.argmax(-1)\r\n        unknown = match.sum(2)<1\r\n        if self.unknown_index == \"random\":\r\n            new[unknown]=torch.randint(0,self.re_embed,size=new[unknown].shape).to(device=new.device)\r\n        else:\r\n            new[unknown] = self.unknown_index\r\n        return new.reshape(ishape)\r\n\r\n    def unmap_to_all(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape)>1\r\n        inds = inds.reshape(ishape[0],-1)\r\n        used = self.used.to(inds)\r\n        if self.re_embed > self.used.shape[0]: # extra token\r\n            inds[inds>=self.used.shape[0]] = 0 # simply set to zero\r\n        back=torch.gather(used[None,:][inds.shape[0]*[0],:], 1, inds)\r\n        return back.reshape(ishape)\r\n\r\n    def forward(self, z, temp=None, rescale_logits=False, return_logits=False):\r\n        assert temp is None or temp==1.0, \"Only for interface compatible with Gumbel\"\r\n        assert rescale_logits==False, \"Only for interface compatible with Gumbel\"\r\n        assert return_logits==False, \"Only for interface compatible with Gumbel\"\r\n        # reshape z -> (batch, height, width, channel) and flatten\r\n        z = rearrange(z, 'b c h w -> b h w c').contiguous()\r\n        z_flattened = z.view(-1, self.e_dim)\r\n        # distances from z to embeddings e_j (z - e)^2 = z^2 + e^2 - 2 e * z\r\n\r\n        d = torch.sum(z_flattened ** 2, dim=1, keepdim=True) + \\\r\n            torch.sum(self.embedding.weight**2, dim=1) - 2 * \\\r\n            torch.einsum('bd,dn->bn', z_flattened, rearrange(self.embedding.weight, 'n d -> d n'))\r\n\r\n        min_encoding_indices = torch.argmin(d, dim=1)\r\n        z_q = self.embedding(min_encoding_indices).view(z.shape)\r\n        perplexity = None\r\n        min_encodings = None\r\n\r\n        # compute loss for embedding\r\n        if not self.legacy:\r\n            loss = self.beta * torch.mean((z_q.detach()-z)**2) + \\\r\n                   torch.mean((z_q - z.detach()) ** 2)\r\n        else:\r\n            loss = torch.mean((z_q.detach()-z)**2) + self.beta * \\\r\n                   torch.mean((z_q - z.detach()) ** 2)\r\n\r\n        # preserve gradients\r\n        z_q = z + (z_q - z).detach()\r\n\r\n        # reshape back to match original input shape\r\n        z_q = rearrange(z_q, 'b h w c -> b c h w').contiguous()\r\n\r\n        if self.remap is not None:\r\n            min_encoding_indices = min_encoding_indices.reshape(z.shape[0],-1) # add batch axis\r\n            min_encoding_indices = self.remap_to_used(min_encoding_indices)\r\n            min_encoding_indices = min_encoding_indices.reshape(-1,1) # flatten\r\n\r\n        if self.sane_index_shape:\r\n            min_encoding_indices = min_encoding_indices.reshape(\r\n                z_q.shape[0], z_q.shape[2], z_q.shape[3])\r\n\r\n        return z_q, loss, (perplexity, min_encodings, min_encoding_indices)\r\n\r\n    def get_codebook_entry(self, indices, shape):\r\n        # shape specifying (batch, height, width, channel)\r\n        if self.remap is not None:\r\n            indices = indices.reshape(shape[0],-1) # add batch axis\r\n            indices = self.unmap_to_all(indices)\r\n            indices = indices.reshape(-1) # flatten again\r\n\r\n        # get quantized latent vectors\r\n        z_q = self.embedding(indices)\r\n\r\n        if shape is not None:\r\n            z_q = z_q.view(shape)\r\n            # reshape back to match original input shape\r\n            z_q = z_q.permute(0, 3, 1, 2).contiguous()\r\n\r\n        return z_q\r\n\r\nclass EmbeddingEMA(nn.Module):\r\n    def __init__(self, num_tokens, codebook_dim, decay=0.99, eps=1e-5):\r\n        super().__init__()\r\n        self.decay = decay\r\n        self.eps = eps\r\n        weight = torch.randn(num_tokens, codebook_dim)\r\n        self.weight = nn.Parameter(weight, requires_grad = False)\r\n        self.cluster_size = nn.Parameter(torch.zeros(num_tokens), requires_grad = False)\r\n        self.embed_avg = nn.Parameter(weight.clone(), requires_grad = False)\r\n        self.update = True\r\n\r\n    def forward(self, embed_id):\r\n        return F.embedding(embed_id, self.weight)\r\n\r\n    def cluster_size_ema_update(self, new_cluster_size):\r\n        self.cluster_size.data.mul_(self.decay).add_(new_cluster_size, alpha=1 - self.decay)\r\n\r\n    def embed_avg_ema_update(self, new_embed_avg):\r\n        self.embed_avg.data.mul_(self.decay).add_(new_embed_avg, alpha=1 - self.decay)\r\n\r\n    def weight_update(self, num_tokens):\r\n        n = self.cluster_size.sum()\r\n        smoothed_cluster_size = (\r\n                (self.cluster_size + self.eps) / (n + num_tokens * self.eps) * n\r\n            )\r\n        #normalize embedding average with smoothed cluster size\r\n        embed_normalized = self.embed_avg / smoothed_cluster_size.unsqueeze(1)\r\n        self.weight.data.copy_(embed_normalized)\r\n\r\n\r\nclass EMAVectorQuantizer(nn.Module):\r\n    def __init__(self, n_embed, embedding_dim, beta, decay=0.99, eps=1e-5,\r\n                remap=None, unknown_index=\"random\"):\r\n        super().__init__()\r\n        self.codebook_dim = codebook_dim\r\n        self.num_tokens = num_tokens\r\n        self.beta = beta\r\n        self.embedding = EmbeddingEMA(self.num_tokens, self.codebook_dim, decay, eps)\r\n\r\n        self.remap = remap\r\n        if self.remap is not None:\r\n            self.register_buffer(\"used\", torch.tensor(np.load(self.remap)))\r\n            self.re_embed = self.used.shape[0]\r\n            self.unknown_index = unknown_index # \"random\" or \"extra\" or integer\r\n            if self.unknown_index == \"extra\":\r\n                self.unknown_index = self.re_embed\r\n                self.re_embed = self.re_embed+1\r\n            print(f\"Remapping {self.n_embed} indices to {self.re_embed} indices. \"\r\n                  f\"Using {self.unknown_index} for unknown indices.\")\r\n        else:\r\n            self.re_embed = n_embed\r\n\r\n    def remap_to_used(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape)>1\r\n        inds = inds.reshape(ishape[0],-1)\r\n        used = self.used.to(inds)\r\n        match = (inds[:,:,None]==used[None,None,...]).long()\r\n        new = match.argmax(-1)\r\n        unknown = match.sum(2)<1\r\n        if self.unknown_index == \"random\":\r\n            new[unknown]=torch.randint(0,self.re_embed,size=new[unknown].shape).to(device=new.device)\r\n        else:\r\n            new[unknown] = self.unknown_index\r\n        return new.reshape(ishape)\r\n\r\n    def unmap_to_all(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape)>1\r\n        inds = inds.reshape(ishape[0],-1)\r\n        used = self.used.to(inds)\r\n        if self.re_embed > self.used.shape[0]: # extra token\r\n            inds[inds>=self.used.shape[0]] = 0 # simply set to zero\r\n        back=torch.gather(used[None,:][inds.shape[0]*[0],:], 1, inds)\r\n        return back.reshape(ishape)\r\n\r\n    def forward(self, z):\r\n        # reshape z -> (batch, height, width, channel) and flatten\r\n        #z, 'b c h w -> b h w c'\r\n        z = rearrange(z, 'b c h w -> b h w c')\r\n        z_flattened = z.reshape(-1, self.codebook_dim)\r\n\r\n        # distances from z to embeddings e_j (z - e)^2 = z^2 + e^2 - 2 e * z\r\n        d = z_flattened.pow(2).sum(dim=1, keepdim=True) + \\\r\n            self.embedding.weight.pow(2).sum(dim=1) - 2 * \\\r\n            torch.einsum('bd,nd->bn', z_flattened, self.embedding.weight) # 'n d -> d n'\r\n\r\n\r\n        encoding_indices = torch.argmin(d, dim=1)\r\n\r\n        z_q = self.embedding(encoding_indices).view(z.shape)\r\n        encodings = F.one_hot(encoding_indices, self.num_tokens).type(z.dtype)\r\n        avg_probs = torch.mean(encodings, dim=0)\r\n        perplexity = torch.exp(-torch.sum(avg_probs * torch.log(avg_probs + 1e-10)))\r\n\r\n        if self.training and self.embedding.update:\r\n            #EMA cluster size\r\n            encodings_sum = encodings.sum(0)\r\n            self.embedding.cluster_size_ema_update(encodings_sum)\r\n            #EMA embedding average\r\n            embed_sum = encodings.transpose(0,1) @ z_flattened\r\n            self.embedding.embed_avg_ema_update(embed_sum)\r\n            #normalize embed_avg and update weight\r\n            self.embedding.weight_update(self.num_tokens)\r\n\r\n        # compute loss for embedding\r\n        loss = self.beta * F.mse_loss(z_q.detach(), z)\r\n\r\n        # preserve gradients\r\n        z_q = z + (z_q - z).detach()\r\n\r\n        # reshape back to match original input shape\r\n        #z_q, 'b h w c -> b c h w'\r\n        z_q = rearrange(z_q, 'b h w c -> b c h w')\r\n        return z_q, loss, (perplexity, encodings, encoding_indices)\r\n", "modifiedCode": "import numpy as np\r\nimport torch\r\nimport torch.nn as nn\r\nimport torch.nn.functional as F\r\nfrom einops import rearrange\r\nfrom torch import einsum\r\n\r\n\r\nclass VectorQuantizer(nn.Module):\r\n    \"\"\"\r\n    see https://github.com/MishaLaskin/vqvae/blob/d761a999e2267766400dc646d82d3ac3657771d4/models/quantizer.py\r\n    ____________________________________________\r\n    Discretization bottleneck part of the VQ-VAE.\r\n    Inputs:\r\n    - n_e : number of embeddings\r\n    - e_dim : dimension of embedding\r\n    - beta : commitment cost used in loss term, beta * ||z_e(x)-sg[e]||^2\r\n    _____________________________________________\r\n    \"\"\"\r\n\r\n    # NOTE: this class contains a bug regarding beta; see VectorQuantizer2 for\r\n    # a fix and use legacy=False to apply that fix. VectorQuantizer2 can be\r\n    # used wherever VectorQuantizer has been used before and is additionally\r\n    # more efficient.\r\n    def __init__(self, n_e, e_dim, beta):\r\n        super(VectorQuantizer, self).__init__()\r\n        self.n_e = n_e\r\n        self.e_dim = e_dim\r\n        self.beta = beta\r\n\r\n        self.embedding = nn.Embedding(self.n_e, self.e_dim)\r\n        self.embedding.weight.data.uniform_(-1.0 / self.n_e, 1.0 / self.n_e)\r\n\r\n    def forward(self, z):\r\n        \"\"\"\r\n        Inputs the output of the encoder network z and maps it to a discrete\r\n        one-hot vector that is the index of the closest embedding vector e_j\r\n        z (continuous) -> z_q (discrete)\r\n        z.shape = (batch, channel, height, width)\r\n        quantization pipeline:\r\n            1. get encoder input (B,C,H,W)\r\n            2. flatten input to (B*H*W,C)\r\n        \"\"\"\r\n        # reshape z -> (batch, height, width, channel) and flatten\r\n        z = z.permute(0, 2, 3, 1).contiguous()\r\n        z_flattened = z.view(-1, self.e_dim)\r\n        # distances from z to embeddings e_j (z - e)^2 = z^2 + e^2 - 2 e * z\r\n\r\n        d = (\r\n            torch.sum(z_flattened**2, dim=1, keepdim=True)\r\n            + torch.sum(self.embedding.weight**2, dim=1)\r\n            - 2 * torch.matmul(z_flattened, self.embedding.weight.t())\r\n        )\r\n\r\n        ## could possible replace this here\r\n        # #\\start...\r\n        # find closest encodings\r\n        min_encoding_indices = torch.argmin(d, dim=1).unsqueeze(1)\r\n\r\n        min_encodings = torch.zeros(min_encoding_indices.shape[0], self.n_e).to(z)\r\n        min_encodings.scatter_(1, min_encoding_indices, 1)\r\n\r\n        # dtype min encodings: torch.float32\r\n        # min_encodings shape: torch.Size([2048, 512])\r\n        # min_encoding_indices.shape: torch.Size([2048, 1])\r\n\r\n        # get quantized latent vectors\r\n        z_q = torch.matmul(min_encodings, self.embedding.weight).view(z.shape)\r\n        # .........\\end\r\n\r\n        # with:\r\n        # .........\\start\r\n        # min_encoding_indices = torch.argmin(d, dim=1)\r\n        # z_q = self.embedding(min_encoding_indices)\r\n        # ......\\end......... (TODO)\r\n\r\n        # compute loss for embedding\r\n        loss = torch.mean((z_q.detach() - z) ** 2) + self.beta * torch.mean(\r\n            (z_q - z.detach()) ** 2\r\n        )\r\n\r\n        # preserve gradients\r\n        z_q = z + (z_q - z).detach()\r\n\r\n        # perplexity\r\n        e_mean = torch.mean(min_encodings, dim=0)\r\n        perplexity = torch.exp(-torch.sum(e_mean * torch.log(e_mean + 1e-10)))\r\n\r\n        # reshape back to match original input shape\r\n        z_q = z_q.permute(0, 3, 1, 2).contiguous()\r\n\r\n        return z_q, loss, (perplexity, min_encodings, min_encoding_indices)\r\n\r\n    def get_codebook_entry(self, indices, shape):\r\n        # shape specifying (batch, height, width, channel)\r\n        # TODO: check for more easy handling with nn.Embedding\r\n        min_encodings = torch.zeros(indices.shape[0], self.n_e).to(indices)\r\n        min_encodings.scatter_(1, indices[:, None], 1)\r\n\r\n        # get quantized latent vectors\r\n        z_q = torch.matmul(min_encodings.float(), self.embedding.weight)\r\n\r\n        if shape is not None:\r\n            z_q = z_q.view(shape)\r\n\r\n            # reshape back to match original input shape\r\n            z_q = z_q.permute(0, 3, 1, 2).contiguous()\r\n\r\n        return z_q\r\n\r\n\r\nclass GumbelQuantize(nn.Module):\r\n    \"\"\"\r\n    credit to @karpathy: https://github.com/karpathy/deep-vector-quantization/blob/main/model.py (thanks!)\r\n    Gumbel Softmax trick quantizer\r\n    Categorical Reparameterization with Gumbel-Softmax, Jang et al. 2016\r\n    https://arxiv.org/abs/1611.01144\r\n    \"\"\"\r\n\r\n    def __init__(\r\n        self,\r\n        num_hiddens,\r\n        embedding_dim,\r\n        n_embed,\r\n        straight_through=True,\r\n        kl_weight=5e-4,\r\n        temp_init=1.0,\r\n        use_vqinterface=True,\r\n        remap=None,\r\n        unknown_index=\"random\",\r\n    ):\r\n        super().__init__()\r\n\r\n        self.embedding_dim = embedding_dim\r\n        self.n_embed = n_embed\r\n\r\n        self.straight_through = straight_through\r\n        self.temperature = temp_init\r\n        self.kl_weight = kl_weight\r\n\r\n        self.proj = nn.Conv2d(num_hiddens, n_embed, 1)\r\n        self.embed = nn.Embedding(n_embed, embedding_dim)\r\n\r\n        self.use_vqinterface = use_vqinterface\r\n\r\n        self.remap = remap\r\n        if self.remap is not None:\r\n            self.register_buffer(\"used\", torch.tensor(np.load(self.remap)))\r\n            self.re_embed = self.used.shape[0]\r\n            self.unknown_index = unknown_index  # \"random\" or \"extra\" or integer\r\n            if self.unknown_index == \"extra\":\r\n                self.unknown_index = self.re_embed\r\n                self.re_embed = self.re_embed + 1\r\n            print(\r\n                f\"Remapping {self.n_embed} indices to {self.re_embed} indices. \"\r\n                f\"Using {self.unknown_index} for unknown indices.\"\r\n            )\r\n        else:\r\n            self.re_embed = n_embed\r\n\r\n    def remap_to_used(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape) > 1\r\n        inds = inds.reshape(ishape[0], -1)\r\n        used = self.used.to(inds)\r\n        match = (inds[:, :, None] == used[None, None, ...]).long()\r\n        new = match.argmax(-1)\r\n        unknown = match.sum(2) < 1\r\n        if self.unknown_index == \"random\":\r\n            new[unknown] = torch.randint(0, self.re_embed, size=new[unknown].shape).to(\r\n                device=new.device\r\n            )\r\n        else:\r\n            new[unknown] = self.unknown_index\r\n        return new.reshape(ishape)\r\n\r\n    def unmap_to_all(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape) > 1\r\n        inds = inds.reshape(ishape[0], -1)\r\n        used = self.used.to(inds)\r\n        if self.re_embed > self.used.shape[0]:  # extra token\r\n            inds[inds >= self.used.shape[0]] = 0  # simply set to zero\r\n        back = torch.gather(used[None, :][inds.shape[0] * [0], :], 1, inds)\r\n        return back.reshape(ishape)\r\n\r\n    def forward(self, z, temp=None, return_logits=False):\r\n        # force hard = True when we are in eval mode, as we must quantize. actually, always true seems to work\r\n        hard = self.straight_through if self.training else True\r\n        temp = self.temperature if temp is None else temp\r\n\r\n        logits = self.proj(z)\r\n        if self.remap is not None:\r\n            # continue only with used logits\r\n            full_zeros = torch.zeros_like(logits)\r\n            logits = logits[:, self.used, ...]\r\n\r\n        soft_one_hot = F.gumbel_softmax(logits, tau=temp, dim=1, hard=hard)\r\n        if self.remap is not None:\r\n            # go back to all entries but unused set to zero\r\n            full_zeros[:, self.used, ...] = soft_one_hot\r\n            soft_one_hot = full_zeros\r\n        z_q = einsum(\"b n h w, n d -> b d h w\", soft_one_hot, self.embed.weight)\r\n\r\n        # + kl divergence to the prior loss\r\n        qy = F.softmax(logits, dim=1)\r\n        diff = (\r\n            self.kl_weight\r\n            * torch.sum(qy * torch.log(qy * self.n_embed + 1e-10), dim=1).mean()\r\n        )\r\n\r\n        ind = soft_one_hot.argmax(dim=1)\r\n        if self.remap is not None:\r\n            ind = self.remap_to_used(ind)\r\n        if self.use_vqinterface:\r\n            if return_logits:\r\n                return z_q, diff, (None, None, ind), logits\r\n            return z_q, diff, (None, None, ind)\r\n        return z_q, diff, ind\r\n\r\n    def get_codebook_entry(self, indices, shape):\r\n        b, h, w, c = shape\r\n        assert b * h * w == indices.shape[0]\r\n        indices = rearrange(indices, \"(b h w) -> b h w\", b=b, h=h, w=w)\r\n        if self.remap is not None:\r\n            indices = self.unmap_to_all(indices)\r\n        one_hot = (\r\n            F.one_hot(indices, num_classes=self.n_embed).permute(0, 3, 1, 2).float()\r\n        )\r\n        z_q = einsum(\"b n h w, n d -> b d h w\", one_hot, self.embed.weight)\r\n        return z_q\r\n\r\n\r\nclass VectorQuantizer2(nn.Module):\r\n    \"\"\"\r\n    Improved version over VectorQuantizer, can be used as a drop-in replacement. Mostly\r\n    avoids costly matrix multiplications and allows for post-hoc remapping of indices.\r\n    \"\"\"\r\n\r\n    # NOTE: due to a bug the beta term was applied to the wrong term. for\r\n    # backwards compatibility we use the buggy version by default, but you can\r\n    # specify legacy=False to fix it.\r\n    def __init__(\r\n        self,\r\n        n_e,\r\n        e_dim,\r\n        beta,\r\n        remap=None,\r\n        unknown_index=\"random\",\r\n        sane_index_shape=False,\r\n        legacy=True,\r\n    ):\r\n        super().__init__()\r\n        self.n_e = n_e\r\n        self.e_dim = e_dim\r\n        self.beta = beta\r\n        self.legacy = legacy\r\n\r\n        self.embedding = nn.Embedding(self.n_e, self.e_dim)\r\n        self.embedding.weight.data.uniform_(-1.0 / self.n_e, 1.0 / self.n_e)\r\n\r\n        self.remap = remap\r\n        if self.remap is not None:\r\n            self.register_buffer(\"used\", torch.tensor(np.load(self.remap)))\r\n            self.re_embed = self.used.shape[0]\r\n            self.unknown_index = unknown_index  # \"random\" or \"extra\" or integer\r\n            if self.unknown_index == \"extra\":\r\n                self.unknown_index = self.re_embed\r\n                self.re_embed = self.re_embed + 1\r\n            print(\r\n                f\"Remapping {self.n_e} indices to {self.re_embed} indices. \"\r\n                f\"Using {self.unknown_index} for unknown indices.\"\r\n            )\r\n        else:\r\n            self.re_embed = n_e\r\n\r\n        self.sane_index_shape = sane_index_shape\r\n\r\n    def remap_to_used(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape) > 1\r\n        inds = inds.reshape(ishape[0], -1)\r\n        used = self.used.to(inds)\r\n        match = (inds[:, :, None] == used[None, None, ...]).long()\r\n        new = match.argmax(-1)\r\n        unknown = match.sum(2) < 1\r\n        if self.unknown_index == \"random\":\r\n            new[unknown] = torch.randint(0, self.re_embed, size=new[unknown].shape).to(\r\n                device=new.device\r\n            )\r\n        else:\r\n            new[unknown] = self.unknown_index\r\n        return new.reshape(ishape)\r\n\r\n    def unmap_to_all(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape) > 1\r\n        inds = inds.reshape(ishape[0], -1)\r\n        used = self.used.to(inds)\r\n        if self.re_embed > self.used.shape[0]:  # extra token\r\n            inds[inds >= self.used.shape[0]] = 0  # simply set to zero\r\n        back = torch.gather(used[None, :][inds.shape[0] * [0], :], 1, inds)\r\n        return back.reshape(ishape)\r\n\r\n    def forward(self, z, temp=None, rescale_logits=False, return_logits=False):\r\n        assert temp is None or temp == 1.0, \"Only for interface compatible with Gumbel\"\r\n        assert rescale_logits == False, \"Only for interface compatible with Gumbel\"\r\n        assert return_logits == False, \"Only for interface compatible with Gumbel\"\r\n        # reshape z -> (batch, height, width, channel) and flatten\r\n        z = rearrange(z, \"b c h w -> b h w c\").contiguous()\r\n        z_flattened = z.view(-1, self.e_dim)\r\n        # distances from z to embeddings e_j (z - e)^2 = z^2 + e^2 - 2 e * z\r\n\r\n        d = (\r\n            torch.sum(z_flattened**2, dim=1, keepdim=True)\r\n            + torch.sum(self.embedding.weight**2, dim=1)\r\n            - 2\r\n            * torch.einsum(\r\n                \"bd,dn->bn\", z_flattened, rearrange(self.embedding.weight, \"n d -> d n\")\r\n            )\r\n        )\r\n\r\n        min_encoding_indices = torch.argmin(d, dim=1)\r\n        z_q = self.embedding(min_encoding_indices).view(z.shape)\r\n        perplexity = None\r\n        min_encodings = None\r\n\r\n        # compute loss for embedding\r\n        if not self.legacy:\r\n            loss = self.beta * torch.mean((z_q.detach() - z) ** 2) + torch.mean(\r\n                (z_q - z.detach()) ** 2\r\n            )\r\n        else:\r\n            loss = torch.mean((z_q.detach() - z) ** 2) + self.beta * torch.mean(\r\n                (z_q - z.detach()) ** 2\r\n            )\r\n\r\n        # preserve gradients\r\n        z_q = z + (z_q - z).detach()\r\n\r\n        # reshape back to match original input shape\r\n        z_q = rearrange(z_q, \"b h w c -> b c h w\").contiguous()\r\n\r\n        if self.remap is not None:\r\n            min_encoding_indices = min_encoding_indices.reshape(\r\n                z.shape[0], -1\r\n            )  # add batch axis\r\n            min_encoding_indices = self.remap_to_used(min_encoding_indices)\r\n            min_encoding_indices = min_encoding_indices.reshape(-1, 1)  # flatten\r\n\r\n        if self.sane_index_shape:\r\n            min_encoding_indices = min_encoding_indices.reshape(\r\n                z_q.shape[0], z_q.shape[2], z_q.shape[3]\r\n            )\r\n\r\n        return z_q, loss, (perplexity, min_encodings, min_encoding_indices)\r\n\r\n    def get_codebook_entry(self, indices, shape):\r\n        # shape specifying (batch, height, width, channel)\r\n        if self.remap is not None:\r\n            indices = indices.reshape(shape[0], -1)  # add batch axis\r\n            indices = self.unmap_to_all(indices)\r\n            indices = indices.reshape(-1)  # flatten again\r\n\r\n        # get quantized latent vectors\r\n        z_q = self.embedding(indices)\r\n\r\n        if shape is not None:\r\n            z_q = z_q.view(shape)\r\n            # reshape back to match original input shape\r\n            z_q = z_q.permute(0, 3, 1, 2).contiguous()\r\n\r\n        return z_q\r\n\r\n\r\nclass EmbeddingEMA(nn.Module):\r\n    def __init__(self, num_tokens, codebook_dim, decay=0.99, eps=1e-5):\r\n        super().__init__()\r\n        self.decay = decay\r\n        self.eps = eps\r\n        weight = torch.randn(num_tokens, codebook_dim)\r\n        self.weight = nn.Parameter(weight, requires_grad=False)\r\n        self.cluster_size = nn.Parameter(torch.zeros(num_tokens), requires_grad=False)\r\n        self.embed_avg = nn.Parameter(weight.clone(), requires_grad=False)\r\n        self.update = True\r\n\r\n    def forward(self, embed_id):\r\n        return F.embedding(embed_id, self.weight)\r\n\r\n    def cluster_size_ema_update(self, new_cluster_size):\r\n        self.cluster_size.data.mul_(self.decay).add_(\r\n            new_cluster_size, alpha=1 - self.decay\r\n        )\r\n\r\n    def embed_avg_ema_update(self, new_embed_avg):\r\n        self.embed_avg.data.mul_(self.decay).add_(new_embed_avg, alpha=1 - self.decay)\r\n\r\n    def weight_update(self, num_tokens):\r\n        n = self.cluster_size.sum()\r\n        smoothed_cluster_size = (\r\n            (self.cluster_size + self.eps) / (n + num_tokens * self.eps) * n\r\n        )\r\n        # normalize embedding average with smoothed cluster size\r\n        embed_normalized = self.embed_avg / smoothed_cluster_size.unsqueeze(1)\r\n        self.weight.data.copy_(embed_normalized)\r\n\r\n\r\nclass EMAVectorQuantizer(nn.Module):\r\n    def __init__(\r\n        self,\r\n        n_embed,\r\n        embedding_dim,\r\n        beta,\r\n        decay=0.99,\r\n        eps=1e-5,\r\n        remap=None,\r\n        unknown_index=\"random\",\r\n    ):\r\n        super().__init__()\r\n        self.codebook_dim = embedding_dim\r\n        self.num_tokens = n_embed\r\n        self.beta = beta\r\n        self.embedding = EmbeddingEMA(self.num_tokens, self.codebook_dim, decay, eps)\r\n\r\n        self.remap = remap\r\n        if self.remap is not None:\r\n            self.register_buffer(\"used\", torch.tensor(np.load(self.remap)))\r\n            self.re_embed = self.used.shape[0]\r\n            self.unknown_index = unknown_index  # \"random\" or \"extra\" or integer\r\n            if self.unknown_index == \"extra\":\r\n                self.unknown_index = self.re_embed\r\n                self.re_embed = self.re_embed + 1\r\n            print(\r\n                f\"Remapping {self.n_embed} indices to {self.re_embed} indices. \"\r\n                f\"Using {self.unknown_index} for unknown indices.\"\r\n            )\r\n        else:\r\n            self.re_embed = n_embed\r\n\r\n    def remap_to_used(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape) > 1\r\n        inds = inds.reshape(ishape[0], -1)\r\n        used = self.used.to(inds)\r\n        match = (inds[:, :, None] == used[None, None, ...]).long()\r\n        new = match.argmax(-1)\r\n        unknown = match.sum(2) < 1\r\n        if self.unknown_index == \"random\":\r\n            new[unknown] = torch.randint(0, self.re_embed, size=new[unknown].shape).to(\r\n                device=new.device\r\n            )\r\n        else:\r\n            new[unknown] = self.unknown_index\r\n        return new.reshape(ishape)\r\n\r\n    def unmap_to_all(self, inds):\r\n        ishape = inds.shape\r\n        assert len(ishape) > 1\r\n        inds = inds.reshape(ishape[0], -1)\r\n        used = self.used.to(inds)\r\n        if self.re_embed > self.used.shape[0]:  # extra token\r\n            inds[inds >= self.used.shape[0]] = 0  # simply set to zero\r\n        back = torch.gather(used[None, :][inds.shape[0] * [0], :], 1, inds)\r\n        return back.reshape(ishape)\r\n\r\n    def forward(self, z):\r\n        # reshape z -> (batch, height, width, channel) and flatten\r\n        # z, 'b c h w -> b h w c'\r\n        z = rearrange(z, \"b c h w -> b h w c\")\r\n        z_flattened = z.reshape(-1, self.codebook_dim)\r\n\r\n        # distances from z to embeddings e_j (z - e)^2 = z^2 + e^2 - 2 e * z\r\n        d = (\r\n            z_flattened.pow(2).sum(dim=1, keepdim=True)\r\n            + self.embedding.weight.pow(2).sum(dim=1)\r\n            - 2 * torch.einsum(\"bd,nd->bn\", z_flattened, self.embedding.weight)\r\n        )  # 'n d -> d n'\r\n\r\n        encoding_indices = torch.argmin(d, dim=1)\r\n\r\n        z_q = self.embedding(encoding_indices).view(z.shape)\r\n        encodings = F.one_hot(encoding_indices, self.num_tokens).type(z.dtype)\r\n        avg_probs = torch.mean(encodings, dim=0)\r\n        perplexity = torch.exp(-torch.sum(avg_probs * torch.log(avg_probs + 1e-10)))\r\n\r\n        if self.training and self.embedding.update:\r\n            # EMA cluster size\r\n            encodings_sum = encodings.sum(0)\r\n            self.embedding.cluster_size_ema_update(encodings_sum)\r\n            # EMA embedding average\r\n            embed_sum = encodings.transpose(0, 1) @ z_flattened\r\n            self.embedding.embed_avg_ema_update(embed_sum)\r\n            # normalize embed_avg and update weight\r\n            self.embedding.weight_update(self.num_tokens)\r\n\r\n        # compute loss for embedding\r\n        loss = self.beta * F.mse_loss(z_q.detach(), z)\r\n\r\n        # preserve gradients\r\n        z_q = z + (z_q - z).detach()\r\n\r\n        # reshape back to match original input shape\r\n        # z_q, 'b h w c -> b c h w'\r\n        z_q = rearrange(z_q, \"b h w c -> b c h w\")\r\n        return z_q, loss, (perplexity, encodings, encoding_indices)\r\n"}