{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_components.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nUnit tests for CtrlColor core components\nTests individual modules and their functionality\n\"\"\"\n\nimport unittest\nimport torch\nimport numpy as np\nfrom pathlib import Path\nimport tempfile\nimport logging\n\nlogging.basicConfig(level=logging.INFO)\n\nclass TestControlNet(unittest.TestCase):\n    \"\"\"Test ControlNet functionality\"\"\"\n    \n    def setUp(self):\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n        self.batch_size = 2\n        self.height, self.width = 512, 512\n        \n    def test_controlnet_forward(self):\n        \"\"\"Test ControlNet forward pass\"\"\"\n        try:\n            from cldm.cldm import ControlledUnetModel\n            \n            # Create mock model\n            model = ControlledUnetModel(\n                image_size=64,\n                in_channels=4,\n                model_channels=320,\n                out_channels=4,\n                num_res_blocks=2,\n                attention_resolutions=[4, 2, 1],\n                channel_mult=[1, 2, 4, 4],\n                num_heads=8,\n                use_spatial_transformer=True,\n                transformer_depth=1,\n                context_dim=768,\n                use_checkpoint=False,\n                legacy=False,\n            ).to(self.device)\n            \n            # Test inputs\n            x = torch.randn(self.batch_size, 4, 64, 64).to(self.device)\n            timesteps = torch.randint(0, 1000, (self.batch_size,)).to(self.device)\n            context = torch.randn(self.batch_size, 77, 768).to(self.device)\n            control = torch.randn(self.batch_size, 3, 512, 512).to(self.device)\n            \n            with torch.no_grad():\n                output = model(x, timesteps, context, control)\n            \n            self.assertEqual(output.shape, x.shape)\n            \n        except Exception as e:\n            self.fail(f\"ControlNet forward pass failed: {e}\")\n\nclass TestAttentionModules(unittest.TestCase):\n    \"\"\"Test custom attention mechanisms\"\"\"\n    \n    def setUp(self):\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n        \n    def test_spatial_transformer(self):\n        \"\"\"Test SpatialTransformer module\"\"\"\n        try:\n            from ldm.modules.attention_dcn_control import SpatialTransformer\n            \n            transformer = SpatialTransformer(\n                in_channels=320,\n                n_heads=8,\n                d_head=40,\n                depth=1,\n                context_dim=768\n            ).to(self.device)\n            \n            x = torch.randn(2, 320, 64, 64).to(self.device)\n            context = torch.randn(2, 77, 768).to(self.device)\n            \n            with torch.no_grad():\n                output = transformer(x, context)\n            \n            self.assertEqual(output.shape, x.shape)\n            \n        except Exception as e:\n            self.fail(f\"SpatialTransformer test failed: {e}\")\n            \n    def test_cross_attention(self):\n        \"\"\"Test CrossAttention module\"\"\"\n        try:\n            from ldm.modules.attention_dcn_control import CrossAttention\n            \n            attention = CrossAttention(\n                query_dim=320,\n                context_dim=768,\n                heads=8,\n                dim_head=40\n            ).to(self.device)\n            \n            x = torch.randn(2, 4096, 320).to(self.device)  # flattened spatial\n            context = torch.randn(2, 77, 768).to(self.device)\n            \n            with torch.no_grad():\n                output = attention(x, context)\n            \n            self.assertEqual(output.shape, x.shape)\n            \n        except Exception as e:\n            self.fail(f\"CrossAttention test failed: {e}\")\n\nclass TestLossComponents(unittest.TestCase):\n    \"\"\"Test loss functions\"\"\"\n    \n    def setUp(self):\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n        \n    def test_contextual_loss(self):\n        \"\"\"Test ContextualLoss implementation\"\"\"\n        try:\n            from ldm.models.diffusion.ddpm import ContextualLoss\n            \n            loss_fn = ContextualLoss().to(self.device)\n            \n            x = torch.randn(2, 512, 16, 16).to(self.device)\n            y = torch.randn(2, 512, 16, 16).to(self.device)\n            \n            loss = loss_fn(x, y)\n            \n            self.assertIsInstance(loss, torch.Tensor)\n            self.assertEqual(loss.dim(), 0)  # scalar loss\n            self.assertGreater(loss.item(), 0)  # positive loss\n            \n        except Exception as e:\n            self.fail(f\"ContextualLoss test failed: {e}\")\n\nclass TestImageProcessing(unittest.TestCase):\n    \"\"\"Test image processing utilities\"\"\"\n    \n    def test_image_degradation(self):\n        \"\"\"Test image degradation functions\"\"\"\n        try:\n            from ldm.modules.image_degradation import bsrgan\n            \n            # Create test image\n            img = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)\n            \n            # Test degradation\n            degraded = bsrgan.degradation_bsrgan_variant(img, sf=4)\n            \n            self.assertIsInstance(degraded, np.ndarray)\n            self.assertEqual(len(degraded.shape), 3)\n            \n        except Exception as e:\n            self.fail(f\"Image degradation test failed: {e}\")\n            \n    def test_utils_image(self):\n        \"\"\"Test image utility functions\"\"\"\n        try:\n            from ldm.modules.image_degradation import utils_image as util\n            \n            # Test image conversion functions\n            img_uint = np.random.randint(0, 255, (64, 64, 3), dtype=np.uint8)\n            img_single = util.uint2single(img_uint)\n            img_back = util.single2uint(img_single)\n            \n            self.assertEqual(img_single.dtype, np.float32)\n            self.assertEqual(img_back.dtype, np.uint8)\n            self.assertTrue(np.allclose(img_uint, img_back, atol=1))\n            \n        except Exception as e:\n            self.fail(f\"Image utils test failed: {e}\")\n\ndef run_component_tests():\n    \"\"\"Run all component tests\"\"\"\n    test_classes = [\n        TestControlNet,\n        TestAttentionModules, \n        TestLossComponents,\n        TestImageProcessing\n    ]\n    \n    suite = unittest.TestSuite()\n    \n    for test_class in test_classes:\n        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)\n        suite.addTests(tests)\n    \n    runner = unittest.TextTestRunner(verbosity=2)\n    result = runner.run(suite)\n    \n    return result.wasSuccessful()\n\nif __name__ == \"__main__\":\n    success = run_component_tests()\n    exit(0 if success else 1)\n"}