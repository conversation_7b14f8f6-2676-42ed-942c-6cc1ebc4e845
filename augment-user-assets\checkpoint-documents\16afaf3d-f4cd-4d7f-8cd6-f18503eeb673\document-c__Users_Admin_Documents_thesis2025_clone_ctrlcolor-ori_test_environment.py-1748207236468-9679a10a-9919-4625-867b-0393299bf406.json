{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nEnvironment and dependency testing for CtrlColor\nTests basic functionality and dependencies before full evaluation\n\"\"\"\n\nimport sys\nimport torch\nimport numpy as np\nimport cv2\nfrom pathlib import Path\nimport logging\n\n# Setup logging\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(__name__)\n\ndef test_pytorch_setup():\n    \"\"\"Test PyTorch installation and CUDA availability\"\"\"\n    logger.info(\"Testing PyTorch setup...\")\n    \n    # Check PyTorch version\n    logger.info(f\"PyTorch version: {torch.__version__}\")\n    \n    # Check CUDA availability\n    if torch.cuda.is_available():\n        logger.info(f\"CUDA available: {torch.cuda.get_device_name(0)}\")\n        logger.info(f\"CUDA version: {torch.version.cuda}\")\n        \n        # Test basic CUDA operations\n        x = torch.randn(100, 100).cuda()\n        y = torch.randn(100, 100).cuda()\n        z = torch.mm(x, y)\n        logger.info(\"CUDA operations: PASSED\")\n    else:\n        logger.warning(\"CUDA not available - will run on CPU\")\n    \n    return torch.cuda.is_available()\n\ndef test_model_loading():\n    \"\"\"Test if core models can be loaded\"\"\"\n    logger.info(\"Testing model loading...\")\n    \n    try:\n        # Test basic imports\n        from ldm.models.diffusion.ddpm import LatentDiffusion\n        from cldm.model import create_model, load_state_dict\n        logger.info(\"Core imports: PASSED\")\n        \n        # Test config loading\n        config_path = Path(\"models/cldm_v15_inpainting_infer.yaml\")\n        if config_path.exists():\n            logger.info(\"Config file found: PASSED\")\n        else:\n            logger.warning(\"Config file not found - may need to download\")\n            \n        return True\n    except ImportError as e:\n        logger.error(f\"Import error: {e}\")\n        return False\n\ndef test_image_processing():\n    \"\"\"Test image processing pipeline\"\"\"\n    logger.info(\"Testing image processing...\")\n    \n    try:\n        # Create test image\n        test_img = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)\n        \n        # Test OpenCV operations\n        gray = cv2.cvtColor(test_img, cv2.COLOR_RGB2GRAY)\n        resized = cv2.resize(test_img, (256, 256))\n        \n        # Test tensor conversion\n        tensor_img = torch.from_numpy(test_img).float() / 255.0\n        tensor_img = tensor_img.permute(2, 0, 1).unsqueeze(0)\n        \n        logger.info(\"Image processing: PASSED\")\n        return True\n    except Exception as e:\n        logger.error(f\"Image processing error: {e}\")\n        return False\n\ndef test_attention_modules():\n    \"\"\"Test custom attention modules\"\"\"\n    logger.info(\"Testing attention modules...\")\n    \n    try:\n        from ldm.modules.attention_dcn_control import SpatialTransformer\n        \n        # Test basic attention\n        transformer = SpatialTransformer(\n            in_channels=320,\n            n_heads=8,\n            d_head=40,\n            depth=1,\n            context_dim=768\n        )\n        \n        # Test forward pass\n        x = torch.randn(1, 320, 64, 64)\n        context = torch.randn(1, 77, 768)\n        \n        if torch.cuda.is_available():\n            transformer = transformer.cuda()\n            x = x.cuda()\n            context = context.cuda()\n        \n        with torch.no_grad():\n            output = transformer(x, context)\n        \n        logger.info(\"Attention modules: PASSED\")\n        return True\n    except Exception as e:\n        logger.error(f\"Attention module error: {e}\")\n        return False\n\ndef main():\n    \"\"\"Run all environment tests\"\"\"\n    logger.info(\"Starting CtrlColor environment tests...\")\n    \n    tests = [\n        (\"PyTorch Setup\", test_pytorch_setup),\n        (\"Model Loading\", test_model_loading),\n        (\"Image Processing\", test_image_processing),\n        (\"Attention Modules\", test_attention_modules),\n    ]\n    \n    results = {}\n    for test_name, test_func in tests:\n        try:\n            results[test_name] = test_func()\n        except Exception as e:\n            logger.error(f\"{test_name} failed with exception: {e}\")\n            results[test_name] = False\n    \n    # Summary\n    logger.info(\"\\n\" + \"=\"*50)\n    logger.info(\"ENVIRONMENT TEST SUMMARY\")\n    logger.info(\"=\"*50)\n    \n    for test_name, passed in results.items():\n        status = \"PASSED\" if passed else \"FAILED\"\n        logger.info(f\"{test_name}: {status}\")\n    \n    all_passed = all(results.values())\n    if all_passed:\n        logger.info(\"\\nAll tests PASSED! Ready for full evaluation.\")\n    else:\n        logger.warning(\"\\nSome tests FAILED! Please fix issues before proceeding.\")\n    \n    return all_passed\n\nif __name__ == \"__main__\":\n    success = main()\n    sys.exit(0 if success else 1)\n"}