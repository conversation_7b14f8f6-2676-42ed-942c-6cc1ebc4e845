{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_environment.py"}, "originalCode": "#!/usr/bin/env python3\r\n\"\"\"\r\nEnvironment and dependency testing for CtrlColor\r\nTests basic functionality and dependencies before full evaluation\r\n\"\"\"\r\n\r\nimport sys\r\nimport torch\r\nimport numpy as np\r\nimport cv2\r\nfrom pathlib import Path\r\nimport logging\r\n\r\n# Setup logging\r\nlogging.basicConfig(level=logging.INFO)\r\nlogger = logging.getLogger(__name__)\r\n\r\ndef test_pytorch_setup():\r\n    \"\"\"Test PyTorch installation and CUDA availability\"\"\"\r\n    logger.info(\"Testing PyTorch setup...\")\r\n    \r\n    # Check PyTorch version\r\n    logger.info(f\"PyTorch version: {torch.__version__}\")\r\n    \r\n    # Check CUDA availability\r\n    if torch.cuda.is_available():\r\n        logger.info(f\"CUDA available: {torch.cuda.get_device_name(0)}\")\r\n        logger.info(f\"CUDA version: {torch.version.cuda}\")\r\n        \r\n        # Test basic CUDA operations\r\n        x = torch.randn(100, 100).cuda()\r\n        y = torch.randn(100, 100).cuda()\r\n        z = torch.mm(x, y)\r\n        logger.info(\"CUDA operations: PASSED\")\r\n    else:\r\n        logger.warning(\"CUDA not available - will run on CPU\")\r\n    \r\n    return torch.cuda.is_available()\r\n\r\ndef test_model_loading():\r\n    \"\"\"Test if core models can be loaded\"\"\"\r\n    logger.info(\"Testing model loading...\")\r\n    \r\n    try:\r\n        # Test basic imports\r\n        from ldm.models.diffusion.ddpm import LatentDiffusion\r\n        from cldm.model import create_model, load_state_dict\r\n        logger.info(\"Core imports: PASSED\")\r\n        \r\n        # Test config loading\r\n        config_path = Path(\"models/cldm_v15_inpainting_infer.yaml\")\r\n        if config_path.exists():\r\n            logger.info(\"Config file found: PASSED\")\r\n        else:\r\n            logger.warning(\"Config file not found - may need to download\")\r\n            \r\n        return True\r\n    except ImportError as e:\r\n        logger.error(f\"Import error: {e}\")\r\n        return False\r\n\r\ndef test_image_processing():\r\n    \"\"\"Test image processing pipeline\"\"\"\r\n    logger.info(\"Testing image processing...\")\r\n    \r\n    try:\r\n        # Create test image\r\n        test_img = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)\r\n        \r\n        # Test OpenCV operations\r\n        gray = cv2.cvtColor(test_img, cv2.COLOR_RGB2GRAY)\r\n        resized = cv2.resize(test_img, (256, 256))\r\n        \r\n        # Test tensor conversion\r\n        tensor_img = torch.from_numpy(test_img).float() / 255.0\r\n        tensor_img = tensor_img.permute(2, 0, 1).unsqueeze(0)\r\n        \r\n        logger.info(\"Image processing: PASSED\")\r\n        return True\r\n    except Exception as e:\r\n        logger.error(f\"Image processing error: {e}\")\r\n        return False\r\n\r\ndef test_attention_modules():\r\n    \"\"\"Test custom attention modules\"\"\"\r\n    logger.info(\"Testing attention modules...\")\r\n    \r\n    try:\r\n        from ldm.modules.attention_dcn_control import SpatialTransformer\r\n        \r\n        # Test basic attention\r\n        transformer = SpatialTransformer(\r\n            in_channels=320,\r\n            n_heads=8,\r\n            d_head=40,\r\n            depth=1,\r\n            context_dim=768\r\n        )\r\n        \r\n        # Test forward pass\r\n        x = torch.randn(1, 320, 64, 64)\r\n        context = torch.randn(1, 77, 768)\r\n        \r\n        if torch.cuda.is_available():\r\n            transformer = transformer.cuda()\r\n            x = x.cuda()\r\n            context = context.cuda()\r\n        \r\n        with torch.no_grad():\r\n            output = transformer(x, context)\r\n        \r\n        logger.info(\"Attention modules: PASSED\")\r\n        return True\r\n    except Exception as e:\r\n        logger.error(f\"Attention module error: {e}\")\r\n        return False\r\n\r\ndef main():\r\n    \"\"\"Run all environment tests\"\"\"\r\n    logger.info(\"Starting CtrlColor environment tests...\")\r\n    \r\n    tests = [\r\n        (\"PyTorch Setup\", test_pytorch_setup),\r\n        (\"Model Loading\", test_model_loading),\r\n        (\"Image Processing\", test_image_processing),\r\n        (\"Attention Modules\", test_attention_modules),\r\n    ]\r\n    \r\n    results = {}\r\n    for test_name, test_func in tests:\r\n        try:\r\n            results[test_name] = test_func()\r\n        except Exception as e:\r\n            logger.error(f\"{test_name} failed with exception: {e}\")\r\n            results[test_name] = False\r\n    \r\n    # Summary\r\n    logger.info(\"\\n\" + \"=\"*50)\r\n    logger.info(\"ENVIRONMENT TEST SUMMARY\")\r\n    logger.info(\"=\"*50)\r\n    \r\n    for test_name, passed in results.items():\r\n        status = \"PASSED\" if passed else \"FAILED\"\r\n        logger.info(f\"{test_name}: {status}\")\r\n    \r\n    all_passed = all(results.values())\r\n    if all_passed:\r\n        logger.info(\"\\nAll tests PASSED! Ready for full evaluation.\")\r\n    else:\r\n        logger.warning(\"\\nSome tests FAILED! Please fix issues before proceeding.\")\r\n    \r\n    return all_passed\r\n\r\nif __name__ == \"__main__\":\r\n    success = main()\r\n    sys.exit(0 if success else 1)\r\n"}