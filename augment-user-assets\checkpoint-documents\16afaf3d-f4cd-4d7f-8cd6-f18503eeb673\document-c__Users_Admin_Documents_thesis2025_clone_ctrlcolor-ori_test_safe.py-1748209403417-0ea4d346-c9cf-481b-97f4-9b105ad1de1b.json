{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/test_safe.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nSafe Test Module for CtrlColor\n==============================\n\nThis is a safer version of test.py that loads models on-demand\ninstead of at import time to avoid segmentation faults.\n\"\"\"\n\nimport os\nimport sys\nimport cv2\nimport einops\nimport gradio as gr\nimport numpy as np\nimport torch\nimport random\nimport gc\nfrom pathlib import Path\n\nfrom pytorch_lightning import seed_everything\nfrom annotator.util import resize_image\nfrom cldm.model import create_model, load_state_dict\nfrom cldm.ddim_haced_sag_step import DDIMSampler\nfrom lavis.models import load_model_and_preprocess\nfrom PIL import Image\nimport tqdm\n\nfrom ldm.models.autoencoder_train import AutoencoderKL\n\n# Global variables for models (loaded on-demand)\n_model = None\n_ddim_sampler = None\n_BLIP_model = None\n_vis_processors = None\n_vae_model = None\n_device = None\n\ndef get_device():\n    \"\"\"Get device safely\"\"\"\n    global _device\n    if _device is None:\n        _device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n    return _device\n\ndef load_main_model():\n    \"\"\"Load main model safely\"\"\"\n    global _model, _ddim_sampler\n    \n    if _model is not None:\n        return _model, _ddim_sampler\n    \n    print(\"🤖 Loading main model...\")\n    \n    try:\n        # Set conservative memory settings\n        if torch.cuda.is_available():\n            torch.cuda.set_per_process_memory_fraction(0.7)\n            torch.cuda.empty_cache()\n        \n        ckpt_path = \"./pretrained_models/main_model.ckpt\"\n        \n        if not os.path.exists(ckpt_path):\n            raise FileNotFoundError(f\"Model file not found: {ckpt_path}\")\n        \n        # Load model\n        _model = create_model('./models/cldm_v15_inpainting_infer1.yaml').cpu()\n        _model.load_state_dict(load_state_dict(ckpt_path, location='cuda'), strict=False)\n        _model = _model.cuda()\n        \n        # Create sampler\n        _ddim_sampler = DDIMSampler(_model)\n        \n        print(\"✅ Main model loaded successfully\")\n        return _model, _ddim_sampler\n        \n    except Exception as e:\n        print(f\"❌ Failed to load main model: {e}\")\n        raise\n\ndef load_blip_model():\n    \"\"\"Load BLIP model safely\"\"\"\n    global _BLIP_model, _vis_processors\n    \n    if _BLIP_model is not None:\n        return _BLIP_model, _vis_processors\n    \n    print(\"🤖 Loading BLIP model...\")\n    \n    try:\n        device = get_device()\n        _BLIP_model, _vis_processors, _ = load_model_and_preprocess(\n            name=\"blip_caption\", \n            model_type=\"base_coco\", \n            is_eval=True, \n            device=device\n        )\n        \n        print(\"✅ BLIP model loaded successfully\")\n        return _BLIP_model, _vis_processors\n        \n    except Exception as e:\n        print(f\"❌ Failed to load BLIP model: {e}\")\n        raise\n\ndef load_vae_model():\n    \"\"\"Load VAE model safely\"\"\"\n    global _vae_model\n    \n    if _vae_model is not None:\n        return _vae_model\n    \n    print(\"🤖 Loading VAE model...\")\n    \n    try:\n        vae_model_ckpt_path = \"./pretrained_models/content-guided_deformable_vae.ckpt\"\n        \n        if not os.path.exists(vae_model_ckpt_path):\n            raise FileNotFoundError(f\"VAE model file not found: {vae_model_ckpt_path}\")\n        \n        init_config = {\n            \"embed_dim\": 4,\n            \"monitor\": \"val/rec_loss\",\n            \"ddconfig\": {\n                \"double_z\": True,\n                \"z_channels\": 4,\n                \"resolution\": 256,\n                \"in_channels\": 3,\n                \"out_ch\": 3,\n                \"ch\": 128,\n                \"ch_mult\": [1, 2, 4, 4],\n                \"num_res_blocks\": 2,\n                \"attn_resolutions\": [],\n                \"dropout\": 0.0,\n            },\n            \"lossconfig\": {\n                \"target\": \"ldm.modules.losses.LPIPSWithDiscriminator\",\n                \"params\": {\n                    \"disc_start\": 501,\n                    \"kl_weight\": 0,\n                    \"disc_weight\": 0.025,\n                    \"disc_factor\": 1.0\n                }\n            }\n        }\n        \n        _vae_model = AutoencoderKL(**init_config)\n        _vae_model.load_state_dict(load_state_dict(vae_model_ckpt_path, location='cuda'))\n        _vae_model = _vae_model.cuda()\n        \n        print(\"✅ VAE model loaded successfully\")\n        return _vae_model\n        \n    except Exception as e:\n        print(f\"❌ Failed to load VAE model: {e}\")\n        raise\n\ndef get_models():\n    \"\"\"Get all models, loading them if necessary\"\"\"\n    model, ddim_sampler = load_main_model()\n    BLIP_model, vis_processors = load_blip_model()\n    vae_model = load_vae_model()\n    device = get_device()\n    \n    return model, ddim_sampler, BLIP_model, vis_processors, vae_model, device\n\ndef encode_mask(mask, masked_image):\n    \"\"\"Encode mask and masked image\"\"\"\n    model, _, _, _, _, device = get_models()\n    \n    mask = torch.nn.functional.interpolate(mask, size=(mask.shape[2] // 8, mask.shape[3] // 8))\n    mask = mask.to(device=\"cuda\")\n    masked_image_latents = model.get_first_stage_encoding(model.encode_first_stage(masked_image.cuda())).detach()\n    return mask, masked_image_latents\n\ndef get_mask(input_image, hint_image):\n    \"\"\"Generate mask from input and hint images\"\"\"\n    mask = input_image.copy()\n    H, W, C = input_image.shape\n    for i in range(H):\n        for j in range(W):\n            if input_image[i, j, 0] == hint_image[i, j, 0]:\n                mask[i, j, :] = 255.\n            else:\n                mask[i, j, :] = 0.\n    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))\n    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)\n    return mask\n\ndef prepare_mask_and_masked_image(image, mask):\n    \"\"\"Prepare mask and masked image for processing\"\"\"\n    # This is the same function from the original test.py\n    # [Implementation details omitted for brevity - same as original]\n    \n    if isinstance(image, (Image.Image, np.ndarray)):\n        image = [image]\n\n    if isinstance(image, list) and isinstance(image[0], Image.Image):\n        image = [np.array(i.convert(\"RGB\"))[None, :] for i in image]\n        image = np.concatenate(image, axis=0)\n    elif isinstance(image, list) and isinstance(image[0], np.ndarray):\n        image = np.concatenate([i[None, :] for i in image], axis=0)\n\n    image = image.transpose(0, 3, 1, 2)\n    image = torch.from_numpy(image).to(dtype=torch.float32) / 127.5 - 1.0\n\n    # preprocess mask\n    if isinstance(mask, (Image.Image, np.ndarray)):\n        mask = [mask]\n\n    if isinstance(mask, list) and isinstance(mask[0], Image.Image):\n        mask = np.concatenate([np.array(m.convert(\"L\"))[None, None, :] for m in mask], axis=0)\n        mask = mask.astype(np.float32) / 255.0\n    elif isinstance(mask, list) and isinstance(mask[0], np.ndarray):\n        mask = np.concatenate([m[None, None, :] for m in mask], axis=0)\n\n    mask[mask < 0.5] = 0\n    mask[mask >= 0.5] = 1\n    mask = torch.from_numpy(mask)\n\n    masked_image = image * (mask < 0.5)\n\n    return mask, masked_image\n\ndef is_gray_scale(img, threshold=10):\n    \"\"\"Check if image is grayscale\"\"\"\n    img = Image.fromarray(img)\n    if len(img.getbands()) == 1:\n        return True\n    img1 = np.asarray(img.getchannel(channel=0), dtype=np.int16)\n    img2 = np.asarray(img.getchannel(channel=1), dtype=np.int16)\n    img3 = np.asarray(img.getchannel(channel=2), dtype=np.int16)\n    diff1 = (img1 - img2).var()\n    diff2 = (img2 - img3).var()\n    diff3 = (img3 - img1).var()\n    diff_sum = (diff1 + diff2 + diff3) / 3.0\n    return diff_sum <= threshold\n\ndef randn_tensor(shape, generator=None, device=None, dtype=None, layout=None):\n    \"\"\"Generate random tensor\"\"\"\n    rand_device = device\n    batch_size = shape[0]\n\n    layout = layout or torch.strided\n    device = device or torch.device(\"cpu\")\n\n    if generator is not None:\n        gen_device_type = generator.device.type if not isinstance(generator, list) else generator[0].device.type\n        if gen_device_type != device.type and gen_device_type == \"cpu\":\n            rand_device = \"cpu\"\n        elif gen_device_type != device.type and gen_device_type == \"cuda\":\n            raise ValueError(f\"Cannot generate a {device} tensor from a generator of type {gen_device_type}.\")\n\n    if isinstance(generator, list) and len(generator) == 1:\n        generator = generator[0]\n\n    if isinstance(generator, list):\n        shape = (1,) + shape[1:]\n        latents = [\n            torch.randn(shape, generator=generator[i], device=rand_device, dtype=dtype, layout=layout)\n            for i in range(batch_size)\n        ]\n        latents = torch.cat(latents, dim=0).to(device)\n    else:\n        latents = torch.randn(shape, generator=generator, device=rand_device, dtype=dtype, layout=layout).to(device)\n\n    return latents\n\n# Set generator\ngenerator = torch.manual_seed(859311133)\n\ndef test_model_loading():\n    \"\"\"Test if models can be loaded safely\"\"\"\n    print(\"🧪 Testing model loading...\")\n    \n    try:\n        model, ddim_sampler, BLIP_model, vis_processors, vae_model, device = get_models()\n        \n        print(f\"✅ All models loaded successfully on {device}\")\n        print(f\"✅ Model type: {type(model)}\")\n        print(f\"✅ DDIM sampler: {type(ddim_sampler)}\")\n        print(f\"✅ BLIP model: {type(BLIP_model)}\")\n        print(f\"✅ VAE model: {type(vae_model)}\")\n        \n        return True\n        \n    except Exception as e:\n        print(f\"❌ Model loading failed: {e}\")\n        return False\n\nif __name__ == \"__main__\":\n    print(\"🧪 Testing Safe Model Loading\")\n    print(\"=\" * 40)\n    \n    success = test_model_loading()\n    \n    if success:\n        print(\"\\n🎉 Safe model loading successful!\")\n        print(\"You can now use this module for testing.\")\n    else:\n        print(\"\\n❌ Safe model loading failed.\")\n        print(\"Check the error messages above.\")\n"}