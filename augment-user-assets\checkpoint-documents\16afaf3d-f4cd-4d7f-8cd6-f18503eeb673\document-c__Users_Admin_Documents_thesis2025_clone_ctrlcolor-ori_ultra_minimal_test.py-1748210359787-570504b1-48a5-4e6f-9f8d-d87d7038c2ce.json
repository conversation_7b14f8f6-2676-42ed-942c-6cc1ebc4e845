{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/ultra_minimal_test.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nUltra Minimal Test - One Import at a Time\n=========================================\n\nTest each import individually with maximum isolation.\n\"\"\"\n\nimport sys\nimport os\n\n# Disable CUDA completely\nos.environ['CUDA_VISIBLE_DEVICES'] = ''\n\ndef test_single_import(import_line):\n    \"\"\"Test a single import in complete isolation\"\"\"\n    print(f\"Testing: {import_line}\")\n    try:\n        exec(import_line)\n        print(\"✅ SUCCESS\")\n        return True\n    except Exception as e:\n        print(f\"❌ FAILED: {e}\")\n        return False\n\ndef main():\n    \"\"\"Test imports one by one\"\"\"\n    print(\"🔍 Ultra Minimal Import Test\")\n    print(\"=\" * 40)\n    \n    # Test the most basic imports first\n    basic_tests = [\n        \"import sys\",\n        \"import os\", \n        \"import numpy\",\n        \"import torch\",\n        \"print('Basic imports OK')\"\n    ]\n    \n    print(\"\\n1️⃣ Testing Basic Python/ML Libraries:\")\n    for test in basic_tests:\n        if not test_single_import(test):\n            print(f\"💥 SEGFAULT AT: {test}\")\n            return\n    \n    # Test project-specific imports\n    project_tests = [\n        \"import config\",\n        \"import share\", \n        \"from annotator.util import resize_image\",\n        \"from cldm.model import create_model\",\n    ]\n    \n    print(\"\\n2️⃣ Testing Project Imports:\")\n    for test in project_tests:\n        if not test_single_import(test):\n            print(f\"💥 SEGFAULT AT: {test}\")\n            return\n    \n    print(\"\\n🎉 All imports successful!\")\n\nif __name__ == \"__main__\":\n    main()\n"}