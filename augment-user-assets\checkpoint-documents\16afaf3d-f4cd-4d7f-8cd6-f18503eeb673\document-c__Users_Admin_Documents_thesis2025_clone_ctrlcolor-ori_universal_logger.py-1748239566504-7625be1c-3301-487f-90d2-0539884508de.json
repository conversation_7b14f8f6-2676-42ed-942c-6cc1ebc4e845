{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone\\ctrlcolor-ori\\universal_logger.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nUniversal Logger for CtrlColor\n==============================\n\nA comprehensive logging system for all phases of CtrlColor:\n- Training logs\n- Evaluation logs\n- Testing logs\n- Performance metrics\n- Error tracking\n- Model checkpoints\n- Experiment tracking\n\nAuthor: CtrlColor Research Team\n\"\"\"\n\nimport os\nimport sys\nimport json\nimport time\nimport logging\nimport traceback\nfrom datetime import datetime\nfrom pathlib import Path\nfrom typing import Dict, Any, Optional, List\nimport torch\nimport numpy as np\nfrom contextlib import contextmanager\n\nclass CtrlColorLogger:\n    \"\"\"Universal logger for CtrlColor research\"\"\"\n\n    def __init__(self,\n                 experiment_name: str = None,\n                 log_dir: str = \"./logs\",\n                 level: str = \"INFO\",\n                 save_to_file: bool = True,\n                 save_to_console: bool = True):\n\n        self.experiment_name = experiment_name or f\"ctrlcolor_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n        self.log_dir = Path(log_dir)\n        self.experiment_dir = self.log_dir / self.experiment_name\n\n        # Create directories\n        self.experiment_dir.mkdir(parents=True, exist_ok=True)\n        (self.experiment_dir / \"train\").mkdir(exist_ok=True)\n        (self.experiment_dir / \"eval\").mkdir(exist_ok=True)\n        (self.experiment_dir / \"test\").mkdir(exist_ok=True)\n        (self.experiment_dir / \"images\").mkdir(exist_ok=True)\n        (self.experiment_dir / \"checkpoints\").mkdir(exist_ok=True)\n        (self.experiment_dir / \"metrics\").mkdir(exist_ok=True)\n\n        # Setup logging\n        self.logger = self._setup_logger(level, save_to_file, save_to_console)\n\n        # Experiment tracking\n        self.experiment_data = {\n            \"experiment_name\": self.experiment_name,\n            \"start_time\": datetime.now().isoformat(),\n            \"phases\": {},\n            \"metrics\": {},\n            \"errors\": [],\n            \"system_info\": self._get_system_info()\n        }\n\n        # Phase tracking\n        self.current_phase = None\n        self.phase_start_time = None\n        self.step_count = 0\n\n        self.logger.info(f\"🚀 CtrlColor Logger initialized: {self.experiment_name}\")\n        self.logger.info(f\"📁 Log directory: {self.experiment_dir}\")\n\n    def _setup_logger(self, level: str, save_to_file: bool, save_to_console: bool):\n        \"\"\"Setup the logging configuration\"\"\"\n        logger = logging.getLogger(f\"CtrlColor_{self.experiment_name}\")\n        logger.setLevel(getattr(logging, level.upper()))\n\n        # Clear existing handlers\n        logger.handlers.clear()\n\n        # Create formatter\n        formatter = logging.Formatter(\n            '%(asctime)s | %(levelname)8s | %(message)s',\n            datefmt='%Y-%m-%d %H:%M:%S'\n        )\n\n        # Console handler\n        if save_to_console:\n            console_handler = logging.StreamHandler(sys.stdout)\n            console_handler.setFormatter(formatter)\n            logger.addHandler(console_handler)\n\n        # File handler\n        if save_to_file:\n            file_handler = logging.FileHandler(\n                self.experiment_dir / f\"{self.experiment_name}.log\"\n            )\n            file_handler.setFormatter(formatter)\n            logger.addHandler(file_handler)\n\n        return logger\n\n    def _get_system_info(self) -> Dict[str, Any]:\n        \"\"\"Get system information\"\"\"\n        info = {\n            \"python_version\": sys.version,\n            \"platform\": sys.platform,\n            \"timestamp\": datetime.now().isoformat()\n        }\n\n        try:\n            import torch\n            info[\"pytorch_version\"] = torch.__version__\n            info[\"cuda_available\"] = torch.cuda.is_available()\n            if torch.cuda.is_available():\n                info[\"cuda_version\"] = torch.version.cuda\n                info[\"gpu_count\"] = torch.cuda.device_count()\n                info[\"gpu_names\"] = [torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())]\n        except ImportError:\n            pass\n\n        return info\n\n    @contextmanager\n    def phase(self, phase_name: str):\n        \"\"\"Context manager for logging phases (train/eval/test)\"\"\"\n        self.start_phase(phase_name)\n        try:\n            yield self\n        except Exception as e:\n            self.log_error(f\"Error in {phase_name} phase\", e)\n            raise\n        finally:\n            self.end_phase()\n\n    def start_phase(self, phase_name: str):\n        \"\"\"Start a new phase (train/eval/test)\"\"\"\n        if self.current_phase:\n            self.end_phase()\n\n        self.current_phase = phase_name\n        self.phase_start_time = time.time()\n        self.step_count = 0\n\n        self.logger.info(f\"📊 Starting {phase_name} phase\")\n\n        # Initialize phase data\n        self.experiment_data[\"phases\"][phase_name] = {\n            \"start_time\": datetime.now().isoformat(),\n            \"steps\": [],\n            \"metrics\": {},\n            \"duration\": None\n        }\n\n    def end_phase(self):\n        \"\"\"End the current phase\"\"\"\n        if not self.current_phase:\n            return\n\n        duration = time.time() - self.phase_start_time\n        self.experiment_data[\"phases\"][self.current_phase][\"duration\"] = duration\n        self.experiment_data[\"phases\"][self.current_phase][\"end_time\"] = datetime.now().isoformat()\n\n        self.logger.info(f\"✅ Completed {self.current_phase} phase in {duration:.2f}s\")\n\n        # Save phase summary\n        self._save_phase_summary(self.current_phase)\n\n        self.current_phase = None\n        self.phase_start_time = None\n\n    def log_step(self, step: int, metrics: Dict[str, float], images: Dict[str, Any] = None):\n        \"\"\"Log a training/evaluation step\"\"\"\n        if not self.current_phase:\n            self.logger.warning(\"No active phase. Call start_phase() first.\")\n            return\n\n        self.step_count += 1\n\n        # Log metrics\n        metrics_str = \" | \".join([f\"{k}: {v:.4f}\" for k, v in metrics.items()])\n        self.logger.info(f\"Step {step:6d} | {metrics_str}\")\n\n        # Store step data\n        step_data = {\n            \"step\": step,\n            \"timestamp\": datetime.now().isoformat(),\n            \"metrics\": metrics\n        }\n\n        self.experiment_data[\"phases\"][self.current_phase][\"steps\"].append(step_data)\n\n        # Save images if provided\n        if images:\n            self._save_step_images(step, images)\n\n        # Update running metrics\n        for metric_name, value in metrics.items():\n            if metric_name not in self.experiment_data[\"phases\"][self.current_phase][\"metrics\"]:\n                self.experiment_data[\"phases\"][self.current_phase][\"metrics\"][metric_name] = []\n            self.experiment_data[\"phases\"][self.current_phase][\"metrics\"][metric_name].append(value)\n\n    def log_epoch(self, epoch: int, metrics: Dict[str, float]):\n        \"\"\"Log epoch-level metrics\"\"\"\n        if not self.current_phase:\n            self.logger.warning(\"No active phase. Call start_phase() first.\")\n            return\n\n        metrics_str = \" | \".join([f\"{k}: {v:.4f}\" for k, v in metrics.items()])\n        self.logger.info(f\"🔄 Epoch {epoch:3d} | {metrics_str}\")\n\n        # Save epoch metrics\n        epoch_file = self.experiment_dir / self.current_phase / f\"epoch_{epoch:04d}.json\"\n        with open(epoch_file, 'w') as f:\n            json.dump({\n                \"epoch\": epoch,\n                \"timestamp\": datetime.now().isoformat(),\n                \"metrics\": metrics\n            }, f, indent=2)\n\n    def log_model_info(self, model, model_name: str = \"model\"):\n        \"\"\"Log model information\"\"\"\n        try:\n            if hasattr(model, 'parameters'):\n                total_params = sum(p.numel() for p in model.parameters())\n                trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n\n                self.logger.info(f\"🤖 {model_name} Info:\")\n                self.logger.info(f\"   Total parameters: {total_params:,}\")\n                self.logger.info(f\"   Trainable parameters: {trainable_params:,}\")\n                self.logger.info(f\"   Model size: {total_params * 4 / 1024**2:.2f} MB\")\n\n                # Store in experiment data\n                self.experiment_data[f\"{model_name}_info\"] = {\n                    \"total_params\": total_params,\n                    \"trainable_params\": trainable_params,\n                    \"size_mb\": total_params * 4 / 1024**2\n                }\n        except Exception as e:\n            self.logger.warning(f\"Could not log model info: {e}\")\n\n    def log_hyperparameters(self, hyperparams: Dict[str, Any]):\n        \"\"\"Log hyperparameters\"\"\"\n        self.logger.info(\"⚙️ Hyperparameters:\")\n        for key, value in hyperparams.items():\n            self.logger.info(f\"   {key}: {value}\")\n\n        self.experiment_data[\"hyperparameters\"] = hyperparams\n\n        # Save hyperparameters to file\n        with open(self.experiment_dir / \"hyperparameters.json\", 'w') as f:\n            json.dump(hyperparams, f, indent=2)\n\n    def log_error(self, message: str, exception: Exception = None):\n        \"\"\"Log errors with full traceback\"\"\"\n        error_data = {\n            \"timestamp\": datetime.now().isoformat(),\n            \"message\": message,\n            \"phase\": self.current_phase\n        }\n\n        if exception:\n            error_data[\"exception\"] = str(exception)\n            error_data[\"traceback\"] = traceback.format_exc()\n            self.logger.error(f\"❌ {message}: {exception}\")\n            self.logger.error(f\"Traceback:\\n{traceback.format_exc()}\")\n        else:\n            self.logger.error(f\"❌ {message}\")\n\n        self.experiment_data[\"errors\"].append(error_data)\n\n    def save_checkpoint(self, model, optimizer, epoch: int, metrics: Dict[str, float],\n                       checkpoint_name: str = None):\n        \"\"\"Save model checkpoint with metadata\"\"\"\n        if checkpoint_name is None:\n            checkpoint_name = f\"checkpoint_epoch_{epoch:04d}.pt\"\n\n        checkpoint_path = self.experiment_dir / \"checkpoints\" / checkpoint_name\n\n        checkpoint_data = {\n            \"epoch\": epoch,\n            \"model_state_dict\": model.state_dict(),\n            \"optimizer_state_dict\": optimizer.state_dict() if optimizer else None,\n            \"metrics\": metrics,\n            \"timestamp\": datetime.now().isoformat(),\n            \"experiment_name\": self.experiment_name\n        }\n\n        torch.save(checkpoint_data, checkpoint_path)\n        self.logger.info(f\"💾 Checkpoint saved: {checkpoint_path}\")\n\n        # Save checkpoint metadata\n        metadata = {\n            \"checkpoint_name\": checkpoint_name,\n            \"epoch\": epoch,\n            \"metrics\": metrics,\n            \"timestamp\": datetime.now().isoformat(),\n            \"file_size_mb\": checkpoint_path.stat().st_size / 1024**2\n        }\n\n        metadata_path = self.experiment_dir / \"checkpoints\" / f\"{checkpoint_name}.meta.json\"\n        with open(metadata_path, 'w') as f:\n            json.dump(metadata, f, indent=2)\n\n    def _save_step_images(self, step: int, images: Dict[str, Any]):\n        \"\"\"Save step images\"\"\"\n        step_dir = self.experiment_dir / \"images\" / self.current_phase / f\"step_{step:06d}\"\n        step_dir.mkdir(parents=True, exist_ok=True)\n\n        for name, image in images.items():\n            if isinstance(image, torch.Tensor):\n                image = image.detach().cpu().numpy()\n\n            if isinstance(image, np.ndarray):\n                from PIL import Image\n                if image.ndim == 3 and image.shape[0] in [1, 3]:  # CHW format\n                    image = image.transpose(1, 2, 0)\n                if image.dtype != np.uint8:\n                    image = (image * 255).astype(np.uint8)\n\n                Image.fromarray(image).save(step_dir / f\"{name}.png\")\n\n    def _save_phase_summary(self, phase_name: str):\n        \"\"\"Save phase summary\"\"\"\n        summary_path = self.experiment_dir / phase_name / \"summary.json\"\n        phase_data = self.experiment_data[\"phases\"][phase_name]\n\n        with open(summary_path, 'w') as f:\n            json.dump(phase_data, f, indent=2)\n\n    def save_experiment(self):\n        \"\"\"Save complete experiment data\"\"\"\n        self.experiment_data[\"end_time\"] = datetime.now().isoformat()\n\n        experiment_file = self.experiment_dir / \"experiment.json\"\n        with open(experiment_file, 'w') as f:\n            json.dump(self.experiment_data, f, indent=2)\n\n        self.logger.info(f\"💾 Experiment data saved: {experiment_file}\")\n\n    def generate_report(self) -> str:\n        \"\"\"Generate a comprehensive experiment report\"\"\"\n        report = f\"\"\"\n# CtrlColor Experiment Report: {self.experiment_name}\n\n## Experiment Overview\n- **Start Time**: {self.experiment_data.get('start_time', 'N/A')}\n- **End Time**: {self.experiment_data.get('end_time', 'N/A')}\n- **Total Phases**: {len(self.experiment_data['phases'])}\n\n## System Information\n- **Python**: {self.experiment_data['system_info'].get('python_version', 'N/A')}\n- **PyTorch**: {self.experiment_data['system_info'].get('pytorch_version', 'N/A')}\n- **CUDA**: {self.experiment_data['system_info'].get('cuda_available', 'N/A')}\n- **GPUs**: {len(self.experiment_data['system_info'].get('gpu_names', []))}\n\n## Phase Summary\n\"\"\"\n\n        for phase_name, phase_data in self.experiment_data['phases'].items():\n            duration = phase_data.get('duration', 0)\n            steps = len(phase_data.get('steps', []))\n            report += f\"\"\"\n### {phase_name.title()} Phase\n- **Duration**: {duration:.2f} seconds\n- **Steps**: {steps}\n- **Metrics**: {list(phase_data.get('metrics', {}).keys())}\n\"\"\"\n\n        if self.experiment_data['errors']:\n            report += f\"\"\"\n## Errors ({len(self.experiment_data['errors'])})\n\"\"\"\n            for i, error in enumerate(self.experiment_data['errors'][:5]):  # Show first 5 errors\n                report += f\"- **Error {i+1}**: {error['message']}\\n\"\n\n        return report\n\n    def __enter__(self):\n        return self\n\n    def __exit__(self, exc_type, exc_val, exc_tb):\n        if exc_type:\n            self.log_error(\"Experiment ended with exception\", exc_val)\n        self.save_experiment()\n\n\n# Convenience functions for quick logging\ndef create_logger(experiment_name: str = None, log_dir: str = \"./logs\") -> CtrlColorLogger:\n    \"\"\"Create a new CtrlColor logger\"\"\"\n    return CtrlColorLogger(experiment_name=experiment_name, log_dir=log_dir)\n\ndef log_system_info():\n    \"\"\"Log system information\"\"\"\n    logger = create_logger(\"system_check\")\n    logger.logger.info(\"System information logged\")\n    logger.save_experiment()\n    return logger.experiment_data[\"system_info\"]\n"}