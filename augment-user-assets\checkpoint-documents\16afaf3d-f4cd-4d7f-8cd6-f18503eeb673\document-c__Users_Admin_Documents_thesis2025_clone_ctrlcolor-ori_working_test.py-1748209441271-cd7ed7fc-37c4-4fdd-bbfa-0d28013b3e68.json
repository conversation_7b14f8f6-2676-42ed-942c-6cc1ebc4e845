{"path": {"rootPath": "c:\\Users\\<USER>\\Documents\\thesis2025", "relPath": "clone/ctrlcolor-ori/working_test.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nWorking Test for CtrlColor\n==========================\n\nA working test script that uses safe model loading to avoid segmentation faults\nand provides comprehensive testing of CtrlColor functionality.\n\"\"\"\n\nimport os\nimport sys\nimport cv2\nimport numpy as np\nimport torch\nimport time\nfrom pathlib import Path\n\n# Set safe environment before any model imports\nos.environ['CUDA_LAUNCH_BLOCKING'] = '1'\nos.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'\n\ndef check_environment():\n    \"\"\"Check if environment is ready\"\"\"\n    print(\"🔧 Checking Environment...\")\n    \n    checks = {\n        \"CUDA available\": torch.cuda.is_available(),\n        \"GPU memory\": f\"{torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB\" if torch.cuda.is_available() else \"N/A\",\n        \"PyTorch version\": torch.__version__,\n        \"Main model exists\": Path(\"pretrained_models/main_model.ckpt\").exists(),\n        \"VAE model exists\": Path(\"pretrained_models/content-guided_deformable_vae.ckpt\").exists(),\n    }\n    \n    print(\"\\n📋 Environment Check:\")\n    for check, result in checks.items():\n        status = \"✅\" if (isinstance(result, bool) and result) or (isinstance(result, str) and result != \"N/A\") else \"❌\"\n        print(f\"  {status} {check}: {result}\")\n    \n    return all(isinstance(v, bool) and v for v in checks.values() if isinstance(v, bool))\n\ndef test_safe_model_loading():\n    \"\"\"Test safe model loading\"\"\"\n    print(\"\\n🤖 Testing Safe Model Loading...\")\n    \n    try:\n        # Import the safe test module\n        import test_safe\n        \n        print(\"✅ Safe test module imported\")\n        \n        # Test model loading\n        success = test_safe.test_model_loading()\n        \n        if success:\n            print(\"✅ All models loaded successfully\")\n            return True\n        else:\n            print(\"❌ Model loading failed\")\n            return False\n            \n    except Exception as e:\n        print(f\"❌ Safe model loading error: {e}\")\n        return False\n\ndef create_test_images():\n    \"\"\"Create test images for colorization\"\"\"\n    print(\"\\n🎨 Creating Test Images...\")\n    \n    # Create a simple test scene\n    img = np.ones((512, 512, 3), dtype=np.uint8) * 128\n    \n    # Add some geometric shapes\n    cv2.rectangle(img, (100, 100), (200, 200), (180, 180, 180), -1)  # Light square\n    cv2.circle(img, (350, 150), 50, (100, 100, 100), -1)  # Dark circle\n    cv2.rectangle(img, (150, 300), (400, 400), (160, 160, 160), -1)  # Medium rectangle\n    \n    # Create stroke image with colors\n    stroke_img = img.copy()\n    cv2.rectangle(stroke_img, (100, 100), (200, 200), (255, 0, 0), -1)  # Red square\n    cv2.circle(stroke_img, (350, 150), 50, (0, 255, 0), -1)  # Green circle\n    cv2.rectangle(stroke_img, (150, 300), (400, 400), (0, 0, 255), -1)  # Blue rectangle\n    \n    # Save test images\n    cv2.imwrite(\"working_test_input.png\", img)\n    cv2.imwrite(\"working_test_stroke.png\", stroke_img)\n    \n    print(\"✅ Test images created:\")\n    print(\"  • working_test_input.png - Grayscale input\")\n    print(\"  • working_test_stroke.png - Color stroke guidance\")\n    \n    return img, stroke_img\n\ndef test_basic_functions():\n    \"\"\"Test basic functions without full inference\"\"\"\n    print(\"\\n🧪 Testing Basic Functions...\")\n    \n    try:\n        import test_safe\n        \n        # Create test images\n        input_img, stroke_img = create_test_images()\n        \n        # Test basic functions\n        print(\"  • Testing grayscale detection... \", end=\"\")\n        is_gray = test_safe.is_gray_scale(input_img)\n        print(f\"✅ (is_gray: {is_gray})\")\n        \n        print(\"  • Testing mask generation... \", end=\"\")\n        mask = test_safe.get_mask(input_img, stroke_img)\n        print(f\"✅ (mask shape: {mask.shape})\")\n        \n        # Save mask for inspection\n        cv2.imwrite(\"working_test_mask.png\", mask)\n        print(\"  • Mask saved to working_test_mask.png\")\n        \n        return True\n        \n    except Exception as e:\n        print(f\"❌ Basic functions test failed: {e}\")\n        return False\n\ndef test_model_inference():\n    \"\"\"Test actual model inference\"\"\"\n    print(\"\\n🚀 Testing Model Inference...\")\n    \n    try:\n        import test_safe\n        from PIL import Image\n        \n        # Load test images\n        input_img = cv2.imread(\"working_test_input.png\")\n        stroke_img = cv2.imread(\"working_test_stroke.png\")\n        \n        if input_img is None or stroke_img is None:\n            print(\"❌ Could not load test images\")\n            return False\n        \n        print(\"  • Loading models... \", end=\"\")\n        model, ddim_sampler, BLIP_model, vis_processors, vae_model, device = test_safe.get_models()\n        print(\"✅\")\n        \n        print(\"  • Preparing inputs... \", end=\"\")\n        # Convert to RGB\n        input_img = cv2.cvtColor(input_img, cv2.COLOR_BGR2RGB)\n        stroke_img = cv2.cvtColor(stroke_img, cv2.COLOR_BGR2RGB)\n        \n        # Prepare mask and masked image\n        mask, masked_image = test_safe.prepare_mask_and_masked_image(\n            Image.fromarray(stroke_img), \n            Image.fromarray(test_safe.get_mask(input_img, stroke_img))\n        )\n        print(\"✅\")\n        \n        print(\"  • Encoding mask... \", end=\"\")\n        mask_encoded, masked_image_latents = test_safe.encode_mask(mask, masked_image)\n        print(\"✅\")\n        \n        print(\"  • Testing BLIP caption... \", end=\"\")\n        image_pil = Image.fromarray(input_img)\n        image_tensor = vis_processors[\"eval\"](image_pil).unsqueeze(0).to(device)\n        caption = BLIP_model.generate({\"image\": image_tensor})[0]\n        print(f\"✅ ('{caption}')\")\n        \n        print(\"✅ Model inference components working!\")\n        return True\n        \n    except Exception as e:\n        print(f\"❌ Model inference test failed: {e}\")\n        import traceback\n        traceback.print_exc()\n        return False\n\ndef test_memory_usage():\n    \"\"\"Test memory usage\"\"\"\n    print(\"\\n💾 Testing Memory Usage...\")\n    \n    if torch.cuda.is_available():\n        memory_before = torch.cuda.memory_allocated() / 1024**2\n        print(f\"  • Memory before: {memory_before:.1f} MB\")\n        \n        # Force garbage collection\n        import gc\n        gc.collect()\n        torch.cuda.empty_cache()\n        \n        memory_after = torch.cuda.memory_allocated() / 1024**2\n        print(f\"  • Memory after cleanup: {memory_after:.1f} MB\")\n        \n        max_memory = torch.cuda.max_memory_allocated() / 1024**2\n        print(f\"  • Peak memory usage: {max_memory:.1f} MB\")\n        \n        return True\n    else:\n        print(\"  • CUDA not available, skipping memory test\")\n        return False\n\ndef run_comprehensive_test():\n    \"\"\"Run comprehensive test suite\"\"\"\n    print(\"🎯 CtrlColor Working Test Suite\")\n    print(\"=\" * 50)\n    print(\"This test uses safe model loading to avoid segmentation faults.\\n\")\n    \n    tests = [\n        (\"Environment Check\", check_environment),\n        (\"Safe Model Loading\", test_safe_model_loading),\n        (\"Basic Functions\", test_basic_functions),\n        (\"Model Inference\", test_model_inference),\n        (\"Memory Usage\", test_memory_usage),\n    ]\n    \n    results = {}\n    start_time = time.time()\n    \n    for test_name, test_func in tests:\n        print(f\"\\n{'='*20} {test_name} {'='*20}\")\n        try:\n            results[test_name] = test_func()\n        except Exception as e:\n            print(f\"💥 {test_name} crashed: {e}\")\n            results[test_name] = False\n    \n    total_time = time.time() - start_time\n    \n    # Summary\n    print(\"\\n\" + \"=\" * 50)\n    print(\"🎯 COMPREHENSIVE TEST SUMMARY\")\n    print(\"=\" * 50)\n    \n    passed = sum(results.values())\n    total = len(results)\n    \n    for test_name, result in results.items():\n        status = \"✅ PASS\" if result else \"❌ FAIL\"\n        print(f\"  {status} {test_name}\")\n    \n    print(f\"\\n📊 Results: {passed}/{total} tests passed\")\n    print(f\"⏱️  Total time: {total_time:.2f} seconds\")\n    \n    if passed == total:\n        print(\"\\n🎉 ALL TESTS PASSED!\")\n        print(\"CtrlColor is working correctly with safe model loading.\")\n        print(\"\\n📁 Generated files:\")\n        print(\"  • working_test_input.png - Test input image\")\n        print(\"  • working_test_stroke.png - Test stroke image\")\n        print(\"  • working_test_mask.png - Generated mask\")\n        \n        print(\"\\n🚀 Next steps:\")\n        print(\"  • Try running full inference with the process function\")\n        print(\"  • Test different prompts and stroke patterns\")\n        print(\"  • Run the comprehensive test suite\")\n        \n    else:\n        print(f\"\\n⚠️  {total - passed} tests failed.\")\n        print(\"Check the error messages above for details.\")\n        \n        print(\"\\n🔧 Troubleshooting:\")\n        if not results.get(\"Environment Check\", False):\n            print(\"  • Check CUDA installation and model files\")\n        if not results.get(\"Safe Model Loading\", False):\n            print(\"  • Model loading issue - check GPU memory\")\n        if not results.get(\"Model Inference\", False):\n            print(\"  • Inference issue - check model compatibility\")\n\ndef main():\n    \"\"\"Main function\"\"\"\n    run_comprehensive_test()\n\nif __name__ == \"__main__\":\n    main()\n"}