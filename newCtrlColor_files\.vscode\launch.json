{"version": "0.2.0", "configurations": [{"name": "Python: Current File (CtrlColor)", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": true, "python": "C:\\Users\\<USER>\\anaconda3\\envs\\CtrlColor\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Debug utest.py (CtrlColor)", "type": "python", "request": "launch", "program": "${workspaceFolder}/utest.py", "console": "integratedTerminal", "justMyCode": false, "python": "C:\\Users\\<USER>\\anaconda3\\envs\\CtrlColor\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Debug ExemplarCLDM Test", "type": "python", "request": "launch", "program": "${workspaceFolder}/cldm/exemplar_cldm.py", "console": "integratedTerminal", "justMyCode": false, "python": "C:\\Users\\<USER>\\anaconda3\\envs\\CtrlColor\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": []}]}