# CtrlColor Bug Fixing Guide

## Overview
This guide documents the recurring bugs and their solutions in the CtrlColor codebase. Use this as a reference when debugging the ExemplarControlLDM implementation.

## Critical Tensor Format Requirements

### 1. Input Data Format Inconsistencies

**Problem**: Different components expect different tensor formats, causing dimension swapping errors.

**Key Rules**:
- `get_input()` method expects `(b, h, w, c)` format and rearranges to `(b, c, h, w)`
- Autoencoder operations expect `(b, c, h, w)` format directly
- Mask operations expect `(b, c, h, w)` format for interpolation
- Exemplar encoder expects `(b, c, h, w)` format

**Test Batch Format**:
```python
test_batch = {
    "image": torch.randn(batch_size, 256, 256, 3),      # (b, h, w, c) for get_input
    "jpg": torch.randn(batch_size, 256, 256, 3),        # (b, h, w, c) for get_input
    "hint": torch.randn(batch_size, 256, 256, 3),       # (b, h, w, c) for get_input
    "exemplar": torch.randn(batch_size, 3, 256, 256),   # (b, c, h, w) for exemplar encoder
    "mask": torch.ones(batch_size, 1, 256, 256),        # (b, c, h, w) for interpolation
    "mask_img": torch.randn(batch_size, 3, 256, 256),   # (b, c, h, w) for autoencoder
    "txt": [("red car", torch.randn(batch_size, 256, 256, 3))],  # Tuple format
}
```

## Common Error Patterns

### 1. Dimension Swapping Error
```
RuntimeError: Given groups=1, weight of size [128, 3, 3, 3],
expected input[1, 512, 3, 512] to have 3 channels, but got 512 channels instead
```

**Cause**: Tensor has wrong dimensions - channels and height are swapped
**Solution**: Check if tensor is in correct format for the operation

### 2. Text Conditioning Error
```
ValueError: not enough values to unpack (expected 2, got 1)
```

**Cause**: Text encoder expects tuple `(txt, x)` but receiving single string
**Solution**: Provide text as tuple: `("text", image_tensor)`

### 3. Missing Keys Error
```
KeyError: 'jpg'
```

**Cause**: Training step expects specific keys in batch
**Solution**: Ensure all required keys are present in test batch

### 4. Interpolation Error
```
RuntimeError: Input and output sizes should be greater than 0,
but got input (H: 256, W: 1) output (H: 32, W: 0)
```

**Cause**: Mask tensor has wrong dimensions for interpolation
**Solution**: Use `(b, c, h, w)` format for mask tensors

## Debugging Workflow

### Step 1: Read the Error
1. Look at the error traceback
2. Identify which component is failing
3. Check what tensor format it expects

### Step 2: Check Tensor Shapes
```python
# Add debug prints to see actual tensor shapes
print(f"Tensor shape: {tensor.shape}")
print(f"Expected format: (batch, channels, height, width)")
```

### Step 3: Fix Format Issues
- For `get_input()` operations: Use `(b, h, w, c)` format
- For autoencoder operations: Use `(b, c, h, w)` format
- For text conditioning: Use tuple `(text, image_tensor)` format

### Step 4: Test Incrementally
```python
# Test each component separately
exemplar_encoding = model.encode_exemplar(exemplar_tensor)
conditioning = model.get_exemplar_conditioning(exemplar_encoding, text_cond)
loss = model.training_step(test_batch, batch_idx=0)
```

## Quick Fixes Reference

### Fix 1: Tensor Format
```python
# Wrong
tensor = torch.randn(1, 3, 256, 256)  # For get_input operations

# Right
tensor = torch.randn(1, 256, 256, 3)  # For get_input operations
```

### Fix 2: Text Format
```python
# Wrong
"txt": ["red car"]

# Right
"txt": [("red car", torch.randn(1, 256, 256, 3))]
```

### Fix 3: Complete Test Batch
```python
test_batch = {
    "image": torch.randn(1, 256, 256, 3).to(device),
    "txt": [("red car", torch.randn(1, 256, 256, 3).to(device))],
    "hint": torch.randn(1, 256, 256, 3).to(device),
    "exemplar": torch.randn(1, 3, 256, 256).to(device),
    "jpg": torch.randn(1, 256, 256, 3).to(device),
    "mask": torch.ones(1, 1, 256, 256).to(device),
    "mask_img": torch.randn(1, 3, 256, 256).to(device),
}
```

## Testing Commands

```bash
# Test full pipeline
python -c "from cldm.exemplar_cldm import test_full_exemplar_cldm; test_full_exemplar_cldm()"

# Test individual components
python -c "from cldm.exemplar_cldm import test_exemplar_encoder; test_exemplar_encoder()"
```

## Memory Updates
When you encounter new bugs, update this guide and your memories with:
1. The specific error pattern
2. The root cause
3. The exact solution
4. The tensor format requirements

This prevents repeating the same debugging cycles!
