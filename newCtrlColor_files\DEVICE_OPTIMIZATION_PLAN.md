# CtrlColor Device Selection & Performance Optimization Plan

## 🔍 **Current Device Selection Analysis**

### **Where Device Selection Happens:**

#### **1. Main Test Script (`test.py`)**
```python
# Line 24-26: Model loading
model = create_model('./models/cldm_v15_inpainting_infer1.yaml').cpu()
model.load_state_dict(load_state_dict(ckpt_path, location='cuda'), strict=False)
model = model.cuda()

# Line 31: BLIP model device selection
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
BLIP_model, vis_processors, _ = load_model_and_preprocess(name="blip_caption", model_type="base_coco", is_eval=True, device=device)

# Line 63-64: VAE model loading
vae.load_state_dict(load_state_dict(vae_model_ckpt_path, location='cuda'))
vae = vae.cuda()

# Line 72: Mask encoding
mask = mask.to(device="cuda")

# Line 74: Image latents
masked_image_latents = model.get_first_stage_encoding(model.encode_first_stage(masked_image.cuda())).detach()
```

#### **2. Model Loading (`cldm/model.py`)**
```python
# Line 12: Checkpoint loading with device specification
def load_state_dict(ckpt_path, location='cpu'):
    state_dict = get_state_dict(torch.load(ckpt_path, map_location=torch.device(location)))

# Line 26: Model creation (defaults to CPU)
model = instantiate_from_config(config.model).cpu()
```

#### **3. Our Implementation (`full/` directory)**
```python
# Consistent pattern across all test functions:
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
```

### **Current Issues:**

1. **❌ Hardcoded CUDA Usage**: Many places force `.cuda()` without checking availability
2. **❌ Mixed Device Strategies**: Some use auto-detection, others hardcode
3. **❌ No Memory Management**: No consideration for GPU memory limits
4. **❌ No Multi-GPU Support**: Single GPU only
5. **❌ No Performance Monitoring**: No device utilization tracking

---

## 🎯 **Optimization Strategy**

### **Phase 1: Smart Device Detection & Selection**

#### **1.1 Create Centralized Device Manager**
```python
# device_manager.py
class DeviceManager:
    def __init__(self):
        self.available_devices = self._detect_devices()
        self.primary_device = self._select_primary_device()
        self.memory_threshold = 0.8  # 80% memory usage threshold
    
    def _detect_devices(self):
        devices = {'cpu': torch.device('cpu')}
        
        # CUDA detection
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                devices[f'cuda:{i}'] = torch.device(f'cuda:{i}')
        
        # MPS detection (Apple Silicon)
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            devices['mps'] = torch.device('mps')
        
        return devices
    
    def _select_primary_device(self):
        # Priority: CUDA > MPS > CPU
        if 'cuda:0' in self.available_devices:
            return self.available_devices['cuda:0']
        elif 'mps' in self.available_devices:
            return self.available_devices['mps']
        else:
            return self.available_devices['cpu']
    
    def get_optimal_device(self, memory_required_gb=None):
        """Get optimal device based on memory requirements"""
        if memory_required_gb is None:
            return self.primary_device
        
        # Check GPU memory availability
        for device_name, device in self.available_devices.items():
            if device.type == 'cuda':
                gpu_id = int(device_name.split(':')[1]) if ':' in device_name else 0
                memory_free = torch.cuda.get_device_properties(gpu_id).total_memory - torch.cuda.memory_allocated(gpu_id)
                memory_free_gb = memory_free / (1024**3)
                
                if memory_free_gb >= memory_required_gb:
                    return device
        
        return self.available_devices['cpu']  # Fallback to CPU
```

#### **1.2 Memory-Aware Model Loading**
```python
def smart_model_loading(model_path, device_manager):
    """Load model with optimal device selection"""
    
    # Estimate model size
    model_size_gb = estimate_model_size(model_path)
    
    # Get optimal device
    device = device_manager.get_optimal_device(model_size_gb * 1.5)  # 50% buffer
    
    # Load with appropriate strategy
    if device.type == 'cuda':
        return load_model_cuda_optimized(model_path, device)
    elif device.type == 'mps':
        return load_model_mps_optimized(model_path, device)
    else:
        return load_model_cpu_optimized(model_path, device)
```

### **Phase 2: Multi-GPU & Memory Optimization**

#### **2.1 Multi-GPU Support**
```python
class MultiGPUManager:
    def __init__(self):
        self.gpu_count = torch.cuda.device_count()
        self.gpu_memory = [torch.cuda.get_device_properties(i).total_memory 
                          for i in range(self.gpu_count)]
    
    def distribute_models(self, models):
        """Distribute models across available GPUs"""
        if self.gpu_count <= 1:
            return models
        
        # Strategy 1: Main model on GPU 0, VAE on GPU 1
        if len(models) >= 2 and self.gpu_count >= 2:
            models['main'].to('cuda:0')
            models['vae'].to('cuda:1')
            models['blip'].to('cuda:0')  # Share with main model
        
        return models
    
    def parallel_inference(self, batch, models):
        """Run inference with model parallelism"""
        # Split batch across GPUs if beneficial
        if batch.size(0) >= self.gpu_count * 2:
            return self._batch_parallel_inference(batch, models)
        else:
            return self._model_parallel_inference(batch, models)
```

#### **2.2 Dynamic Memory Management**
```python
class MemoryManager:
    def __init__(self, device_manager):
        self.device_manager = device_manager
        self.memory_cache = {}
    
    def optimize_memory_usage(self):
        """Optimize memory usage based on current state"""
        if torch.cuda.is_available():
            # Clear cache if memory usage > 80%
            for i in range(torch.cuda.device_count()):
                memory_used = torch.cuda.memory_allocated(i)
                memory_total = torch.cuda.get_device_properties(i).total_memory
                
                if memory_used / memory_total > 0.8:
                    torch.cuda.empty_cache()
                    print(f"Cleared CUDA cache on GPU {i}")
    
    def adaptive_batch_size(self, base_batch_size, model_size):
        """Adapt batch size based on available memory"""
        if torch.cuda.is_available():
            available_memory = torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)
            memory_per_sample = model_size / base_batch_size
            
            optimal_batch_size = min(base_batch_size, int(available_memory * 0.7 / memory_per_sample))
            return max(1, optimal_batch_size)
        
        return base_batch_size
```

### **Phase 3: Performance Monitoring & Auto-Tuning**

#### **3.1 Performance Monitor**
```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'inference_times': [],
            'memory_usage': [],
            'gpu_utilization': [],
            'batch_sizes': []
        }
    
    def monitor_inference(self, func):
        """Decorator to monitor inference performance"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # Monitor memory before
            if torch.cuda.is_available():
                memory_before = torch.cuda.memory_allocated()
            
            result = func(*args, **kwargs)
            
            # Monitor memory after
            if torch.cuda.is_available():
                memory_after = torch.cuda.memory_allocated()
                self.metrics['memory_usage'].append(memory_after - memory_before)
            
            inference_time = time.time() - start_time
            self.metrics['inference_times'].append(inference_time)
            
            return result
        return wrapper
    
    def get_optimization_recommendations(self):
        """Provide optimization recommendations based on metrics"""
        recommendations = []
        
        if len(self.metrics['inference_times']) > 10:
            avg_time = np.mean(self.metrics['inference_times'][-10:])
            
            if avg_time > 5.0:  # Slow inference
                recommendations.append("Consider using mixed precision (FP16)")
                recommendations.append("Try increasing batch size if memory allows")
            
            if torch.cuda.is_available():
                avg_memory = np.mean(self.metrics['memory_usage'][-10:])
                total_memory = torch.cuda.get_device_properties(0).total_memory
                
                if avg_memory / total_memory < 0.5:
                    recommendations.append("GPU memory underutilized - can increase batch size")
                elif avg_memory / total_memory > 0.9:
                    recommendations.append("GPU memory near limit - consider reducing batch size")
        
        return recommendations
```

---

## 🚀 **Implementation Plan**

### **Step 1: Create Device Management System**
```bash
# Create new device optimization module
mkdir clone/newCtrlColor/device_optimization
touch clone/newCtrlColor/device_optimization/__init__.py
touch clone/newCtrlColor/device_optimization/device_manager.py
touch clone/newCtrlColor/device_optimization/memory_manager.py
touch clone/newCtrlColor/device_optimization/performance_monitor.py
```

### **Step 2: Update Existing Code**
1. **Replace hardcoded `.cuda()` calls** with device manager
2. **Add memory monitoring** to main inference loop
3. **Implement adaptive batch sizing** based on available memory
4. **Add multi-GPU support** for model distribution

### **Step 3: Configuration-Based Device Selection**
```yaml
# device_config.yaml
device_strategy:
  auto_detect: true
  preferred_device: "auto"  # auto, cuda, mps, cpu
  memory_threshold: 0.8
  enable_multi_gpu: true
  enable_mixed_precision: true
  adaptive_batch_size: true

performance_optimization:
  enable_monitoring: true
  cache_management: "aggressive"  # conservative, balanced, aggressive
  memory_optimization: true
```

### **Step 4: Benchmarking & Validation**
```python
# benchmark_devices.py
def benchmark_all_configurations():
    """Benchmark different device configurations"""
    configurations = [
        {'device': 'cuda:0', 'precision': 'fp32', 'batch_size': 1},
        {'device': 'cuda:0', 'precision': 'fp16', 'batch_size': 1},
        {'device': 'cuda:0', 'precision': 'fp32', 'batch_size': 4},
        {'device': 'cpu', 'precision': 'fp32', 'batch_size': 1},
    ]
    
    results = {}
    for config in configurations:
        results[str(config)] = run_benchmark(config)
    
    return results
```

---

## 📊 **Expected Performance Improvements**

| Optimization | Expected Speedup | Memory Reduction | Compatibility |
|--------------|------------------|------------------|---------------|
| **Smart Device Selection** | 1.2x | 10% | Universal |
| **Multi-GPU Distribution** | 1.5-2x | 20% | Multi-GPU systems |
| **Mixed Precision (FP16)** | 1.5-2x | 50% | Modern GPUs |
| **Adaptive Batch Sizing** | 1.3x | 15% | All devices |
| **Memory Management** | 1.1x | 25% | GPU systems |

---

## 🎯 **Quick Implementation Priority**

### **High Priority (Immediate)**
1. ✅ **Replace hardcoded `.cuda()`** with device detection
2. ✅ **Add memory monitoring** to prevent OOM errors
3. ✅ **Implement fallback strategies** for different devices

### **Medium Priority (Next Week)**
4. ✅ **Multi-GPU support** for users with multiple GPUs
5. ✅ **Mixed precision training** for 2x speedup
6. ✅ **Adaptive batch sizing** for optimal memory usage

### **Low Priority (Future)**
7. ✅ **Performance profiling** and auto-tuning
8. ✅ **Cloud deployment optimization**
9. ✅ **Mobile/edge device support**

This plan will transform CtrlColor from a hardcoded single-GPU system to an intelligent, adaptive, multi-device optimized framework! 🚀

---

# Optimal CtrlColor settings for RTX 3050
```
DEVICE_CONFIG = {
    'device': 'cuda',
    'precision': 'fp16',           # 50% memory savings
    'batch_size_inference': 2,     # Safe for inference
    'batch_size_training': 8,      # Optimal for training
    'max_image_size': 512,         # Conservative for inference
    'max_image_size_training': 768, # Maximum for training
    'memory_fraction': 0.85,       # 85% of 4.3GB
    'enable_cudnn_benchmark': True,
    'gradient_checkpointing': True  # For training
}
```