# CtrlColor Implementation Testing Guide

## 🧪 **Step-by-Step Testing Instructions**

This guide provides commands to test each implemented component when the project is ready to run.

---

## 📋 **Prerequisites**

Before testing, ensure you have:
```bash
# Install required packages
pip install torch torchvision torchaudio
pip install opencv-python scikit-image
pip install lpips torchmetrics
pip install gradio transformers diffusers
pip install numpy matplotlib pillow

# Verify PyTorch installation
python -c "import torch; print('PyTorch version:', torch.__version__); print('CUDA available:', torch.cuda.is_available())"
```

---

## 🔴 **Phase 1: Exemplar Pipeline Foundation Tests**

### **Week 1-2: Loss Functions**

#### **Test 1: VGG19 Contextual Loss**
```bash
cd clone/newCtrlColor
python -c "
from ldm.modules.losses.contextual_loss import test_contextual_loss
print('=== Testing VGG19 Contextual Loss ===')
print('Implements: $$\mathcal{L}_{\text{context}} = \sum_{l \in \{3,5\}} w_l \left[-\log\left(\frac{1}{N_l} \sum_i \max_j A^l(i,j)\right)\right]$$')
test_contextual_loss()
"
```

**Expected Output:**
```
Testing on device: cuda/cpu
Implements: $$\mathcal{L}_{\text{context}} = \sum_{l \in \{3,5\}} w_l \left[-\log\left(\frac{1}{N_l} \sum_i \max_j A^l(i,j)\right)\right]$$
Contextual loss: 0.XXXXXX
Loss requires_grad: True/False
Exemplar grad norm: 0.XXXXXX
Generated grad norm: 0.XXXXXX
✅ Contextual loss test passed!
```

#### **Test 2: Grayscale Consistency Loss**
```bash
python -c "
from ldm.modules.losses.grayscale_loss import test_grayscale_loss
print('=== Testing Grayscale Consistency Loss ===')
print('Implements: $$\mathcal{L}_{\text{gray}} = \left\|\frac{\sum_{c \in \{R,G,B\}} I_i^c}{3} - \frac{\sum_{c \in \{R,G,B\}} I_g^c}{3}\right\|_2$$')
test_grayscale_loss()
"
```

**Expected Output:**
```
Testing on device: cuda/cpu
Implements: $$\mathcal{L}_{\text{gray}} = \left\|\frac{\sum_{c \in \{R,G,B\}} I_i^c}{3} - \frac{\sum_{c \in \{R,G,B\}} I_g^c}{3}\right\|_2$$
Testing basic grayscale loss...
Basic grayscale loss: 0.XXXXXX
Testing perceptual grayscale loss...
Perceptual grayscale loss: 0.XXXXXX
Input grad norm: 0.XXXXXX
Generated grad norm: 0.XXXXXX
✅ Grayscale loss test passed!
```

#### **Test 3: Combined Exemplar Loss**
```bash
python -c "
from ldm.modules.losses.exemplar_loss import test_exemplar_loss
print('=== Testing Combined Exemplar Loss ===')
print('Implements: $$\mathcal{L}_{\text{exemplar}} = \mathcal{L}_{\text{context}} + w_e \cdot \mathcal{L}_{\text{gray}}$$ where $w_e = 1000$')
test_exemplar_loss()
"
```

**Expected Output:**
```
Testing on device: cuda/cpu
Implements: $$\mathcal{L}_{\text{exemplar}} = \mathcal{L}_{\text{context}} + w_e \cdot \mathcal{L}_{\text{gray}}$$ where $w_e = 1000$
Testing basic exemplar loss...
Total exemplar loss: 0.XXXXXX
Contextual loss: 0.XXXXXX
Grayscale loss: 0.XXXXXX
Weighted grayscale loss: 0.XXXXXX

Testing adaptive exemplar loss...
Step 0: w_e = 500.0
Step 250: w_e = 625.0
Step 500: w_e = 750.0
Step 750: w_e = 875.0
Step 1000: w_e = 1000.0
Step 1500: w_e = 1000.0

Gradient norms:
Generated grad norm: 0.XXXXXX
Exemplar grad norm: 0.XXXXXX
Input grad norm: 0.XXXXXX
✅ Exemplar loss test passed!
```

#### **Test 4: Loss Integration**
```bash
python -c "
from ldm.modules.losses import ExemplarLoss, VGG19ContextualLoss, GrayscaleConsistencyLoss
import torch

print('=== Testing Loss Module Integration ===')
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Test individual imports
contextual = VGG19ContextualLoss().to(device)
grayscale = GrayscaleConsistencyLoss().to(device)
exemplar = ExemplarLoss().to(device)

print('✅ All loss modules imported successfully!')
print(f'Contextual loss layers: {contextual.layers}')
print(f'Exemplar loss weights: {exemplar.get_loss_weights()}')
"
```

**Expected Output:**
```
=== Testing Loss Module Integration ===
✅ All loss modules imported successfully!
Contextual loss layers: [3, 5]
Exemplar loss weights: {'contextual_weight': 1.0, 'grayscale_weight': 1000.0}
```

---

## 🟡 **Phase 2: CLIP Integration Tests (Week 3)**

### **Test 5: CLIP Image Encoder**
```bash
python -c "
from ldm.modules.encoders.exemplar_encoder import test_clip_encoder
print('=== Testing CLIP Image Encoder ===')
test_clip_encoder()
"
```

**Expected Output:**
```
=== Testing CLIP Image Encoder ===
Testing CLIP exemplar encoder on device: cuda
Testing exemplar encoder...
Features shape: torch.Size([2, 768])
Pooled features shape: torch.Size([2, 768])
Sequence features shape: torch.Size([2, 50, 768])
Color palette shape: torch.Size([2, 16, 3])
Testing exemplar-text fusion...
Fused features shape: torch.Size([2, 77, 768])
Exemplar grad norm: 0.009512
CLIP exemplar encoder test passed!
```

### **Test 6: Exemplar Processing Pipeline**
```bash
python -c "
from cldm.exemplar_cldm import test_exemplar_pipeline
print('=== Testing Exemplar Processing Pipeline ===')
test_exemplar_pipeline()
"
```

**Expected Output:**
```
=== Testing Exemplar Processing Pipeline ===
Testing exemplar pipeline on device: cuda
Testing exemplar encoder component...
Exemplar features shape: torch.Size([2, 768])
Pooled features shape: torch.Size([2, 768])
Color palette shape: torch.Size([2, 16, 3])
Testing exemplar-text fusion...
Fused features shape: torch.Size([2, 77, 768])
Exemplar pipeline test passed!
```

### **Test 6.5: Full ExemplarControlLDM Initialization**

Test the complete ExemplarControlLDM with actual model configs:

```bash
python -c "
from cldm.exemplar_cldm import test_full_exemplar_cldm
print('=== Testing Full ExemplarControlLDM ===')
test_full_exemplar_cldm()
"
```

**Expected Output:**
```
=== Testing Full ExemplarControlLDM Initialization ===
Testing on device: cuda
Loaded config from: ./models/exemplar_cldm_v15.yaml
Initializing full ExemplarControlLDM...
ExemplarControlLDM initialized successfully!
Testing exemplar encoding...
Exemplar features shape: torch.Size([1, 768])
Testing exemplar conditioning...
Exemplar conditioning shape: torch.Size([1, 77, 768])
Testing training step...
Training loss keys: ['loss', 'exemplar_loss', 'contextual_loss', 'grayscale_loss']
Exemplar loss: 125.678901
Full ExemplarControlLDM test passed!
```

### **Test 6.6: Complete Exemplar Pipeline (Both Tests)**

Run both component and full model tests together:

```bash
python -c "
from cldm.exemplar_cldm import test_exemplar_pipeline, test_full_exemplar_cldm

print('Running component test...')
component_success = test_exemplar_pipeline()

print('\n' + '='*60)

print('Running full model test...')
full_success = test_full_exemplar_cldm()

print('\n' + '='*60)
print('TEST SUMMARY:')
print(f'Component test: {\"PASSED\" if component_success else \"FAILED\"}')
print(f'Full model test: {\"PASSED\" if full_success else \"FAILED\"}')
"
```

**Expected Output:**
```
Running component test...
=== Testing Exemplar Processing Pipeline ===
Testing exemplar pipeline on device: cuda
Testing exemplar encoder component...
Exemplar features shape: torch.Size([2, 768])
Pooled features shape: torch.Size([2, 768])
Color palette shape: torch.Size([2, 16, 3])
Testing exemplar-text fusion...
Fused features shape: torch.Size([2, 77, 768])
Exemplar pipeline test passed!

============================================================

=== Testing Full ExemplarControlLDM Initialization ===
Testing on device: cuda
Loaded config from: ./models/exemplar_cldm_v15.yaml
Initializing full ExemplarControlLDM...
ExemplarControlLDM initialized successfully!
Testing exemplar encoding...
Exemplar features shape: torch.Size([1, 768])
Testing exemplar conditioning...
Exemplar conditioning shape: torch.Size([1, 77, 768])
Testing training step...
Training loss keys: ['loss', 'exemplar_loss', 'contextual_loss', 'grayscale_loss']
Exemplar loss: 125.678901
Full ExemplarControlLDM test passed!

============================================================
TEST SUMMARY:
Component test: PASSED
Full model test: PASSED
```

---

## 🟢 **Phase 3: UI Integration Tests (Week 4)**

### **Test 7: Exemplar Input Interface**
```bash
# Test that exemplar UI components are properly integrated
python -c "
import gradio as gr
print('=== Testing Exemplar UI Integration ===')

# Check if exemplar components are accessible in test.py
try:
    import test
    print('✅ test.py imports successfully')
    print('✅ Exemplar UI components should be visible in Gradio interface')
    print('✅ Check for \"Exemplar-based Colorization\" accordion in UI')
except Exception as e:
    print(f'❌ UI integration test failed: {e}')
"
```

### **Test 8: Full 4-Mode Colorization**
```bash
# Test all conditioning modes with sample images
python -c "
print('=== Testing All 4 Conditioning Modes ===')
print('1. Unconditional: Run with empty prompt and no strokes')
print('2. Text-guided: Run with text prompt only')
print('3. Stroke-based: Run with color strokes only')
print('4. Exemplar-based: Run with exemplar image and use_exemplar=True')
print('Manual testing required through Gradio interface')
"
```

---

## 📊 **Phase 4: Evaluation Infrastructure Tests (Week 5-6)**

### **Test 9: FID Metric** (To be implemented)
```bash
python -c "
from evaluation.metrics import test_fid_metric
print('=== Testing FID Metric ===')
test_fid_metric()
"
```

### **Test 10: LPIPS Metric** (To be implemented)
```bash
python -c "
from evaluation.metrics import test_lpips_metric
print('=== Testing LPIPS Metric ===')
test_lpips_metric()
"
```

### **Test 11: Colorfulness Metric** (To be implemented)
```bash
python -c "
from evaluation.metrics import test_colorfulness_metric
print('=== Testing Colorfulness Metric ===')
test_colorfulness_metric()
"
```

### **Test 12: Complete Evaluation Suite** (To be implemented)
```bash
python -c "
from evaluation.evaluate_model import run_evaluation_suite
print('=== Running Complete Evaluation Suite ===')
run_evaluation_suite()
"
```

---

## 🔧 **Phase 5: Data Processing Tests (Week 7-8)**

### **Test 13: SLIC Superpixel Generation** (To be implemented)
```bash
python -c "
from data.slic_processor import test_slic_generation
print('=== Testing SLIC Superpixel Generation ===')
test_slic_generation()
"
```

### **Test 14: Color Jittering** (To be implemented)
```bash
python -c "
from data.color_jittering import test_color_jittering
print('=== Testing Color Jittering ===')
test_color_jittering()
"
```

### **Test 15: Training Data Pipeline** (To be implemented)
```bash
python -c "
from data.training_data_processor import test_data_pipeline
print('=== Testing Training Data Pipeline ===')
test_data_pipeline()
"
```

---

## 🚀 **Integration Tests**

### **Test 16: End-to-End Exemplar Colorization**
```bash
python test.py \
  --input_image "test_images/grayscale_image.jpg" \
  --exemplar_image "test_images/color_exemplar.jpg" \
  --prompt "colorful image" \
  --mode "exemplar" \
  --output_dir "test_outputs/"
```

### **Test 17: Quantitative Evaluation**
```bash
python -c "
from evaluation.reproduce_paper_results import reproduce_table1
print('=== Reproducing Paper Table 1 Results ===')
reproduce_table1()
"
```

### **Test 18: Training Pipeline** (To be implemented)
```bash
python training/train_exemplar_stage.py \
  --config configs/exemplar_training.yaml \
  --data_dir "datasets/imagenet_subset/" \
  --output_dir "checkpoints/exemplar_stage/"
```

---

## 🔍 **Debugging Commands**

### **Check Model Loading**
```bash
python -c "
import torch
from cldm.cldm import ControlLDM

print('=== Testing Model Loading ===')
try:
    model = ControlLDM.from_pretrained('pretrained_models/main_model.ckpt')
    print('✅ Main model loaded successfully')
except Exception as e:
    print(f'❌ Model loading failed: {e}')
"
```

### **Check GPU Memory Usage**
```bash
python -c "
import torch
if torch.cuda.is_available():
    print(f'GPU: {torch.cuda.get_device_name()}')
    print(f'Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')
    print(f'Current usage: {torch.cuda.memory_allocated() / 1e9:.3f} GB')
else:
    print('CUDA not available')
"
```

### **Verify Dependencies**
```bash
python -c "
import sys
required_packages = [
    'torch', 'torchvision', 'transformers', 'diffusers',
    'opencv-python', 'scikit-image', 'lpips', 'torchmetrics',
    'gradio', 'numpy', 'matplotlib', 'pillow'
]

print('=== Checking Dependencies ===')
for package in required_packages:
    try:
        __import__(package.replace('-', '_'))
        print(f'✅ {package}')
    except ImportError:
        print(f'❌ {package} - MISSING')
"
```

---

## 📝 **Test Results Documentation**

Create a test results file:
```bash
# Run all tests and save results
python -c "
import datetime
print(f'CtrlColor Test Results - {datetime.datetime.now()}')
print('=' * 50)
" > test_results.txt

# Append each test result to the file
# Example: python test_command >> test_results.txt 2>&1
```

---

## 🎯 **Success Criteria**

### **Phase 1 Success (Week 1-2):**
- ✅ All loss function tests pass
- ✅ Gradients computed correctly
- ✅ No import errors

### **Phase 2 Success (Week 3):**
- ✅ CLIP encoder processes exemplar images
- ✅ Exemplar features integrated into diffusion pipeline

### **Phase 3 Success (Week 4):**
- ✅ Exemplar input working in UI
- ✅ All 4 conditioning modes functional

### **Phase 4 Success (Week 5-6):**
- ✅ All evaluation metrics implemented
- ✅ Can reproduce at least one paper table

### **Phase 5 Success (Week 7-8):**
- ✅ SLIC superpixel generation working
- ✅ Training data preprocessing complete

This testing guide ensures systematic validation of each component as it's implemented!
