#!/usr/bin/env python3
"""
Debug script for ExemplarControlLDM
Usage: python debug_exemplar.py
"""

import pdb
import torch
from cldm.exemplar_cldm import test_full_exemplar_cldm

def debug_exemplar():
    """Debug the exemplar CLDM with breakpoints"""
    print("Starting ExemplarControlLDM debug session...")
    
    # Set breakpoint here
    pdb.set_trace()
    
    # This will stop at the breakpoint above
    test_full_exemplar_cldm()

if __name__ == "__main__":
    debug_exemplar()
