"""
CUDA Diagnostic Tool for CtrlColor

Comprehensive diagnostic to identify why CUDA is not being detected
and provide solutions for optimal device scheduling.
"""

import torch
import sys
import os
import subprocess
import platform
from typing import Dict, List, Any


def check_cuda_installation():
    """Check CUDA installation and drivers"""
    print("🔍 CUDA INSTALLATION DIAGNOSTIC")
    print("=" * 50)
    
    # Check PyTorch CUDA support
    print(f"PyTorch version: {torch.__version__}")
    print(f"PyTorch CUDA available: {torch.cuda.is_available()}")
    print(f"PyTorch CUDA version: {torch.version.cuda}")
    
    if torch.cuda.is_available():
        print(f"CUDA device count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"  GPU {i}: {props.name}")
            print(f"    Memory: {props.total_memory / 1e9:.1f} GB")
            print(f"    Compute Capability: {props.major}.{props.minor}")
    else:
        print("❌ CUDA not available in PyTorch")
    
    # Check NVIDIA driver
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("\n✅ NVIDIA driver detected:")
            lines = result.stdout.split('\n')
            for line in lines[:10]:  # Show first 10 lines
                if line.strip():
                    print(f"  {line}")
        else:
            print("\n❌ nvidia-smi command failed")
    except FileNotFoundError:
        print("\n❌ nvidia-smi not found - NVIDIA drivers may not be installed")
    
    # Check CUDA toolkit
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"\n✅ CUDA toolkit detected:")
            print(f"  {result.stdout.strip()}")
        else:
            print("\n❌ nvcc command failed")
    except FileNotFoundError:
        print("\n❌ nvcc not found - CUDA toolkit may not be installed")


def check_environment_variables():
    """Check CUDA-related environment variables"""
    print("\n🌍 ENVIRONMENT VARIABLES")
    print("=" * 50)
    
    cuda_vars = [
        'CUDA_HOME', 'CUDA_PATH', 'CUDA_ROOT',
        'CUDA_VISIBLE_DEVICES', 'NVIDIA_VISIBLE_DEVICES',
        'TORCH_CUDA_ARCH_LIST', 'CUDA_LAUNCH_BLOCKING'
    ]
    
    for var in cuda_vars:
        value = os.environ.get(var, 'Not set')
        print(f"{var}: {value}")


def check_pytorch_installation():
    """Check PyTorch installation details"""
    print("\n🐍 PYTORCH INSTALLATION")
    print("=" * 50)
    
    print(f"Python version: {sys.version}")
    print(f"Platform: {platform.platform()}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"PyTorch compiled with CUDA: {torch.version.cuda}")
    
    # Check if PyTorch was compiled with CUDA support
    if hasattr(torch.backends, 'cudnn'):
        print(f"cuDNN enabled: {torch.backends.cudnn.enabled}")
        print(f"cuDNN version: {torch.backends.cudnn.version()}")
    
    # Check installation method
    try:
        import torch.utils.cpp_extension
        print("✅ PyTorch C++ extensions available")
    except ImportError:
        print("❌ PyTorch C++ extensions not available")


def test_cuda_operations():
    """Test basic CUDA operations"""
    print("\n🧪 CUDA OPERATIONS TEST")
    print("=" * 50)
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available - skipping operations test")
        return
    
    try:
        # Test tensor creation
        device = torch.device('cuda:0')
        x = torch.randn(1000, 1000, device=device)
        print("✅ CUDA tensor creation successful")
        
        # Test computation
        y = torch.matmul(x, x.t())
        print("✅ CUDA matrix multiplication successful")
        
        # Test memory management
        torch.cuda.empty_cache()
        print("✅ CUDA memory management successful")
        
        # Memory info
        allocated = torch.cuda.memory_allocated(0)
        total = torch.cuda.get_device_properties(0).total_memory
        print(f"Memory usage: {allocated / 1e6:.1f} MB / {total / 1e9:.1f} GB")
        
    except Exception as e:
        print(f"❌ CUDA operations failed: {e}")


def provide_solutions():
    """Provide solutions based on diagnostic results"""
    print("\n💡 SOLUTIONS & RECOMMENDATIONS")
    print("=" * 50)
    
    if not torch.cuda.is_available():
        print("🔧 CUDA NOT AVAILABLE - SOLUTIONS:")
        print()
        print("1. **Install NVIDIA Drivers:**")
        print("   - Download from: https://www.nvidia.com/drivers")
        print("   - Use GeForce Experience for automatic updates")
        print()
        print("2. **Install CUDA Toolkit:**")
        print("   - Download from: https://developer.nvidia.com/cuda-downloads")
        print("   - Choose version compatible with your PyTorch")
        print()
        print("3. **Reinstall PyTorch with CUDA:**")
        print("   - Visit: https://pytorch.org/get-started/locally/")
        print("   - Select CUDA version matching your installation")
        print("   - Example: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        print()
        print("4. **Check Hardware Compatibility:**")
        print("   - Ensure your GPU supports CUDA (GTX 600 series or newer)")
        print("   - Check compute capability: https://developer.nvidia.com/cuda-gpus")
        print()
        print("5. **Environment Setup:**")
        print("   - Add CUDA to PATH: C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin")
        print("   - Set CUDA_HOME: C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8")
    else:
        print("✅ CUDA IS AVAILABLE - OPTIMIZATION TIPS:")
        print()
        print("1. **Memory Optimization:**")
        print("   - Use mixed precision (FP16) for faster inference")
        print("   - Enable gradient checkpointing for large models")
        print("   - Clear cache regularly: torch.cuda.empty_cache()")
        print()
        print("2. **Multi-GPU Setup:**")
        print("   - Use DataParallel for simple multi-GPU")
        print("   - Use DistributedDataParallel for better performance")
        print("   - Distribute models across GPUs strategically")
        print()
        print("3. **Performance Tuning:**")
        print("   - Set CUDA_LAUNCH_BLOCKING=0 for async execution")
        print("   - Use torch.backends.cudnn.benchmark=True for consistent input sizes")
        print("   - Optimize batch sizes based on GPU memory")


def create_device_optimization_plan():
    """Create a comprehensive device optimization plan"""
    print("\n📋 DEVICE OPTIMIZATION PLAN")
    print("=" * 50)
    
    plan = {
        "immediate_actions": [],
        "performance_optimizations": [],
        "monitoring_setup": [],
        "fallback_strategies": []
    }
    
    if not torch.cuda.is_available():
        plan["immediate_actions"] = [
            "Install/update NVIDIA drivers",
            "Install CUDA toolkit (version 11.8 recommended)",
            "Reinstall PyTorch with CUDA support",
            "Verify installation with nvidia-smi",
            "Test CUDA operations"
        ]
        plan["fallback_strategies"] = [
            "Use CPU with optimized batch sizes",
            "Enable mixed precision on CPU (limited benefit)",
            "Use model quantization for faster CPU inference",
            "Consider cloud GPU instances for training"
        ]
    else:
        gpu_count = torch.cuda.device_count()
        total_memory = sum(torch.cuda.get_device_properties(i).total_memory for i in range(gpu_count))
        
        plan["performance_optimizations"] = [
            f"Utilize all {gpu_count} GPU(s) with {total_memory/1e9:.1f}GB total memory",
            "Implement multi-GPU model distribution",
            "Use FP16 mixed precision training",
            "Optimize batch sizes per GPU memory",
            "Enable CUDA memory caching"
        ]
        
        plan["monitoring_setup"] = [
            "Set up GPU memory monitoring",
            "Track inference times per device",
            "Monitor GPU utilization",
            "Set up automatic memory cleanup",
            "Log device switching events"
        ]
    
    plan["monitoring_setup"].extend([
        "Implement performance benchmarking",
        "Set up automated device selection",
        "Create memory usage alerts",
        "Track model loading times"
    ])
    
    # Print the plan
    for category, actions in plan.items():
        if actions:
            print(f"\n{category.replace('_', ' ').title()}:")
            for i, action in enumerate(actions, 1):
                print(f"  {i}. {action}")
    
    return plan


def main():
    """Run complete CUDA diagnostic"""
    print("🚀 CTRLCOLOR CUDA DIAGNOSTIC TOOL")
    print("=" * 60)
    
    check_cuda_installation()
    check_environment_variables()
    check_pytorch_installation()
    test_cuda_operations()
    provide_solutions()
    plan = create_device_optimization_plan()
    
    print("\n" + "=" * 60)
    print("✅ DIAGNOSTIC COMPLETE")
    print("=" * 60)
    
    # Summary
    cuda_available = torch.cuda.is_available()
    gpu_count = torch.cuda.device_count() if cuda_available else 0
    
    print(f"\n📊 SUMMARY:")
    print(f"  CUDA Available: {'✅ Yes' if cuda_available else '❌ No'}")
    print(f"  GPU Count: {gpu_count}")
    print(f"  Primary Device: {'cuda:0' if cuda_available else 'cpu'}")
    
    if cuda_available:
        total_memory = sum(torch.cuda.get_device_properties(i).total_memory for i in range(gpu_count))
        print(f"  Total GPU Memory: {total_memory/1e9:.1f} GB")
        print(f"  Recommended Strategy: Multi-GPU with memory optimization")
    else:
        print(f"  Recommended Strategy: Fix CUDA installation or use CPU fallback")
    
    return cuda_available, gpu_count


if __name__ == "__main__":
    main()
