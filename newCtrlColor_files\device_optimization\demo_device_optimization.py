"""
Device Optimization Demo for CtrlColor

Demonstrates the smart device management capabilities and shows
how the system automatically selects optimal devices and configurations.
"""

import torch
import time
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from device_optimization.device_manager import Device<PERSON>anager, get_device_manager


def demo_device_detection():
    """Demonstrate automatic device detection"""
    print("🔍 DEVICE DETECTION DEMO")
    print("=" * 50)
    
    # Initialize device manager
    device_manager = DeviceManager()
    
    # Show detected devices
    device_info = device_manager.get_device_info()
    
    print(f"Primary Device: {device_info['primary_device']}")
    print(f"Available Devices: {device_info['available_devices']}")
    
    print("\nDevice Capabilities:")
    for device_name, caps in device_info['device_capabilities'].items():
        print(f"  {device_name}:")
        print(f"    Memory: {caps['total_memory'] / 1e9:.1f} GB")
        print(f"    FP16 Support: {caps['supports_fp16']}")
        print(f"    BF16 Support: {caps['supports_bf16']}")
        if caps['compute_capability']:
            print(f"    Compute Capability: {caps['compute_capability']}")
    
    if 'current_memory_usage' in device_info:
        mem_info = device_info['current_memory_usage']
        print(f"\nCurrent GPU Memory Usage:")
        print(f"  Allocated: {mem_info['allocated_gb']:.2f} GB")
        print(f"  Total: {mem_info['total_gb']:.2f} GB")
        print(f"  Usage: {mem_info['usage_percent']:.1f}%")


def demo_optimal_device_selection():
    """Demonstrate optimal device selection for different scenarios"""
    print("\n🎯 OPTIMAL DEVICE SELECTION DEMO")
    print("=" * 50)
    
    device_manager = get_device_manager()
    
    scenarios = [
        {"name": "Small Model (1GB)", "memory_gb": 1.0, "fp16": False},
        {"name": "Medium Model (4GB)", "memory_gb": 4.0, "fp16": False},
        {"name": "Large Model (8GB)", "memory_gb": 8.0, "fp16": False},
        {"name": "Large Model with FP16", "memory_gb": 4.0, "fp16": True},
        {"name": "Huge Model (16GB)", "memory_gb": 16.0, "fp16": False},
    ]
    
    for scenario in scenarios:
        optimal_device = device_manager.get_optimal_device(
            memory_required_gb=scenario["memory_gb"],
            requires_fp16=scenario["fp16"]
        )
        print(f"{scenario['name']:25} -> {optimal_device}")


def demo_adaptive_batch_sizing():
    """Demonstrate adaptive batch sizing"""
    print("\n📊 ADAPTIVE BATCH SIZING DEMO")
    print("=" * 50)
    
    device_manager = get_device_manager()
    
    # Test different model sizes and batch sizes
    test_cases = [
        {"model_memory_gb": 0.5, "base_batch": 8},
        {"model_memory_gb": 1.0, "base_batch": 4},
        {"model_memory_gb": 2.0, "base_batch": 2},
        {"model_memory_gb": 4.0, "base_batch": 1},
    ]
    
    for case in test_cases:
        optimal_batch = device_manager.adaptive_batch_size(
            base_batch_size=case["base_batch"],
            model_memory_gb=case["model_memory_gb"]
        )
        print(f"Model: {case['model_memory_gb']:.1f}GB, "
              f"Requested: {case['base_batch']}, "
              f"Optimal: {optimal_batch}")


def demo_multi_gpu_strategy():
    """Demonstrate multi-GPU model distribution"""
    print("\n🔄 MULTI-GPU STRATEGY DEMO")
    print("=" * 50)
    
    device_manager = get_device_manager(enable_multi_gpu=True)
    
    # Simulate different model configurations
    model_configs = [
        {"main_model": "dummy", "vae_model": "dummy"},
        {"main_model": "dummy", "vae_model": "dummy", "blip_model": "dummy"},
        {"unet": "dummy", "vae": "dummy", "text_encoder": "dummy", "clip": "dummy"},
    ]
    
    for i, models in enumerate(model_configs):
        print(f"\nConfiguration {i+1}: {list(models.keys())}")
        strategy = device_manager.get_multi_gpu_strategy(models)
        
        for model_name, device in strategy.items():
            print(f"  {model_name:15} -> {device}")


def demo_memory_optimization():
    """Demonstrate memory optimization"""
    print("\n🧹 MEMORY OPTIMIZATION DEMO")
    print("=" * 50)
    
    device_manager = get_device_manager()
    
    if device_manager.primary_device.type == 'cuda':
        # Show memory before optimization
        gpu_id = device_manager.primary_device.index or 0
        before_allocated = torch.cuda.memory_allocated(gpu_id)
        before_cached = torch.cuda.memory_reserved(gpu_id)
        
        print(f"Before optimization:")
        print(f"  Allocated: {before_allocated / 1e9:.2f} GB")
        print(f"  Cached: {before_cached / 1e9:.2f} GB")
        
        # Create some dummy tensors to use memory
        dummy_tensors = []
        for i in range(5):
            tensor = torch.randn(1000, 1000, device=device_manager.primary_device)
            dummy_tensors.append(tensor)
        
        after_alloc = torch.cuda.memory_allocated(gpu_id)
        print(f"\nAfter creating dummy tensors:")
        print(f"  Allocated: {after_alloc / 1e9:.2f} GB")
        
        # Delete tensors and optimize memory
        del dummy_tensors
        device_manager.optimize_memory_usage()
        
        final_allocated = torch.cuda.memory_allocated(gpu_id)
        final_cached = torch.cuda.memory_reserved(gpu_id)
        
        print(f"\nAfter optimization:")
        print(f"  Allocated: {final_allocated / 1e9:.2f} GB")
        print(f"  Cached: {final_cached / 1e9:.2f} GB")
        
    else:
        print("Memory optimization demo requires CUDA device")


def demo_performance_monitoring():
    """Demonstrate performance monitoring"""
    print("\n📈 PERFORMANCE MONITORING DEMO")
    print("=" * 50)
    
    device_manager = get_device_manager()
    
    # Simulate some inference runs
    print("Simulating inference runs...")
    
    for i in range(10):
        start_time = time.time()
        
        # Simulate inference work
        if device_manager.primary_device.type != 'cpu':
            dummy_tensor = torch.randn(100, 100, device=device_manager.primary_device)
            result = torch.matmul(dummy_tensor, dummy_tensor.T)
            del dummy_tensor, result
        else:
            time.sleep(0.1)  # Simulate CPU work
        
        inference_time = time.time() - start_time
        memory_used = 100 * 1024 * 1024  # Dummy memory usage
        
        device_manager.monitor_performance(inference_time, memory_used)
        
        if i % 3 == 0:
            print(f"  Run {i+1}: {inference_time:.3f}s")
    
    # Get recommendations
    recommendations = device_manager.get_performance_recommendations()
    
    print(f"\nPerformance Recommendations:")
    for rec in recommendations:
        print(f"  • {rec}")


def demo_real_world_scenario():
    """Demonstrate a real-world usage scenario"""
    print("\n🌍 REAL-WORLD SCENARIO DEMO")
    print("=" * 50)
    
    print("Scenario: Loading CtrlColor models for inference")
    
    device_manager = get_device_manager(
        memory_threshold=0.8,
        enable_multi_gpu=True,
        preferred_device="auto"
    )
    
    # Simulate model loading decisions
    models = {
        "main_model": {"size_gb": 3.5, "requires_fp16": False},
        "vae_model": {"size_gb": 1.2, "requires_fp16": False},
        "blip_model": {"size_gb": 0.8, "requires_fp16": False}
    }
    
    print("\nModel Loading Strategy:")
    
    for model_name, specs in models.items():
        optimal_device = device_manager.get_optimal_device(
            memory_required_gb=specs["size_gb"],
            requires_fp16=specs["requires_fp16"]
        )
        
        optimal_batch = device_manager.adaptive_batch_size(
            base_batch_size=4,
            model_memory_gb=specs["size_gb"]
        )
        
        print(f"  {model_name}:")
        print(f"    Device: {optimal_device}")
        print(f"    Optimal Batch Size: {optimal_batch}")
    
    # Multi-GPU distribution
    print(f"\nMulti-GPU Distribution:")
    strategy = device_manager.get_multi_gpu_strategy(models)
    for model_name, device in strategy.items():
        print(f"  {model_name:15} -> {device}")


def main():
    """Run all device optimization demos"""
    print("🚀 CTRLCOLOR DEVICE OPTIMIZATION DEMO")
    print("=" * 60)
    
    try:
        demo_device_detection()
        demo_optimal_device_selection()
        demo_adaptive_batch_sizing()
        demo_multi_gpu_strategy()
        demo_memory_optimization()
        demo_performance_monitoring()
        demo_real_world_scenario()
        
        print("\n" + "=" * 60)
        print("✅ ALL DEMOS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        # Final recommendations
        device_manager = get_device_manager()
        final_recommendations = device_manager.get_performance_recommendations()
        
        if final_recommendations:
            print("\n🎯 FINAL OPTIMIZATION RECOMMENDATIONS:")
            for rec in final_recommendations:
                print(f"  • {rec}")
        
        print(f"\n📊 DEVICE SUMMARY:")
        device_info = device_manager.get_device_info()
        print(f"  Primary Device: {device_info['primary_device']}")
        print(f"  Multi-GPU Enabled: {device_info['multi_gpu_enabled']}")
        print(f"  Memory Threshold: {device_info['memory_threshold']:.0%}")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
