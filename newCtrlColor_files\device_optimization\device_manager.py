"""
Smart Device Manager for CtrlColor

Provides intelligent device selection, memory management, and multi-GPU support
for optimal performance across different hardware configurations.
"""

import torch
import psutil
import time
import logging
from typing import Dict, List, Optional, Tuple, Any
import numpy as np


class DeviceManager:
    """
    Centralized device management for CtrlColor
    
    Features:
    - Automatic device detection (CUDA, MPS, CPU)
    - Memory-aware device selection
    - Multi-GPU support
    - Performance monitoring
    - Fallback strategies
    """
    
    def __init__(self, 
                 memory_threshold: float = 0.8,
                 enable_multi_gpu: bool = True,
                 preferred_device: str = "auto"):
        """
        Initialize device manager
        
        Args:
            memory_threshold: Maximum memory usage before switching devices (0.0-1.0)
            enable_multi_gpu: Whether to use multiple GPUs if available
            preferred_device: Preferred device ("auto", "cuda", "mps", "cpu")
        """
        self.memory_threshold = memory_threshold
        self.enable_multi_gpu = enable_multi_gpu
        self.preferred_device = preferred_device
        
        # Device detection
        self.available_devices = self._detect_devices()
        self.primary_device = self._select_primary_device()
        self.device_capabilities = self._analyze_device_capabilities()
        
        # Performance tracking
        self.performance_history = {
            'inference_times': [],
            'memory_usage': [],
            'device_switches': 0
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"DeviceManager initialized with primary device: {self.primary_device}")
        self.logger.info(f"Available devices: {list(self.available_devices.keys())}")
    
    def _detect_devices(self) -> Dict[str, torch.device]:
        """Detect all available devices"""
        devices = {'cpu': torch.device('cpu')}
        
        # CUDA detection
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                device_name = f'cuda:{i}'
                devices[device_name] = torch.device(device_name)
                
                # Log GPU info
                props = torch.cuda.get_device_properties(i)
                self.logger.info(f"GPU {i}: {props.name}, Memory: {props.total_memory / 1e9:.1f}GB")
        
        # MPS detection (Apple Silicon)
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            devices['mps'] = torch.device('mps')
            self.logger.info("Apple MPS device detected")
        
        return devices
    
    def _select_primary_device(self) -> torch.device:
        """Select primary device based on preferences and capabilities"""
        
        if self.preferred_device != "auto":
            if self.preferred_device in self.available_devices:
                return self.available_devices[self.preferred_device]
            else:
                self.logger.warning(f"Preferred device {self.preferred_device} not available, using auto selection")
        
        # Auto selection priority: Best CUDA > MPS > CPU
        cuda_devices = [name for name in self.available_devices.keys() if name.startswith('cuda')]
        
        if cuda_devices:
            # Select CUDA device with most memory
            best_cuda = max(cuda_devices, key=lambda x: self._get_device_memory(x))
            return self.available_devices[best_cuda]
        elif 'mps' in self.available_devices:
            return self.available_devices['mps']
        else:
            return self.available_devices['cpu']
    
    def _get_device_memory(self, device_name: str) -> int:
        """Get total memory for a device"""
        if device_name.startswith('cuda'):
            gpu_id = int(device_name.split(':')[1]) if ':' in device_name else 0
            return torch.cuda.get_device_properties(gpu_id).total_memory
        elif device_name == 'cpu':
            return psutil.virtual_memory().total
        else:
            return 0  # Unknown device
    
    def _analyze_device_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Analyze capabilities of each device"""
        capabilities = {}
        
        for device_name, device in self.available_devices.items():
            caps = {
                'total_memory': self._get_device_memory(device_name),
                'supports_fp16': False,
                'supports_bf16': False,
                'compute_capability': None
            }
            
            if device_name.startswith('cuda'):
                gpu_id = int(device_name.split(':')[1]) if ':' in device_name else 0
                props = torch.cuda.get_device_properties(gpu_id)
                
                caps['supports_fp16'] = props.major >= 6  # Pascal and newer
                caps['supports_bf16'] = props.major >= 8  # Ampere and newer
                caps['compute_capability'] = (props.major, props.minor)
            elif device_name == 'mps':
                caps['supports_fp16'] = True  # MPS supports FP16
            
            capabilities[device_name] = caps
        
        return capabilities
    
    def get_optimal_device(self, 
                          memory_required_gb: Optional[float] = None,
                          requires_fp16: bool = False) -> torch.device:
        """
        Get optimal device based on requirements
        
        Args:
            memory_required_gb: Required memory in GB
            requires_fp16: Whether FP16 support is required
            
        Returns:
            Optimal device for the task
        """
        suitable_devices = []
        
        for device_name, device in self.available_devices.items():
            caps = self.device_capabilities[device_name]
            
            # Check FP16 requirement
            if requires_fp16 and not caps['supports_fp16']:
                continue
            
            # Check memory requirement
            if memory_required_gb is not None:
                available_memory_gb = self._get_available_memory(device_name) / 1e9
                if available_memory_gb < memory_required_gb:
                    continue
            
            suitable_devices.append((device_name, device))
        
        if not suitable_devices:
            self.logger.warning("No suitable device found, falling back to CPU")
            return self.available_devices['cpu']
        
        # Select best suitable device (prefer CUDA > MPS > CPU)
        cuda_devices = [(name, dev) for name, dev in suitable_devices if name.startswith('cuda')]
        if cuda_devices:
            # Select CUDA device with most available memory
            best_device = max(cuda_devices, key=lambda x: self._get_available_memory(x[0]))
            return best_device[1]
        
        mps_devices = [(name, dev) for name, dev in suitable_devices if name == 'mps']
        if mps_devices:
            return mps_devices[0][1]
        
        return suitable_devices[0][1]  # Fallback to first suitable device
    
    def _get_available_memory(self, device_name: str) -> int:
        """Get available memory for a device"""
        if device_name.startswith('cuda'):
            gpu_id = int(device_name.split(':')[1]) if ':' in device_name else 0
            total_memory = torch.cuda.get_device_properties(gpu_id).total_memory
            allocated_memory = torch.cuda.memory_allocated(gpu_id)
            return total_memory - allocated_memory
        elif device_name == 'cpu':
            return psutil.virtual_memory().available
        else:
            return 0
    
    def optimize_memory_usage(self, device: Optional[torch.device] = None):
        """Optimize memory usage for specified device"""
        if device is None:
            device = self.primary_device
        
        if device.type == 'cuda':
            # Get current memory usage
            gpu_id = device.index if device.index is not None else 0
            allocated = torch.cuda.memory_allocated(gpu_id)
            total = torch.cuda.get_device_properties(gpu_id).total_memory
            usage_ratio = allocated / total
            
            if usage_ratio > self.memory_threshold:
                self.logger.info(f"Memory usage {usage_ratio:.1%} > threshold {self.memory_threshold:.1%}, clearing cache")
                torch.cuda.empty_cache()
                
                # Log memory after cleanup
                new_allocated = torch.cuda.memory_allocated(gpu_id)
                new_usage_ratio = new_allocated / total
                self.logger.info(f"Memory usage after cleanup: {new_usage_ratio:.1%}")
    
    def get_multi_gpu_strategy(self, models: Dict[str, Any]) -> Dict[str, torch.device]:
        """
        Get multi-GPU distribution strategy for models
        
        Args:
            models: Dictionary of model names and objects
            
        Returns:
            Dictionary mapping model names to optimal devices
        """
        if not self.enable_multi_gpu:
            return {name: self.primary_device for name in models.keys()}
        
        cuda_devices = [dev for name, dev in self.available_devices.items() if name.startswith('cuda')]
        
        if len(cuda_devices) < 2:
            return {name: self.primary_device for name in models.keys()}
        
        # Strategy: Distribute models across GPUs based on estimated memory usage
        model_assignments = {}
        
        if 'main_model' in models and 'vae_model' in models:
            # Put main model on GPU 0, VAE on GPU 1
            model_assignments['main_model'] = cuda_devices[0]
            model_assignments['vae_model'] = cuda_devices[1] if len(cuda_devices) > 1 else cuda_devices[0]
        
        if 'blip_model' in models:
            # BLIP model on GPU with most available memory
            best_gpu = max(cuda_devices, key=lambda dev: self._get_available_memory(f'cuda:{dev.index}'))
            model_assignments['blip_model'] = best_gpu
        
        # Assign remaining models
        for model_name in models.keys():
            if model_name not in model_assignments:
                model_assignments[model_name] = self.primary_device
        
        return model_assignments
    
    def adaptive_batch_size(self, 
                           base_batch_size: int,
                           model_memory_gb: float,
                           device: Optional[torch.device] = None) -> int:
        """
        Calculate adaptive batch size based on available memory
        
        Args:
            base_batch_size: Desired batch size
            model_memory_gb: Estimated memory usage per sample in GB
            device: Target device (uses primary if None)
            
        Returns:
            Optimal batch size for current memory conditions
        """
        if device is None:
            device = self.primary_device
        
        if device.type == 'cpu':
            # For CPU, use conservative batch size
            return min(base_batch_size, 4)
        
        if device.type == 'cuda':
            gpu_id = device.index if device.index is not None else 0
            available_memory = self._get_available_memory(f'cuda:{gpu_id}')
            available_memory_gb = available_memory / 1e9
            
            # Reserve 20% of memory for other operations
            usable_memory_gb = available_memory_gb * 0.8
            
            # Calculate maximum batch size that fits in memory
            max_batch_size = int(usable_memory_gb / model_memory_gb)
            optimal_batch_size = min(base_batch_size, max(1, max_batch_size))
            
            if optimal_batch_size != base_batch_size:
                self.logger.info(f"Adjusted batch size from {base_batch_size} to {optimal_batch_size} due to memory constraints")
            
            return optimal_batch_size
        
        return base_batch_size
    
    def monitor_performance(self, inference_time: float, memory_used: int):
        """Record performance metrics"""
        self.performance_history['inference_times'].append(inference_time)
        self.performance_history['memory_usage'].append(memory_used)
        
        # Keep only recent history (last 100 measurements)
        if len(self.performance_history['inference_times']) > 100:
            self.performance_history['inference_times'] = self.performance_history['inference_times'][-100:]
            self.performance_history['memory_usage'] = self.performance_history['memory_usage'][-100:]
    
    def get_performance_recommendations(self) -> List[str]:
        """Get performance optimization recommendations"""
        recommendations = []
        
        if len(self.performance_history['inference_times']) < 5:
            return ["Collect more performance data for recommendations"]
        
        avg_time = np.mean(self.performance_history['inference_times'][-10:])
        
        # Slow inference recommendations
        if avg_time > 5.0:
            recommendations.append("Consider using mixed precision (FP16) for faster inference")
            recommendations.append("Try increasing batch size if memory allows")
            
            if self.primary_device.type == 'cpu':
                recommendations.append("Consider using GPU acceleration if available")
        
        # Memory usage recommendations
        if self.primary_device.type == 'cuda':
            gpu_id = self.primary_device.index if self.primary_device.index is not None else 0
            total_memory = torch.cuda.get_device_properties(gpu_id).total_memory
            current_usage = torch.cuda.memory_allocated(gpu_id)
            usage_ratio = current_usage / total_memory
            
            if usage_ratio < 0.5:
                recommendations.append("GPU memory underutilized - consider increasing batch size")
            elif usage_ratio > 0.9:
                recommendations.append("GPU memory near limit - consider reducing batch size or using gradient checkpointing")
        
        return recommendations
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get comprehensive device information"""
        info = {
            'primary_device': str(self.primary_device),
            'available_devices': list(self.available_devices.keys()),
            'device_capabilities': self.device_capabilities,
            'multi_gpu_enabled': self.enable_multi_gpu,
            'memory_threshold': self.memory_threshold
        }
        
        # Add current memory usage for CUDA devices
        if self.primary_device.type == 'cuda':
            gpu_id = self.primary_device.index if self.primary_device.index is not None else 0
            allocated = torch.cuda.memory_allocated(gpu_id)
            total = torch.cuda.get_device_properties(gpu_id).total_memory
            info['current_memory_usage'] = {
                'allocated_gb': allocated / 1e9,
                'total_gb': total / 1e9,
                'usage_percent': (allocated / total) * 100
            }
        
        return info


# Global device manager instance
_device_manager = None

def get_device_manager(**kwargs) -> DeviceManager:
    """Get global device manager instance"""
    global _device_manager
    if _device_manager is None:
        _device_manager = DeviceManager(**kwargs)
    return _device_manager

def reset_device_manager():
    """Reset global device manager (useful for testing)"""
    global _device_manager
    _device_manager = None
