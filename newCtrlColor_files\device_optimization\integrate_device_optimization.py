"""
Integration Script for CtrlColor Device Optimization

This script shows how to integrate the device optimization system
into the existing CtrlColor codebase, replacing hardcoded device
selections with intelligent device management.
"""

import torch
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from device_optimization.device_manager import get_device_manager


class OptimizedCtrlColorInference:
    """
    Optimized version of CtrlColor inference with smart device management
    
    This class demonstrates how to integrate device optimization
    into the existing CtrlColor inference pipeline.
    """
    
    def __init__(self, 
                 model_config_path: str,
                 checkpoint_path: str,
                 vae_checkpoint_path: str,
                 enable_multi_gpu: bool = True,
                 memory_threshold: float = 0.8):
        """
        Initialize optimized CtrlColor inference
        
        Args:
            model_config_path: Path to model configuration
            checkpoint_path: Path to main model checkpoint
            vae_checkpoint_path: Path to VAE checkpoint
            enable_multi_gpu: Whether to use multi-GPU if available
            memory_threshold: Memory usage threshold for optimization
        """
        
        # Initialize device manager
        self.device_manager = get_device_manager(
            enable_multi_gpu=enable_multi_gpu,
            memory_threshold=memory_threshold
        )
        
        print("🔧 Initializing Optimized CtrlColor...")
        print(f"Primary Device: {self.device_manager.primary_device}")
        
        # Load models with optimal device placement
        self.models = self._load_models_optimized(
            model_config_path, 
            checkpoint_path, 
            vae_checkpoint_path
        )
        
        print("✅ Models loaded successfully!")
        
        # Print device assignments
        for model_name, model_info in self.models.items():
            print(f"  {model_name}: {model_info['device']}")
    
    def _load_models_optimized(self, model_config_path, checkpoint_path, vae_checkpoint_path):
        """Load models with optimal device placement"""
        
        # Estimate model memory requirements
        model_memory_estimates = {
            'main_model': 3.5,  # GB
            'vae_model': 1.2,   # GB
            'blip_model': 0.8   # GB
        }
        
        models = {}
        
        # Get multi-GPU strategy
        gpu_strategy = self.device_manager.get_multi_gpu_strategy(model_memory_estimates)
        
        # Load main model
        print("Loading main model...")
        main_device = self.device_manager.get_optimal_device(
            memory_required_gb=model_memory_estimates['main_model']
        )
        
        # In practice, replace this with actual model loading
        # from cldm.model import create_model, load_state_dict
        # model = create_model(model_config_path).cpu()
        # model.load_state_dict(load_state_dict(checkpoint_path, location='cpu'), strict=False)
        # model = model.to(main_device)
        
        # For demo, create dummy model
        main_model = torch.nn.Linear(100, 100).to(main_device)
        
        models['main_model'] = {
            'model': main_model,
            'device': main_device,
            'memory_gb': model_memory_estimates['main_model']
        }
        
        # Load VAE model
        print("Loading VAE model...")
        vae_device = gpu_strategy.get('vae_model', main_device)
        
        # In practice:
        # from ldm.models.autoencoder import AutoencoderKL
        # vae = AutoencoderKL(...)
        # vae.load_state_dict(load_state_dict(vae_checkpoint_path, location='cpu'))
        # vae = vae.to(vae_device)
        
        # For demo:
        vae_model = torch.nn.Conv2d(3, 3, 3).to(vae_device)
        
        models['vae_model'] = {
            'model': vae_model,
            'device': vae_device,
            'memory_gb': model_memory_estimates['vae_model']
        }
        
        # Load BLIP model
        print("Loading BLIP model...")
        blip_device = self.device_manager.get_optimal_device(
            memory_required_gb=model_memory_estimates['blip_model']
        )
        
        # In practice:
        # from lavis.models import load_model_and_preprocess
        # blip_model, vis_processors, _ = load_model_and_preprocess(
        #     name="blip_caption", model_type="base_coco", 
        #     is_eval=True, device=blip_device
        # )
        
        # For demo:
        blip_model = torch.nn.Linear(50, 50).to(blip_device)
        
        models['blip_model'] = {
            'model': blip_model,
            'device': blip_device,
            'memory_gb': model_memory_estimates['blip_model']
        }
        
        return models
    
    def preprocess_image(self, image_path: str, target_size: tuple = (512, 512)):
        """Preprocess image with optimal device placement"""
        
        # In practice, load and preprocess image
        # For demo, create dummy image tensor
        dummy_image = torch.randn(1, 3, *target_size)
        
        # Move to optimal device for preprocessing
        preprocess_device = self.device_manager.get_optimal_device(memory_required_gb=0.1)
        image_tensor = dummy_image.to(preprocess_device)
        
        return image_tensor
    
    def run_inference(self, 
                     image_tensor: torch.Tensor,
                     prompt: str = "",
                     num_samples: int = 1,
                     ddim_steps: int = 20):
        """Run optimized inference with adaptive batch sizing and memory management"""
        
        print(f"🚀 Running inference with {num_samples} samples, {ddim_steps} steps")
        
        # Optimize memory before inference
        self.device_manager.optimize_memory_usage()
        
        # Calculate optimal batch size
        estimated_memory_per_sample = 0.5  # GB per sample
        optimal_batch_size = self.device_manager.adaptive_batch_size(
            base_batch_size=num_samples,
            model_memory_gb=estimated_memory_per_sample
        )
        
        if optimal_batch_size != num_samples:
            print(f"Adjusted batch size from {num_samples} to {optimal_batch_size}")
        
        # Start performance monitoring
        import time
        start_time = time.time()
        
        # Move input to main model device
        main_device = self.models['main_model']['device']
        image_tensor = image_tensor.to(main_device)
        
        # Simulate inference steps
        results = []
        
        for batch_start in range(0, num_samples, optimal_batch_size):
            batch_end = min(batch_start + optimal_batch_size, num_samples)
            current_batch_size = batch_end - batch_start
            
            print(f"  Processing batch {batch_start//optimal_batch_size + 1}: samples {batch_start}-{batch_end-1}")
            
            # Create batch
            batch_tensor = image_tensor.repeat(current_batch_size, 1, 1, 1)
            
            # Simulate main model inference
            with torch.no_grad():
                # In practice: main_output = self.models['main_model']['model'](batch_tensor, ...)
                main_output = self.models['main_model']['model'](batch_tensor.view(current_batch_size, -1))
                
                # Move to VAE device if different
                vae_device = self.models['vae_model']['device']
                if vae_device != main_device:
                    main_output = main_output.to(vae_device)
                
                # Simulate VAE decoding
                # In practice: decoded = self.models['vae_model']['model'].decode(main_output)
                decoded = self.models['vae_model']['model'](batch_tensor.to(vae_device))
                
                results.append(decoded.cpu())
            
            # Memory optimization after each batch
            if batch_end < num_samples:
                self.device_manager.optimize_memory_usage()
        
        # Combine results
        final_result = torch.cat(results, dim=0)
        
        # Performance monitoring
        inference_time = time.time() - start_time
        memory_used = sum(model_info['memory_gb'] for model_info in self.models.values()) * 1e9
        
        self.device_manager.monitor_performance(inference_time, memory_used)
        
        print(f"✅ Inference completed in {inference_time:.2f}s")
        
        # Get performance recommendations
        recommendations = self.device_manager.get_performance_recommendations()
        if recommendations:
            print("💡 Performance recommendations:")
            for rec in recommendations:
                print(f"  • {rec}")
        
        return final_result
    
    def get_system_status(self):
        """Get current system status and recommendations"""
        device_info = self.device_manager.get_device_info()
        
        print("\n📊 SYSTEM STATUS")
        print("=" * 40)
        print(f"Primary Device: {device_info['primary_device']}")
        print(f"Available Devices: {device_info['available_devices']}")
        print(f"Multi-GPU Enabled: {device_info['multi_gpu_enabled']}")
        
        if 'current_memory_usage' in device_info:
            mem_info = device_info['current_memory_usage']
            print(f"GPU Memory Usage: {mem_info['usage_percent']:.1f}% ({mem_info['allocated_gb']:.2f}/{mem_info['total_gb']:.2f} GB)")
        
        recommendations = self.device_manager.get_performance_recommendations()
        if recommendations:
            print("\n💡 Recommendations:")
            for rec in recommendations:
                print(f"  • {rec}")


def demo_integration():
    """Demonstrate the integration of device optimization"""
    
    print("🎯 CTRLCOLOR DEVICE OPTIMIZATION INTEGRATION DEMO")
    print("=" * 60)
    
    # Initialize optimized inference
    try:
        inference = OptimizedCtrlColorInference(
            model_config_path="dummy_config.yaml",
            checkpoint_path="dummy_checkpoint.ckpt", 
            vae_checkpoint_path="dummy_vae.ckpt",
            enable_multi_gpu=True,
            memory_threshold=0.8
        )
        
        # Preprocess dummy image
        print("\n📸 Preprocessing image...")
        image_tensor = inference.preprocess_image("dummy_image.jpg")
        print(f"Image tensor shape: {image_tensor.shape}")
        print(f"Image tensor device: {image_tensor.device}")
        
        # Run inference with different configurations
        test_configs = [
            {"num_samples": 1, "ddim_steps": 20},
            {"num_samples": 4, "ddim_steps": 20},
            {"num_samples": 8, "ddim_steps": 10},
        ]
        
        for i, config in enumerate(test_configs):
            print(f"\n🔄 Test {i+1}: {config}")
            result = inference.run_inference(
                image_tensor=image_tensor,
                prompt="a beautiful landscape",
                **config
            )
            print(f"Result shape: {result.shape}")
        
        # Show system status
        inference.get_system_status()
        
        print("\n✅ Integration demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Integration demo failed: {e}")
        import traceback
        traceback.print_exc()


def show_before_after_comparison():
    """Show before/after comparison of device handling"""
    
    print("\n📋 BEFORE vs AFTER COMPARISON")
    print("=" * 60)
    
    print("BEFORE (Original CtrlColor):")
    print("```python")
    print("# Hardcoded device selection")
    print("model = create_model('./models/config.yaml').cpu()")
    print("model.load_state_dict(load_state_dict(ckpt_path, location='cuda'), strict=False)")
    print("model = model.cuda()  # ❌ Assumes CUDA is available")
    print("")
    print("device = torch.device('cuda')  # ❌ No fallback")
    print("BLIP_model = load_model_and_preprocess(..., device=device)")
    print("")
    print("vae.load_state_dict(load_state_dict(vae_path, location='cuda'))")
    print("vae = vae.cuda()  # ❌ Hardcoded CUDA")
    print("```")
    
    print("\nAFTER (Optimized CtrlColor):")
    print("```python")
    print("# Smart device management")
    print("device_manager = get_device_manager()")
    print("optimal_device = device_manager.get_optimal_device(memory_required_gb=3.5)")
    print("")
    print("model = create_model('./models/config.yaml').cpu()")
    print("model.load_state_dict(load_state_dict(ckpt_path, location='cpu'), strict=False)")
    print("model = model.to(optimal_device)  # ✅ Optimal device selection")
    print("")
    print("# Multi-GPU distribution")
    print("gpu_strategy = device_manager.get_multi_gpu_strategy(models)")
    print("blip_device = gpu_strategy['blip_model']")
    print("BLIP_model = load_model_and_preprocess(..., device=blip_device)")
    print("")
    print("# Adaptive batch sizing")
    print("optimal_batch = device_manager.adaptive_batch_size(base_batch_size=4, model_memory_gb=0.5)")
    print("```")
    
    print("\n🎯 KEY IMPROVEMENTS:")
    print("✅ Automatic device detection (CUDA, MPS, CPU)")
    print("✅ Memory-aware device selection")
    print("✅ Multi-GPU model distribution")
    print("✅ Adaptive batch sizing")
    print("✅ Automatic memory optimization")
    print("✅ Performance monitoring and recommendations")
    print("✅ Graceful fallback strategies")


if __name__ == "__main__":
    demo_integration()
    show_before_after_comparison()
