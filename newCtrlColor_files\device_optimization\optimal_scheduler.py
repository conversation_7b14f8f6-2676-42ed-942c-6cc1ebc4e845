"""
Optimal Device Scheduler for CtrlColor

Provides intelligent device scheduling for maximum performance across
different hardware configurations and model components.
"""

import torch
import time
import logging
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from enum import Enum
import threading
import queue


class ModelType(Enum):
    """Model types for optimal scheduling"""
    UNET = "unet"
    VAE = "vae"
    TEXT_ENCODER = "text_encoder"
    CLIP = "clip"
    CONTROLNET = "controlnet"
    EXEMPLAR_PROCESSOR = "exemplar_processor"


@dataclass
class ModelProfile:
    """Profile for model memory and compute requirements"""
    name: str
    model_type: ModelType
    memory_gb: float
    compute_intensity: float  # 0.0-1.0
    requires_fp16: bool = False
    can_offload: bool = True
    priority: int = 1  # 1=highest, 5=lowest


class OptimalDeviceScheduler:
    """
    Advanced device scheduler for optimal performance
    
    Features:
    - Dynamic model placement based on memory and compute requirements
    - Automatic model offloading and loading
    - Performance-based device selection
    - Memory pressure management
    - Multi-GPU load balancing
    """
    
    def __init__(self, 
                 enable_offloading: bool = True,
                 memory_threshold: float = 0.85,
                 performance_tracking: bool = True):
        """
        Initialize optimal device scheduler
        
        Args:
            enable_offloading: Enable CPU offloading when GPU memory is full
            memory_threshold: Memory usage threshold before offloading
            performance_tracking: Track and optimize based on performance
        """
        self.enable_offloading = enable_offloading
        self.memory_threshold = memory_threshold
        self.performance_tracking = performance_tracking
        
        # Device detection and analysis
        self.devices = self._detect_and_analyze_devices()
        self.primary_device = self._select_primary_device()
        
        # Model management
        self.model_registry: Dict[str, ModelProfile] = {}
        self.device_assignments: Dict[str, torch.device] = {}
        self.model_cache: Dict[str, Any] = {}
        self.offloaded_models: Dict[str, Any] = {}
        
        # Performance tracking
        self.performance_history: Dict[str, List[float]] = {}
        self.device_utilization: Dict[str, float] = {}
        
        # Threading for async operations
        self.offload_queue = queue.Queue()
        self.offload_thread = threading.Thread(target=self._offload_worker, daemon=True)
        self.offload_thread.start()
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"OptimalDeviceScheduler initialized with primary device: {self.primary_device}")
    
    def _detect_and_analyze_devices(self) -> Dict[str, Dict[str, Any]]:
        """Detect and analyze all available devices"""
        devices = {}
        
        # CPU analysis
        import psutil
        devices['cpu'] = {
            'device': torch.device('cpu'),
            'memory_gb': psutil.virtual_memory().total / 1e9,
            'compute_score': 1.0,  # Baseline
            'supports_fp16': False,
            'supports_bf16': False,
            'bandwidth_score': 0.5,  # Relative to GPU
            'available': True
        }
        
        # CUDA analysis
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                device_name = f'cuda:{i}'
                
                # Compute score based on specs
                compute_score = self._calculate_compute_score(props)
                bandwidth_score = self._calculate_bandwidth_score(props)
                
                devices[device_name] = {
                    'device': torch.device(device_name),
                    'memory_gb': props.total_memory / 1e9,
                    'compute_score': compute_score,
                    'supports_fp16': props.major >= 6,
                    'supports_bf16': props.major >= 8,
                    'bandwidth_score': bandwidth_score,
                    'available': True,
                    'props': props
                }
        
        # MPS analysis (Apple Silicon)
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            devices['mps'] = {
                'device': torch.device('mps'),
                'memory_gb': 16.0,  # Estimate for Apple Silicon
                'compute_score': 3.0,  # Good performance
                'supports_fp16': True,
                'supports_bf16': False,
                'bandwidth_score': 2.0,
                'available': True
            }
        
        return devices
    
    def _calculate_compute_score(self, props) -> float:
        """Calculate relative compute score for GPU"""
        # Based on compute capability and multiprocessor count
        base_score = props.major + props.minor * 0.1
        mp_multiplier = props.multi_processor_count / 100.0
        return base_score * mp_multiplier
    
    def _calculate_bandwidth_score(self, props) -> float:
        """Calculate relative memory bandwidth score"""
        # Estimate based on memory size and type
        memory_gb = props.total_memory / 1e9
        if memory_gb >= 24:  # High-end cards
            return 5.0
        elif memory_gb >= 12:  # Mid-high range
            return 4.0
        elif memory_gb >= 8:   # Mid range
            return 3.0
        elif memory_gb >= 4:   # Entry level
            return 2.0
        else:
            return 1.0
    
    def _select_primary_device(self) -> torch.device:
        """Select the best primary device"""
        cuda_devices = {k: v for k, v in self.devices.items() if k.startswith('cuda')}
        
        if cuda_devices:
            # Select CUDA device with highest compute score
            best_cuda = max(cuda_devices.items(), key=lambda x: x[1]['compute_score'])
            return best_cuda[1]['device']
        elif 'mps' in self.devices:
            return self.devices['mps']['device']
        else:
            return self.devices['cpu']['device']
    
    def register_model(self, 
                      name: str,
                      model: Any,
                      model_type: ModelType,
                      memory_gb: Optional[float] = None,
                      compute_intensity: float = 0.5,
                      priority: int = 1) -> None:
        """
        Register a model for optimal scheduling
        
        Args:
            name: Unique model name
            model: Model object
            model_type: Type of model
            memory_gb: Estimated memory usage (auto-estimated if None)
            compute_intensity: Relative compute intensity (0.0-1.0)
            priority: Scheduling priority (1=highest, 5=lowest)
        """
        if memory_gb is None:
            memory_gb = self._estimate_model_memory(model)
        
        profile = ModelProfile(
            name=name,
            model_type=model_type,
            memory_gb=memory_gb,
            compute_intensity=compute_intensity,
            requires_fp16=compute_intensity > 0.7,
            priority=priority
        )
        
        self.model_registry[name] = profile
        self.model_cache[name] = model
        
        # Assign optimal device
        optimal_device = self._get_optimal_device_for_model(profile)
        self.device_assignments[name] = optimal_device
        
        self.logger.info(f"Registered model '{name}' -> {optimal_device}")
    
    def _estimate_model_memory(self, model) -> float:
        """Estimate model memory usage in GB"""
        try:
            param_count = sum(p.numel() for p in model.parameters())
            # Estimate: 4 bytes per parameter + overhead
            memory_bytes = param_count * 4 * 1.5  # 50% overhead
            return memory_bytes / 1e9
        except:
            return 1.0  # Default estimate
    
    def _get_optimal_device_for_model(self, profile: ModelProfile) -> torch.device:
        """Get optimal device for a specific model"""
        suitable_devices = []
        
        for device_name, device_info in self.devices.items():
            if not device_info['available']:
                continue
            
            # Check memory requirement
            available_memory = self._get_available_memory(device_name)
            if available_memory < profile.memory_gb:
                continue
            
            # Check FP16 requirement
            if profile.requires_fp16 and not device_info['supports_fp16']:
                continue
            
            # Calculate suitability score
            compute_score = device_info['compute_score']
            memory_score = available_memory / profile.memory_gb
            bandwidth_score = device_info['bandwidth_score']
            
            # Weight by compute intensity
            total_score = (
                compute_score * profile.compute_intensity +
                bandwidth_score * (1 - profile.compute_intensity) +
                memory_score * 0.1
            )
            
            suitable_devices.append((device_name, device_info['device'], total_score))
        
        if not suitable_devices:
            self.logger.warning(f"No suitable device for {profile.name}, using CPU")
            return self.devices['cpu']['device']
        
        # Select device with highest score
        best_device = max(suitable_devices, key=lambda x: x[2])
        return best_device[1]
    
    def _get_available_memory(self, device_name: str) -> float:
        """Get available memory for device in GB"""
        device_info = self.devices[device_name]
        total_memory = device_info['memory_gb']
        
        if device_name.startswith('cuda'):
            gpu_id = int(device_name.split(':')[1])
            allocated = torch.cuda.memory_allocated(gpu_id) / 1e9
            return total_memory - allocated
        else:
            # For CPU/MPS, assume 80% available
            return total_memory * 0.8
    
    def get_model_device(self, model_name: str) -> torch.device:
        """Get current device assignment for model"""
        return self.device_assignments.get(model_name, self.primary_device)
    
    def load_model(self, model_name: str) -> Any:
        """Load model to its assigned device"""
        if model_name not in self.model_registry:
            raise ValueError(f"Model '{model_name}' not registered")
        
        # Check if model is offloaded
        if model_name in self.offloaded_models:
            model = self.offloaded_models.pop(model_name)
            self.model_cache[model_name] = model
            self.logger.info(f"Loaded model '{model_name}' from CPU")
        
        model = self.model_cache[model_name]
        target_device = self.device_assignments[model_name]
        
        # Move to target device if needed
        if hasattr(model, 'device') and model.device != target_device:
            start_time = time.time()
            model = model.to(target_device)
            load_time = time.time() - start_time
            
            if self.performance_tracking:
                self._record_performance(f"{model_name}_load", load_time)
            
            self.logger.info(f"Moved model '{model_name}' to {target_device} in {load_time:.2f}s")
        
        return model
    
    def offload_model(self, model_name: str) -> None:
        """Offload model to CPU to free GPU memory"""
        if not self.enable_offloading:
            return
        
        if model_name not in self.model_cache:
            return
        
        model = self.model_cache[model_name]
        profile = self.model_registry[model_name]
        
        # Only offload if model can be offloaded and is on GPU
        if profile.can_offload and hasattr(model, 'device') and model.device.type != 'cpu':
            self.offload_queue.put((model_name, model))
            self.logger.info(f"Queued model '{model_name}' for offloading")
    
    def _offload_worker(self):
        """Background worker for model offloading"""
        while True:
            try:
                model_name, model = self.offload_queue.get(timeout=1.0)
                
                start_time = time.time()
                cpu_model = model.to('cpu')
                offload_time = time.time() - start_time
                
                self.offloaded_models[model_name] = cpu_model
                if model_name in self.model_cache:
                    del self.model_cache[model_name]
                
                # Clear GPU cache
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                self.logger.info(f"Offloaded model '{model_name}' to CPU in {offload_time:.2f}s")
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Error in offload worker: {e}")
    
    def optimize_memory_usage(self) -> None:
        """Optimize memory usage across all devices"""
        for device_name, device_info in self.devices.items():
            if not device_name.startswith('cuda'):
                continue
            
            gpu_id = int(device_name.split(':')[1])
            allocated = torch.cuda.memory_allocated(gpu_id)
            total = torch.cuda.get_device_properties(gpu_id).total_memory
            usage_ratio = allocated / total
            
            if usage_ratio > self.memory_threshold:
                self.logger.info(f"GPU {gpu_id} memory usage {usage_ratio:.1%} > threshold")
                
                # Find models to offload (lowest priority first)
                models_on_device = [
                    (name, profile) for name, profile in self.model_registry.items()
                    if self.device_assignments[name].index == gpu_id
                ]
                
                models_to_offload = sorted(models_on_device, key=lambda x: x[1].priority, reverse=True)
                
                for model_name, profile in models_to_offload:
                    if usage_ratio <= self.memory_threshold:
                        break
                    
                    self.offload_model(model_name)
                    usage_ratio -= profile.memory_gb / (total / 1e9)
    
    def _record_performance(self, operation: str, time_taken: float):
        """Record performance metrics"""
        if operation not in self.performance_history:
            self.performance_history[operation] = []
        
        self.performance_history[operation].append(time_taken)
        
        # Keep only recent history
        if len(self.performance_history[operation]) > 100:
            self.performance_history[operation] = self.performance_history[operation][-100:]
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        report = {
            'device_assignments': {name: str(device) for name, device in self.device_assignments.items()},
            'memory_usage': {},
            'performance_metrics': {},
            'recommendations': []
        }
        
        # Memory usage per device
        for device_name, device_info in self.devices.items():
            if device_name.startswith('cuda'):
                gpu_id = int(device_name.split(':')[1])
                allocated = torch.cuda.memory_allocated(gpu_id) / 1e9
                total = device_info['memory_gb']
                report['memory_usage'][device_name] = {
                    'allocated_gb': allocated,
                    'total_gb': total,
                    'usage_percent': (allocated / total) * 100
                }
        
        # Performance metrics
        for operation, times in self.performance_history.items():
            if times:
                report['performance_metrics'][operation] = {
                    'avg_time': sum(times) / len(times),
                    'min_time': min(times),
                    'max_time': max(times),
                    'sample_count': len(times)
                }
        
        # Generate recommendations
        report['recommendations'] = self._generate_recommendations()
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate performance optimization recommendations"""
        recommendations = []
        
        # Check for underutilized GPUs
        cuda_devices = [name for name in self.devices.keys() if name.startswith('cuda')]
        if len(cuda_devices) > 1:
            utilization = []
            for device_name in cuda_devices:
                models_on_device = sum(1 for d in self.device_assignments.values() if str(d) == device_name)
                utilization.append(models_on_device)
            
            if max(utilization) - min(utilization) > 1:
                recommendations.append("Consider redistributing models for better GPU load balancing")
        
        # Check memory usage
        for device_name in cuda_devices:
            gpu_id = int(device_name.split(':')[1])
            allocated = torch.cuda.memory_allocated(gpu_id)
            total = torch.cuda.get_device_properties(gpu_id).total_memory
            usage_ratio = allocated / total
            
            if usage_ratio < 0.3:
                recommendations.append(f"GPU {gpu_id} is underutilized - consider consolidating models")
            elif usage_ratio > 0.9:
                recommendations.append(f"GPU {gpu_id} memory near limit - consider offloading or using FP16")
        
        # Performance recommendations
        if self.performance_history:
            avg_load_time = sum(
                sum(times) / len(times) for op, times in self.performance_history.items()
                if 'load' in op and times
            ) / max(1, len([op for op in self.performance_history.keys() if 'load' in op]))
            
            if avg_load_time > 2.0:
                recommendations.append("Model loading is slow - consider keeping models in GPU memory")
        
        return recommendations


# Global scheduler instance
_scheduler = None

def get_optimal_scheduler(**kwargs) -> OptimalDeviceScheduler:
    """Get global optimal scheduler instance"""
    global _scheduler
    if _scheduler is None:
        _scheduler = OptimalDeviceScheduler(**kwargs)
    return _scheduler
