name: mini
channels:
  - pytorch
  - conda-forge
  - defaults
dependencies:
  # Core Python and CUDA
  - python=3.8.5
  - pip=20.3
  - cudatoolkit=11.3
  
  # Core PyTorch ecosystem (compatible versions)
  - pytorch=1.12.1
  - torchvision=0.13.1
  - torchaudio=0.12.1
  
  # Core scientific computing
  - numpy=1.23.1
  - scipy=1.9.1
  - matplotlib=3.5.3
  - pillow=9.2.0
  - scikit-image=0.19.3
  
  # Computer vision and image processing
  - opencv=4.6.0
  - imageio=2.21.1
  
  # Development tools
  - jupyter=1.0.0
  - pytest=7.1.2
  
  # Pip dependencies (core only)
  - pip:
      # Deep learning frameworks (compatible versions)
      - pytorch-lightning==1.5.0
      - transformers==4.21.3
      - diffusers==0.15.1
      
      # UI framework
      - gradio==3.31.0
      
      # Configuration
      - omegaconf==2.1.1
      
      # Computer vision
      - kornia==0.6.7
      - albumentations==1.3.0
      
      # Evaluation metrics
      - lpips==0.1.4
      - pytorch-fid==0.3.0
      
      # Logging (optional)
      - wandb==0.13.10
      - tensorboard==2.10.0
      
      # Utilities
      - tqdm==4.64.0
      - einops==0.6.1
      - requests==2.28.1
      
      # NLP for text conditioning
      - sentence-transformers==2.2.2
      
      # Additional utilities
      - rich==12.5.1
      - click==8.1.3
