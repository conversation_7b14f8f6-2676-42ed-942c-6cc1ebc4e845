name: full
channels:
  - pytorch
  - conda-forge
  - defaults
dependencies:
  # Core Python and CUDA
  - python=3.8.5
  - pip=20.3
  - cudatoolkit=11.3

  # Core PyTorch ecosystem (compatible versions)
  - pytorch=1.12.1
  - torchvision=0.13.1
  - torchaudio=0.12.1

  # Core scientific computing
  - numpy=1.23.1
  - scipy=1.9.1
  - matplotlib=3.5.3
  - seaborn=0.11.2
  - pillow=9.2.0
  - scikit-image=0.19.3
  - scikit-learn=1.1.2

  # Computer vision and image processing
  - opencv=4.6.0
  - imageio=2.21.1
  - albumentations=1.3.0

  # Development and utilities
  - jupyter=1.0.0
  - ipython=8.4.0
  - pytest=7.1.2
  - black=22.6.0
  - flake8=5.0.4

  # System utilities
  - git=2.37.1
  - ffmpeg=4.4.2

  # Pip dependencies (specific versions to avoid conflicts)
  - pip:
      # Deep learning frameworks
      - pytorch-lightning==1.5.0
      - transformers==4.21.3
      - diffusers==0.15.1
      - accelerate==0.20.3

      # CtrlColor specific dependencies
      - gradio==3.31.0
      - gradio-client==0.2.5
      - omegaconf==2.1.1
      - kornia==0.6.7
      - open_clip_torch==2.0.2
      - invisible-watermark==0.1.5
      - streamlit-drawable-canvas==0.8.0
      - torchmetrics==0.6.0
      - addict==2.4.0
      - basicsr==1.4.2

      # Evaluation metrics
      - lpips==0.1.4
      - pytorch-fid==0.3.0

      # Logging and experiment tracking
      - wandb==0.13.10
      - tensorboard==2.10.0
      - test-tube==0.7.5

      # Video processing
      - imageio-ffmpeg==0.4.7
      - moviepy==1.0.3

      # Feature matching (optional - skip if not available)
      # - lightglue==0.0.1

      # Web framework
      - fastapi==0.92.0
      - uvicorn==0.20.0
      - streamlit==1.12.1

      # Data processing
      - webdataset==0.2.5
      - datasets==2.4.0
      - pycocotools==2.0.4

      # Utilities
      - prettytable==3.6.0
      - yapf==0.32.0
      - tqdm==4.64.0
      - einops==0.6.1
      - timm==0.6.7

      # NLP (for text conditioning)
      - spacy==3.4.1
      - nltk==3.7
      - sentence-transformers==2.2.2

      # Type checking and validation
      - pydantic==1.10.5
      - typer==0.7.0
      - typing-extensions==4.4.0

      # Network and communication
      - grpcio==1.48.2
      - requests==2.28.1
      - aiohttp==3.8.1

      # Image and color processing (optional)
      # - colorspacious==1.1.2
      # - colour-science==0.4.1

      # Optimization
      - optuna==3.0.1
      - hyperopt==0.2.7

      # Reproducibility
      - sacred==0.8.2
      - mlflow==1.28.0

      # Additional utilities
      - rich==12.5.1
      - click==8.1.3
      - pathlib2==2.3.7
      - psutil==5.9.1
      - memory-profiler==0.60.0
