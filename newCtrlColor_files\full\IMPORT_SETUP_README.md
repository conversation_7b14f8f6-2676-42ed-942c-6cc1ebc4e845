# Universal Import Configuration for CtrlColor Full

This document explains how to use the universal import configuration to resolve all import issues in the `full` folder.

## 🎯 **Problem Solved**

The `full` folder needs to import external components from the main CtrlColor codebase (like `cldm.cldm` and `ldm.modules.diffusionmodules.util`), but Python can't find them by default because they're in the parent directory.

## 🚀 **Universal Solutions**

### **Method 1: Simple Import Config (Recommended)**

Add this **single line** at the top of any Python file in the `full` folder:

```python
import import_config  # That's it! All imports are now fixed.

# Now you can use external imports:
from cldm.cldm import ControlLDM
from ldm.modules.diffusionmodules.util import timestep_embedding
```

### **Method 2: Advanced Setup**

For more control and verbose output:

```python
from setup_imports import setup_universal_imports
setup_universal_imports(verbose=True)  # Shows what's being configured

# Now external imports work:
from cldm.cldm import ControlLDM
from ldm.modules.diffusionmodules.util import timestep_embedding
```

### **Method 3: Package-Level Auto-Setup**

When importing the `full` package from outside, imports are automatically configured:

```python
# From parent directory:
import full  # Auto-configures all imports

# Or from within full folder:
import full  # Also works
```

## 📁 **What Gets Fixed**

The universal configuration automatically resolves imports for:

### **External CLDM Components:**
- `cldm.cldm.ControlLDM`
- `cldm.cldm.ControlNet`
- `cldm.cldm.ControlledUnetModel`

### **External LDM Components:**
- `ldm.modules.diffusionmodules.util.*`
- `ldm.modules.encoders.modules.FrozenCLIPEmbedder`
- `ldm.models.diffusion.ddpm.LatentDiffusion`
- `ldm.util.instantiate_from_config`

### **Internal Full Folder Components:**
- `losses.contextual_loss.ContextualLoss`
- `modules.exemplar_processor.ExemplarProcessor`
- `cldm.exemplar_cldm.ExemplarControlLDM`
- `data.data_processor.LabColorProcessor`
- All other internal modules

## 🔧 **How It Works**

1. **Path Resolution**: Automatically adds the project root to `sys.path`
2. **Import Verification**: Tests that critical external imports work
3. **Compatibility Check**: Ensures internal modules can use external dependencies
4. **Error Handling**: Graceful fallbacks if external modules aren't available

## 📋 **Usage Examples**

### **In a Loss Function:**
```python
# losses/my_new_loss.py
import import_config  # Universal import fix

from cldm.cldm import ControlLDM  # External import
from ldm.modules.diffusionmodules.util import timestep_embedding  # External import
from .contextual_loss import ContextualLoss  # Internal import

class MyNewLoss(nn.Module):
    def __init__(self):
        super().__init__()
        self.contextual_loss = ContextualLoss()
    
    def forward(self, x, y):
        # Can use external utilities
        embeddings = timestep_embedding(torch.tensor([100]), 320)
        return self.contextual_loss(x, y)
```

### **In a Model:**
```python
# cldm/my_model.py
import import_config  # Universal import fix

from cldm.cldm import ControlLDM  # External base class
from ldm.util import instantiate_from_config  # External utility

class MyModel(ControlLDM):  # Inherits from external class
    def __init__(self, config):
        # Can use external utilities
        processed_config = instantiate_from_config(config)
        super().__init__(**processed_config)
```

### **In a Training Script:**
```python
# training/my_trainer.py
import import_config  # Universal import fix

from cldm.cldm import ControlLDM  # External import
from ldm.models.diffusion.ddpm import LatentDiffusion  # External import
from ..modules.exemplar_processor import ExemplarProcessor  # Internal import

def train_model():
    model = ControlLDM(...)  # Uses external class
    processor = ExemplarProcessor()  # Uses internal class
    # Training logic here
```

## ✅ **Testing**

To verify the universal import configuration is working:

```bash
cd full/
python test_universal_imports.py
```

This will test all import methods and show you exactly what's working.

## 🎯 **Benefits**

1. **Universal**: Works for all files in the `full` folder
2. **Simple**: Just one import line needed
3. **Automatic**: No manual path configuration required
4. **Robust**: Handles missing dependencies gracefully
5. **Compatible**: Works with the entire CtrlColor codebase
6. **Tested**: Comprehensive test suite verifies functionality

## 🚨 **Important Notes**

- Always add the import config **before** any external imports
- The configuration is automatically applied when importing the `full` package
- External dependencies must exist in the parent directory structure
- Internal imports within the `full` folder work normally

## 📞 **Troubleshooting**

If imports still fail:

1. **Check file location**: Make sure you're in the `full` folder
2. **Verify parent structure**: Ensure `cldm` and `ldm` folders exist in parent directory
3. **Run test**: Execute `python test_universal_imports.py` to diagnose issues
4. **Check verbose output**: Use `setup_universal_imports(verbose=True)` for detailed info

---

**Result**: The `full` folder is now fully compatible with the entire CtrlColor codebase! 🎉
