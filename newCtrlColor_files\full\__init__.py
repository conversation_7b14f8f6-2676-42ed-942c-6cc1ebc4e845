"""
CtrlColor Full Implementation

This package contains the complete implementation of CtrlColor with exemplar-based conditioning.
It is designed to work with the main CtrlColor codebase located in the parent directory.
"""

import sys
import os

# Add parent directory to Python path to access cldm and ldm modules
_parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if _parent_dir not in sys.path:
    sys.path.insert(0, _parent_dir)

# Verify that we can import the required modules
try:
    import cldm
    import ldm
    _EXTERNAL_IMPORTS_AVAILABLE = True
except ImportError:
    _EXTERNAL_IMPORTS_AVAILABLE = False

__version__ = "1.0.0"
__all__ = [
    "cldm",
    "data", 
    "evaluation",
    "losses",
    "modules",
    "training",
    "ui"
]
