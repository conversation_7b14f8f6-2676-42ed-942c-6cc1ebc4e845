

"""

Extended ControlLDM with Exemplar Support for CtrlColor



Extends the original ControlLDM to support exemplar-based colorization:

- Integrates CLIP image encoder for exemplar processing

- Adds exemplar conditioning to cross-attention

- Supports multi-modal conditioning (text + stroke + exemplar)



Reference: CtrlColor paper Section 3.3 Exemplar Control

"""



from typing import Any, Dict, List, Optional



import torch

import torch.nn as nn



try:

    from cldm.cldm import ControlLDM, ControlNet

    from ldm.modules.encoders.modules import FrozenCLIPEmbedder

except ImportError:

    print("Warning: Original ControlLDM not found, using dummy base class")

    ControlLDM = nn.Module

    ControlNet = nn.Module

    FrozenCLIPEmbedder = nn.Module





class ExemplarControlLDM(ControlLDM):

    """

    Extended ControlLDM with Exemplar Support



    Adds exemplar-based conditioning to the original ControlLDM:

    - CLIP image encoder for exemplar features

    - Multi-modal conditioning fusion

    - Exemplar-guided cross-attention

    """



    def __init__(

        self,

        # Original ControlLDM parameters

        unet_config: dict,

        control_stage_config: dict,

        control_key: str,

        only_mid_control: bool = False,

        learning_rate: float = 1e-4,

        # Exemplar-specific parameters

        exemplar_config: Optional[dict] = None,

        exemplar_weight: float = 1.0,

        fusion_strategy: str = "concat",

        **kwargs,

    ):

        """

        Initialize ExemplarControlLDM



        Args:

            exemplar_config: Configuration for exemplar processing

            exemplar_weight: Weight for exemplar conditioning

            fusion_strategy: Strategy for fusing text and exemplar features

                - 'concat': Concatenate features

                - 'add': Add features

                - 'cross_attention': Use cross-attention fusion

        """

        # Initialize base ControlLDM

        super().__init__(

            unet_config=unet_config,

            control_stage_config=control_stage_config,

            control_key=control_key,

            only_mid_control=only_mid_control,

            learning_rate=learning_rate,

            **kwargs,

        )



        self.exemplar_weight = exemplar_weight

        self.fusion_strategy = fusion_strategy



        # Initialize exemplar processing components

        if exemplar_config is None:

            exemplar_config = {

                "clip_model_name": "openai/clip-vit-base-patch32",

                "feature_dim": 512,

                "max_colors": 16,

            }



        self.exemplar_processor = ExemplarProcessor(**exemplar_config)



        # Initialize exemplar conditioner

        condition_dim = 768  # CLIP text embedding dimension

        self.exemplar_conditioner = ExemplarConditioner(

            exemplar_dim=exemplar_config["feature_dim"], condition_dim=condition_dim

        )



        # Fusion network for multi-modal conditioning

        if fusion_strategy == "cross_attention":

            self.fusion_attention = nn.MultiheadAttention(

                embed_dim=condition_dim, num_heads=8, dropout=0.1, batch_first=True

            )

        elif fusion_strategy == "concat":

            self.fusion_projection = nn.Linear(condition_dim * 2, condition_dim)



    def get_learned_conditioning(self, c: List[str]) -> torch.Tensor:

        """

        Get learned conditioning from text prompts



        Args:

            c: List of text prompts



        Returns:

            Text conditioning tensor

        """

        if hasattr(self, "cond_stage_model") and self.cond_stage_model is not None:

            return self.cond_stage_model.encode(c)

        else:

            # Fallback: create dummy conditioning

            batch_size = len(c) if isinstance(c, list) else 1

            return torch.randn(batch_size, 77, 768, device=self.device)



    def process_exemplar_conditioning(

        self, exemplar_images: Optional[torch.Tensor], text_conditioning: torch.Tensor

    ) -> torch.Tensor:

        """

        Process exemplar images and fuse with text conditioning



        Args:

            exemplar_images: Exemplar images [B, 3, H, W] or None

            text_conditioning: Text conditioning [B, seq_len, dim]



        Returns:

            Fused conditioning tensor

        """

        if exemplar_images is None:

            return text_conditioning



        # Process exemplar images

        exemplar_result = self.exemplar_processor(exemplar_images)

        exemplar_features = exemplar_result["clip_features"]



        # Convert to conditioning format

        exemplar_conditioning = self.exemplar_conditioner(exemplar_features)



        # Fuse text and exemplar conditioning

        if self.fusion_strategy == "concat":

            # Concatenate along sequence dimension

            fused_conditioning = torch.cat(

                [text_conditioning, exemplar_conditioning], dim=1

            )

            # Project back to original dimension

            if hasattr(self, "fusion_projection"):

                # Reshape for projection

                B, seq_len, dim = fused_conditioning.shape

                fused_conditioning = fused_conditioning.view(B * seq_len, dim)

                fused_conditioning = self.fusion_projection(fused_conditioning)

                fused_conditioning = fused_conditioning.view(B, seq_len, -1)



        elif self.fusion_strategy == "add":

            # Add exemplar conditioning to text conditioning (broadcast)

            exemplar_expanded = exemplar_conditioning.expand(

                -1, text_conditioning.shape[1], -1

            )

            fused_conditioning = (

                text_conditioning + self.exemplar_weight * exemplar_expanded

            )



        elif self.fusion_strategy == "cross_attention":

            # Use cross-attention to fuse features

            fused_conditioning, _ = self.fusion_attention(

                query=text_conditioning,

                key=exemplar_conditioning,

                value=exemplar_conditioning,

            )

            # Add residual connection

            fused_conditioning = (

                text_conditioning + self.exemplar_weight * fused_conditioning

            )



        else:

            raise ValueError(f"Unknown fusion strategy: {self.fusion_strategy}")



        return fused_conditioning



    def apply_model(

        self, x_noisy: torch.Tensor, t: torch.Tensor, cond: Dict[str, Any]

    ) -> torch.Tensor:

        """

        Apply the model with exemplar conditioning support



        Args:

            x_noisy: Noisy latent tensor

            t: Timestep tensor

            cond: Conditioning dictionary containing:

                - c_concat: Control inputs (stroke masks, etc.)

                - c_crossattn: Text conditioning

                - c_exemplar: Exemplar images (optional)



        Returns:

            Predicted noise

        """

        # Extract conditioning components

        control_inputs = cond.get("c_concat", None)

        text_prompts = cond.get("c_crossattn", [])

        exemplar_images = cond.get("c_exemplar", None)



        # Get text conditioning

        if isinstance(text_prompts, list) and len(text_prompts) > 0:

            text_conditioning = self.get_learned_conditioning(text_prompts)

        else:

            # Create dummy text conditioning

            batch_size = x_noisy.shape[0]

            text_conditioning = torch.randn(batch_size, 77, 768, device=x_noisy.device)



        # Process and fuse exemplar conditioning

        fused_conditioning = self.process_exemplar_conditioning(

            exemplar_images, text_conditioning

        )



        # Apply ControlNet if control inputs provided

        if control_inputs is not None and hasattr(self, "control_model"):

            control_outputs = self.control_model(

                x=x_noisy, hint=control_inputs, timesteps=t, context=fused_conditioning

            )

        else:

            control_outputs = None



        # Apply main UNet with fused conditioning

        if hasattr(self, "model") and hasattr(self.model, "diffusion_model"):

            noise_pred = self.model.diffusion_model(

                x=x_noisy,

                timesteps=t,

                context=fused_conditioning,

                control=control_outputs,

            )

        else:

            # Fallback for testing

            noise_pred = torch.randn_like(x_noisy)



        return noise_pred



    def forward(

        self,

        x: torch.Tensor,

        t: torch.Tensor,

        c_concat: Optional[torch.Tensor] = None,

        c_crossattn: Optional[List[str]] = None,

        c_exemplar: Optional[torch.Tensor] = None,

        **kwargs,

    ) -> torch.Tensor:

        """

        Forward pass with exemplar support



        Args:

            x: Input latent tensor

            t: Timestep tensor

            c_concat: Control inputs (stroke masks, etc.)

            c_crossattn: Text prompts

            c_exemplar: Exemplar images



        Returns:

            Model output

        """

        # Prepare conditioning dictionary

        cond = {

            "c_concat": c_concat,

            "c_crossattn": c_crossattn or [],

            "c_exemplar": c_exemplar,

        }



        return self.apply_model(x, t, cond)



    def get_unconditional_conditioning(self, batch_size: int) -> torch.Tensor:

        """Get unconditional conditioning for classifier-free guidance"""

        # Create empty text conditioning

        empty_prompts = [""] * batch_size

        return self.get_learned_conditioning(empty_prompts)



    def configure_optimizers(self):

        """Configure optimizers for training"""

        # Get parameters from all components

        params = list(self.parameters())



        # Add exemplar processor parameters if trainable

        if hasattr(self.exemplar_processor, "feature_projection"):

            params.extend(list(self.exemplar_processor.feature_projection.parameters()))



        optimizer = torch.optim.AdamW(params, lr=self.learning_rate)

        return optimizer





# Test function

def test_exemplar_cldm():

    """Test ExemplarControlLDM implementation"""

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")



    print("Testing ExemplarControlLDM...")



    # Create dummy configurations

    unet_config = {"in_channels": 4, "out_channels": 4}

    control_config = {"in_channels": 4}



    try:

        # Initialize model

        model = ExemplarControlLDM(

            unet_config=unet_config,

            control_stage_config=control_config,

            control_key="hint",

        ).to(device)



        # Create test inputs

        batch_size = 2

        x = torch.randn(batch_size, 4, 32, 32).to(device)

        t = torch.randint(0, 1000, (batch_size,)).to(device)

        c_concat = torch.randn(batch_size, 4, 32, 32).to(device)

        c_crossattn = ["a red car", "a blue house"]

        c_exemplar = torch.randn(batch_size, 3, 256, 256).to(device)



        # Test forward pass

        output = model(

            x=x, t=t, c_concat=c_concat, c_crossattn=c_crossattn, c_exemplar=c_exemplar

        )



        print(f"Output shape: {output.shape}")

        print("ExemplarControlLDM test passed!")



        return model, output



    except Exception as e:

        print(f"ExemplarControlLDM test failed: {e}")

        return None, None





if __name__ == "__main__":

    test_exemplar_cldm()

