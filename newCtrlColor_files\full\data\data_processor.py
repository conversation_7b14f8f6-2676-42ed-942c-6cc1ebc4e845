

"""

Data Processing Pipeline for CtrlColor Training



Implements data processing components mentioned in the paper:

- SLIC superpixel generation for stroke simulation

- Color jittering for hint robustness

- Image preprocessing and augmentation

- Lab color space conversions



Reference: CtrlColor paper Section 4.1 Implementation Details

"""



import torch

import torch.nn.functional as F

import numpy as np

import cv2

from typing import Tuple, Optional, List

from PIL import Image

import random



try:

    from skimage.segmentation import slic

    from skimage.color import rgb2lab, lab2rgb

    SKIMAGE_AVAILABLE = True

except ImportError:

    SKIMAGE_AVAILABLE = False

    print("Warning: scikit-image not available, using fallback implementations")





class SLICProcessor:

    """

    SLIC Superpixel Processor for stroke simulation

    

    Generates superpixel segmentation to simulate realistic stroke patterns

    as mentioned in the paper's training methodology

    """

    

    def __init__(self,

                 n_segments: int = 100,

                 compactness: float = 10.0,

                 sigma: float = 1.0,

                 min_size_factor: float = 0.01):

        """

        Initialize SLIC processor

        

        Args:

            n_segments: Number of superpixel segments

            compactness: Compactness parameter for SLIC

            sigma: Gaussian smoothing parameter

            min_size_factor: Minimum segment size as fraction of image

        """

        self.n_segments = n_segments

        self.compactness = compactness

        self.sigma = sigma

        self.min_size_factor = min_size_factor

    

    def generate_superpixels(self, image: np.ndarray) -> np.ndarray:

        """

        Generate SLIC superpixel segmentation

        

        Args:

            image: RGB image [H, W, 3] in range [0, 255]

            

        Returns:

            Superpixel labels [H, W]

        """

        if not SKIMAGE_AVAILABLE:

            return self._fallback_superpixels(image)

        

        # Convert to float and normalize

        image_float = image.astype(np.float32) / 255.0

        

        # Generate SLIC superpixels

        segments = slic(

            image_float,

            n_segments=self.n_segments,

            compactness=self.compactness,

            sigma=self.sigma,

            start_label=1

        )

        

        # Remove small segments

        min_size = int(image.shape[0] * image.shape[1] * self.min_size_factor)

        segments = self._remove_small_segments(segments, min_size)

        

        return segments

    

    def _fallback_superpixels(self, image: np.ndarray) -> np.ndarray:

        """Fallback superpixel generation using watershed"""

        # Simple grid-based segmentation as fallback

        h, w = image.shape[:2]

        grid_h = int(np.sqrt(self.n_segments * h / w))

        grid_w = int(np.sqrt(self.n_segments * w / h))

        

        segments = np.zeros((h, w), dtype=np.int32)

        segment_id = 1

        

        for i in range(grid_h):

            for j in range(grid_w):

                y1 = i * h // grid_h

                y2 = (i + 1) * h // grid_h

                x1 = j * w // grid_w

                x2 = (j + 1) * w // grid_w

                

                segments[y1:y2, x1:x2] = segment_id

                segment_id += 1

        

        return segments

    

    def _remove_small_segments(self, segments: np.ndarray, min_size: int) -> np.ndarray:

        """Remove segments smaller than min_size"""

        unique_labels, counts = np.unique(segments, return_counts=True)

        

        for label, count in zip(unique_labels, counts):

            if count < min_size:

                # Find neighboring segments and merge

                mask = segments == label

                # Simple approach: assign to most frequent neighbor

                neighbors = []

                for dy, dx in [(-1, 0), (1, 0), (0, -1), (0, 1)]:

                    shifted = np.roll(np.roll(segments, dy, axis=0), dx, axis=1)

                    neighbor_labels = shifted[mask]

                    neighbors.extend(neighbor_labels[neighbor_labels != label])

                

                if neighbors:

                    most_frequent = max(set(neighbors), key=neighbors.count)

                    segments[mask] = most_frequent

        

        return segments

    

    def generate_stroke_mask(self, 

                           segments: np.ndarray,

                           stroke_ratio: float = 0.1) -> np.ndarray:

        """

        Generate stroke mask from superpixel segmentation

        

        Args:

            segments: Superpixel labels [H, W]

            stroke_ratio: Fraction of segments to include in stroke

            

        Returns:

            Binary stroke mask [H, W]

        """

        unique_labels = np.unique(segments)

        n_stroke_segments = max(1, int(len(unique_labels) * stroke_ratio))

        

        # Randomly select segments for stroke

        stroke_labels = np.random.choice(

            unique_labels, 

            size=n_stroke_segments, 

            replace=False

        )

        

        # Create binary mask

        stroke_mask = np.isin(segments, stroke_labels).astype(np.uint8)

        

        return stroke_mask





class ColorJitterer:

    """

    Color Jittering for hint robustness

    

    Implements color jittering mentioned in the paper (20% of hint images)

    to improve model robustness to color variations

    """

    

    def __init__(self,

                 brightness: float = 0.2,

                 contrast: float = 0.2,

                 saturation: float = 0.2,

                 hue: float = 0.1,

                 jitter_probability: float = 0.2):

        """

        Initialize color jitterer

        

        Args:

            brightness: Brightness jittering range

            contrast: Contrast jittering range  

            saturation: Saturation jittering range

            hue: Hue jittering range

            jitter_probability: Probability of applying jittering

        """

        self.brightness = brightness

        self.contrast = contrast

        self.saturation = saturation

        self.hue = hue

        self.jitter_probability = jitter_probability

    

    def apply_jitter(self, image: torch.Tensor) -> torch.Tensor:

        """

        Apply color jittering to image

        

        Args:

            image: RGB image tensor [3, H, W] or [B, 3, H, W] in range [0, 1]

            

        Returns:

            Jittered image tensor

        """

        if random.random() > self.jitter_probability:

            return image

        

        # Convert to PIL for torchvision transforms

        if len(image.shape) == 4:

            # Batch processing

            jittered_images = []

            for i in range(image.shape[0]):

                jittered = self._jitter_single_image(image[i])

                jittered_images.append(jittered)

            return torch.stack(jittered_images, dim=0)

        else:

            return self._jitter_single_image(image)

    

    def _jitter_single_image(self, image: torch.Tensor) -> torch.Tensor:

        """Apply jittering to single image"""

        # Convert to numpy for OpenCV processing

        img_np = (image.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)

        

        # Apply brightness jittering

        if self.brightness > 0:

            brightness_factor = 1.0 + random.uniform(-self.brightness, self.brightness)

            img_np = np.clip(img_np * brightness_factor, 0, 255).astype(np.uint8)

        

        # Apply contrast jittering

        if self.contrast > 0:

            contrast_factor = 1.0 + random.uniform(-self.contrast, self.contrast)

            mean = img_np.mean()

            img_np = np.clip((img_np - mean) * contrast_factor + mean, 0, 255).astype(np.uint8)

        

        # Apply saturation jittering in HSV space

        if self.saturation > 0 or self.hue > 0:

            img_hsv = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV).astype(np.float32)

            

            if self.saturation > 0:

                sat_factor = 1.0 + random.uniform(-self.saturation, self.saturation)

                img_hsv[:, :, 1] = np.clip(img_hsv[:, :, 1] * sat_factor, 0, 255)

            

            if self.hue > 0:

                hue_shift = random.uniform(-self.hue, self.hue) * 180

                img_hsv[:, :, 0] = (img_hsv[:, :, 0] + hue_shift) % 180

            

            img_np = cv2.cvtColor(img_hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)

        

        # Convert back to tensor

        img_tensor = torch.from_numpy(img_np).float() / 255.0

        img_tensor = img_tensor.permute(2, 0, 1)

        

        return img_tensor





class LabColorProcessor:

    """

    Lab Color Space Processor

    

    Handles conversions between RGB and Lab color spaces

    as used throughout the CtrlColor pipeline

    """

    

    @staticmethod

    def rgb_to_lab(rgb_image: torch.Tensor) -> torch.Tensor:

        """

        Convert RGB to Lab color space

        

        Args:

            rgb_image: RGB image [B, 3, H, W] or [3, H, W] in range [0, 1]

            

        Returns:

            Lab image [B, 3, H, W] or [3, H, W]

        """

        if not SKIMAGE_AVAILABLE:

            return LabColorProcessor._rgb_to_lab_manual(rgb_image)

        

        # Handle batch dimension

        single_image = len(rgb_image.shape) == 3

        if single_image:

            rgb_image = rgb_image.unsqueeze(0)

        

        batch_size = rgb_image.shape[0]

        lab_images = []

        

        for i in range(batch_size):

            # Convert to numpy

            rgb_np = rgb_image[i].permute(1, 2, 0).cpu().numpy()

            

            # Convert to Lab

            lab_np = rgb2lab(rgb_np)

            

            # Normalize L channel to [0, 1] and ab channels to [-1, 1]

            lab_np[:, :, 0] = lab_np[:, :, 0] / 100.0  # L: [0, 100] -> [0, 1]

            lab_np[:, :, 1:] = lab_np[:, :, 1:] / 128.0  # ab: [-128, 127] -> [-1, 1]

            

            # Convert back to tensor

            lab_tensor = torch.from_numpy(lab_np).permute(2, 0, 1).float()

            lab_images.append(lab_tensor)

        

        lab_batch = torch.stack(lab_images, dim=0)

        

        if single_image:

            lab_batch = lab_batch.squeeze(0)

        

        return lab_batch

    

    @staticmethod

    def lab_to_rgb(lab_image: torch.Tensor) -> torch.Tensor:

        """

        Convert Lab to RGB color space

        

        Args:

            lab_image: Lab image [B, 3, H, W] or [3, H, W]

            

        Returns:

            RGB image [B, 3, H, W] or [3, H, W] in range [0, 1]

        """

        if not SKIMAGE_AVAILABLE:

            return LabColorProcessor._lab_to_rgb_manual(lab_image)

        

        # Handle batch dimension

        single_image = len(lab_image.shape) == 3

        if single_image:

            lab_image = lab_image.unsqueeze(0)

        

        batch_size = lab_image.shape[0]

        rgb_images = []

        

        for i in range(batch_size):

            # Convert to numpy

            lab_np = lab_image[i].permute(1, 2, 0).cpu().numpy()

            

            # Denormalize Lab values

            lab_np[:, :, 0] = lab_np[:, :, 0] * 100.0  # L: [0, 1] -> [0, 100]

            lab_np[:, :, 1:] = lab_np[:, :, 1:] * 128.0  # ab: [-1, 1] -> [-128, 127]

            

            # Convert to RGB

            rgb_np = lab2rgb(lab_np)

            rgb_np = np.clip(rgb_np, 0, 1)

            

            # Convert back to tensor

            rgb_tensor = torch.from_numpy(rgb_np).permute(2, 0, 1).float()

            rgb_images.append(rgb_tensor)

        

        rgb_batch = torch.stack(rgb_images, dim=0)

        

        if single_image:

            rgb_batch = rgb_batch.squeeze(0)

        

        return rgb_batch

    

    @staticmethod

    def _rgb_to_lab_manual(rgb_image: torch.Tensor) -> torch.Tensor:

        """Manual RGB to Lab conversion (fallback)"""

        # Simplified conversion - not as accurate as skimage

        # This is a basic approximation

        r, g, b = rgb_image[0], rgb_image[1], rgb_image[2]

        

        # Convert to grayscale for L channel (approximation)

        l_channel = 0.299 * r + 0.587 * g + 0.114 * b

        

        # Simple approximation for a and b channels

        a_channel = (r - g) * 0.5

        b_channel = (r + g - 2 * b) * 0.25

        

        return torch.stack([l_channel, a_channel, b_channel], dim=0)

    

    @staticmethod

    def _lab_to_rgb_manual(lab_image: torch.Tensor) -> torch.Tensor:

        """Manual Lab to RGB conversion (fallback)"""

        # Simplified conversion - not as accurate as skimage

        l, a, b = lab_image[0], lab_image[1], lab_image[2]

        

        # Simple approximation

        r = l + a

        g = l - a

        b_channel = l - b

        

        # Clamp to valid range

        r = torch.clamp(r, 0, 1)

        g = torch.clamp(g, 0, 1)

        b_channel = torch.clamp(b_channel, 0, 1)

        

        return torch.stack([r, g, b_channel], dim=0)





# Test functions

def test_slic_processor():

    """Test SLIC superpixel processor"""

    print("Testing SLIC Processor...")

    

    # Create test image

    test_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)

    

    processor = SLICProcessor(n_segments=50)

    segments = processor.generate_superpixels(test_image)

    stroke_mask = processor.generate_stroke_mask(segments)

    

    print(f"Segments shape: {segments.shape}")

    print(f"Unique segments: {len(np.unique(segments))}")

    print(f"Stroke mask shape: {stroke_mask.shape}")

    print(f"Stroke coverage: {stroke_mask.mean():.2%}")





def test_color_jitterer():

    """Test color jitterer"""

    print("Testing Color Jitterer...")

    

    # Create test image

    test_image = torch.rand(3, 256, 256)

    

    jitterer = ColorJitterer(jitter_probability=1.0)  # Always apply for testing

    jittered = jitterer.apply_jitter(test_image)

    

    print(f"Original range: [{test_image.min():.3f}, {test_image.max():.3f}]")

    print(f"Jittered range: [{jittered.min():.3f}, {jittered.max():.3f}]")





def test_lab_processor():

    """Test Lab color processor"""

    print("Testing Lab Color Processor...")

    

    # Create test RGB image

    rgb_image = torch.rand(3, 64, 64)

    

    # Convert to Lab and back

    lab_image = LabColorProcessor.rgb_to_lab(rgb_image)

    rgb_reconstructed = LabColorProcessor.lab_to_rgb(lab_image)

    

    print(f"RGB shape: {rgb_image.shape}")

    print(f"Lab shape: {lab_image.shape}")

    print(f"Reconstruction error: {torch.mean((rgb_image - rgb_reconstructed) ** 2):.6f}")





if __name__ == "__main__":

    test_slic_processor()

    test_color_jitterer()

    test_lab_processor()

