"""
Memory-Efficient Training Configuration for RTX 3050 Laptop GPU

Optimized training settings for 4.3GB VRAM:
- Gradient accumulation for effective larger batch sizes
- Mixed precision training (FP16)
- Gradient checkpointing
- Dynamic loss scaling
- Memory-efficient data loading

Reference: Your RTX 3050 Laptop GPU specifications
"""

import torch
import torch.nn as nn
from torch.cuda.amp import autocast, GradScaler
from torch.utils.data import DataLoader
import pytorch_lightning as pl
from typing import Dict, Any, Optional
import os
import sys

# Add parent directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from ..training.base_trainer import BaseCtrlColorTrainer, TrainingConfig


class MemoryEfficientTrainer(BaseCtrlColorTrainer):
    """
    Memory-efficient trainer optimized for RTX 3050 Laptop GPU
    
    Optimizations:
    - Mixed precision training (FP16)
    - Gradient accumulation (effective batch size 16 with micro-batch 2)
    - Gradient checkpointing
    - Dynamic memory management
    - Optimized data loading
    """
    
    def __init__(self,
                 model: nn.Module,
                 micro_batch_size: int = 2,
                 effective_batch_size: int = 16,
                 use_fp16: bool = True,
                 gradient_checkpointing: bool = True,
                 max_memory_usage: float = 0.85,
                 **kwargs):
        """
        Initialize memory-efficient trainer
        
        Args:
            model: Model to train
            micro_batch_size: Actual batch size per forward pass
            effective_batch_size: Effective batch size via gradient accumulation
            use_fp16: Enable mixed precision training
            gradient_checkpointing: Enable gradient checkpointing
            max_memory_usage: Maximum GPU memory usage
        """
        # Calculate gradient accumulation steps
        self.micro_batch_size = micro_batch_size
        self.effective_batch_size = effective_batch_size
        self.gradient_accumulation_steps = effective_batch_size // micro_batch_size
        
        # Memory optimization settings
        self.use_fp16 = use_fp16 and torch.cuda.is_available()
        self.gradient_checkpointing = gradient_checkpointing
        self.max_memory_usage = max_memory_usage
        
        # Initialize base trainer
        super().__init__(model=model, **kwargs)
        
        # Setup mixed precision
        if self.use_fp16:
            self.scaler = GradScaler()
        
        # Setup memory optimizations
        self._setup_memory_optimizations()
        
        print(f"✅ Memory-efficient trainer initialized:")
        print(f"   - Micro batch size: {self.micro_batch_size}")
        print(f"   - Effective batch size: {self.effective_batch_size}")
        print(f"   - Gradient accumulation steps: {self.gradient_accumulation_steps}")
        print(f"   - Mixed precision: {self.use_fp16}")
        print(f"   - Gradient checkpointing: {self.gradient_checkpointing}")
    
    def _setup_memory_optimizations(self):
        """Setup memory optimizations"""
        if torch.cuda.is_available():
            # Set memory fraction
            torch.cuda.set_per_process_memory_fraction(self.max_memory_usage)
            
            # Enable cuDNN optimizations
            torch.backends.cudnn.benchmark = True
            
            # Enable gradient checkpointing if requested
            if self.gradient_checkpointing and hasattr(self.model, 'gradient_checkpointing_enable'):
                self.model.gradient_checkpointing_enable()
                print("✅ Gradient checkpointing enabled")
            
            print("✅ Memory optimizations applied")
    
    def training_step(self, batch: Dict[str, Any], batch_idx: int) -> torch.Tensor:
        """
        Memory-efficient training step with gradient accumulation
        
        Args:
            batch: Training batch
            batch_idx: Batch index
            
        Returns:
            Loss tensor
        """
        # Check if this is an accumulation step
        is_accumulating = (batch_idx + 1) % self.gradient_accumulation_steps != 0
        
        # Forward pass with mixed precision
        if self.use_fp16:
            with autocast():
                loss = self._compute_loss(batch)
        else:
            loss = self._compute_loss(batch)
        
        # Scale loss for gradient accumulation
        loss = loss / self.gradient_accumulation_steps
        
        # Backward pass
        if self.use_fp16:
            self.scaler.scale(loss).backward()
            
            # Update weights only at the end of accumulation
            if not is_accumulating:
                # Unscale gradients for clipping
                self.scaler.unscale_(self.optimizers())
                
                # Clip gradients
                if self.gradient_clip_val > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(), 
                        self.gradient_clip_val
                    )
                
                # Update weights
                self.scaler.step(self.optimizers())
                self.scaler.update()
                self.optimizers().zero_grad()
        else:
            loss.backward()
            
            if not is_accumulating:
                # Clip gradients
                if self.gradient_clip_val > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(), 
                        self.gradient_clip_val
                    )
                
                # Update weights
                self.optimizers().step()
                self.optimizers().zero_grad()
        
        # Log metrics (scale back the loss for logging)
        actual_loss = loss * self.gradient_accumulation_steps
        self.log_metrics({
            'loss': actual_loss.item(),
            'step': self.step_count,
            'memory_allocated_mb': torch.cuda.memory_allocated() / 1e6 if torch.cuda.is_available() else 0,
            'memory_cached_mb': torch.cuda.memory_reserved() / 1e6 if torch.cuda.is_available() else 0
        }, prefix='train')
        
        return actual_loss
    
    def _compute_loss(self, batch: Dict[str, Any]) -> torch.Tensor:
        """
        Compute loss for the batch
        
        Args:
            batch: Training batch
            
        Returns:
            Loss tensor
        """
        # Extract data from batch
        rgb_images = batch['rgb_image']  # [B, 3, H, W]
        l_channel = batch['l_channel']   # [B, 1, H, W]
        
        batch_size = rgb_images.shape[0]
        
        # Ensure micro batch size
        if batch_size > self.micro_batch_size:
            rgb_images = rgb_images[:self.micro_batch_size]
            l_channel = l_channel[:self.micro_batch_size]
            batch_size = self.micro_batch_size
        
        # Move to device
        rgb_images = rgb_images.to(self.device)
        l_channel = l_channel.to(self.device)
        
        # Convert to half precision if using FP16
        if self.use_fp16:
            rgb_images = rgb_images.half()
            l_channel = l_channel.half()
        
        # Simplified loss computation (replace with actual CtrlColor loss)
        # In practice, this would include:
        # 1. Encode images to latents
        # 2. Add noise according to diffusion schedule
        # 3. Predict noise with model
        # 4. Compute MSE loss
        
        # For now, use a dummy loss
        target = torch.randn_like(rgb_images)
        prediction = torch.randn_like(rgb_images)
        loss = nn.functional.mse_loss(prediction, target)
        
        return loss
    
    def on_train_batch_end(self, outputs, batch, batch_idx):
        """Called after each training batch"""
        super().on_train_batch_end(outputs, batch, batch_idx)
        
        # Clean up memory periodically
        if batch_idx % 50 == 0:
            self._cleanup_memory()
    
    def _cleanup_memory(self):
        """Clean up GPU memory"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    def configure_optimizers(self):
        """Configure optimizer with memory-efficient settings"""
        # Get trainable parameters
        params = [p for p in self.model.parameters() if p.requires_grad]
        
        # Use AdamW with lower memory usage
        optimizer = torch.optim.AdamW(
            params,
            lr=self.learning_rate,
            weight_decay=self.weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8,
            amsgrad=False  # Disable amsgrad to save memory
        )
        
        # Learning rate scheduler
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=self.max_steps,
            eta_min=self.learning_rate * 0.01
        )
        
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "interval": "step",
                "frequency": 1,
            },
        }


def create_rtx3050_training_config() -> Dict[str, TrainingConfig]:
    """
    Create optimized training configurations for RTX 3050 Laptop GPU
    
    Returns:
        Dictionary of training configurations for each stage
    """
    # Base configuration optimized for 4.3GB VRAM
    base_config = {
        'micro_batch_size': 2,
        'effective_batch_size': 8,  # Reduced from 16 for memory
        'use_fp16': True,
        'gradient_checkpointing': True,
        'max_memory_usage': 0.85,
        'gradient_clip_val': 1.0,
        'save_every_n_steps': 2500,  # More frequent saves
        'validate_every_n_steps': 500
    }
    
    configs = {
        'stage1_sd_rtx3050': TrainingConfig(
            stage_name='stage1_sd_rtx3050',
            max_steps=10000,  # Reduced from 15K for faster iteration
            learning_rate=5e-5,  # Slightly lower LR for stability
            warmup_steps=500,
            description="Stage 1: SD fine-tuning (RTX 3050 optimized)",
            **base_config
        ),
        
        'stage2_stroke_rtx3050': TrainingConfig(
            stage_name='stage2_stroke_rtx3050',
            max_steps=40000,  # Reduced from 65K
            learning_rate=1e-4,
            warmup_steps=1000,
            description="Stage 2: Stroke control (RTX 3050 optimized)",
            **base_config
        ),
        
        'stage3_exemplar_rtx3050': TrainingConfig(
            stage_name='stage3_exemplar_rtx3050',
            max_steps=60000,  # Reduced from 100K
            learning_rate=1e-4,
            warmup_steps=2000,
            description="Stage 3: Exemplar control (RTX 3050 optimized)",
            **base_config
        ),
        
        'stage4_deformable_rtx3050': TrainingConfig(
            stage_name='stage4_deformable_rtx3050',
            max_steps=6000,  # Reduced from 9K
            learning_rate=1e-4,
            warmup_steps=300,
            description="Stage 4: Deformable VAE (RTX 3050 optimized)",
            **base_config
        )
    }
    
    return configs


def create_memory_efficient_dataloader(dataset, 
                                     micro_batch_size: int = 2,
                                     num_workers: int = 2,
                                     pin_memory: bool = True) -> DataLoader:
    """
    Create memory-efficient dataloader for RTX 3050
    
    Args:
        dataset: Training dataset
        micro_batch_size: Batch size per forward pass
        num_workers: Number of data loading workers
        pin_memory: Enable pinned memory for faster GPU transfer
        
    Returns:
        Optimized DataLoader
    """
    return DataLoader(
        dataset,
        batch_size=micro_batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory and torch.cuda.is_available(),
        drop_last=True,  # Ensure consistent batch sizes
        persistent_workers=True if num_workers > 0 else False
    )


# Test function
def test_memory_efficient_training():
    """Test memory-efficient training setup"""
    print("Testing Memory-Efficient Training...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create dummy model
    model = nn.Conv2d(8, 4, 3, padding=1).to(device)
    
    # Create memory-efficient trainer
    trainer = MemoryEfficientTrainer(
        model=model,
        micro_batch_size=2,
        effective_batch_size=8,
        max_steps=1000,
        stage_name="test_rtx3050"
    )
    
    print(f"✅ Memory-efficient trainer created:")
    print(f"   - Micro batch size: {trainer.micro_batch_size}")
    print(f"   - Effective batch size: {trainer.effective_batch_size}")
    print(f"   - Gradient accumulation: {trainer.gradient_accumulation_steps}")
    print(f"   - Mixed precision: {trainer.use_fp16}")
    
    # Test RTX 3050 configurations
    configs = create_rtx3050_training_config()
    print(f"✅ RTX 3050 configurations created:")
    for name, config in configs.items():
        print(f"   - {name}: {config.max_steps} steps, batch {config.effective_batch_size}")
    
    return trainer, configs


if __name__ == "__main__":
    test_memory_efficient_training()
