"""
Performance Monitoring Dashboard for RTX 3050 CtrlColor Setup

Real-time monitoring of:
- GPU memory usage and optimization
- Inference performance metrics
- Training progress and efficiency
- Device utilization statistics
- Automatic performance recommendations

Reference: Your RTX 3050 Laptop GPU diagnostic results
"""

import torch
import psutil
import time
import json
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import os
import sys

# Add parent directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    import matplotlib.pyplot as plt
    import matplotlib.animation as animation
    from matplotlib.dates import DateFormatter
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("Warning: matplotlib not available for plotting")


@dataclass
class PerformanceSnapshot:
    """Single performance measurement snapshot"""
    timestamp: datetime
    gpu_memory_allocated_mb: float
    gpu_memory_cached_mb: float
    gpu_memory_total_mb: float
    gpu_utilization_percent: float
    cpu_percent: float
    ram_percent: float
    inference_time_ms: Optional[float] = None
    batch_size: Optional[int] = None
    image_size: Optional[tuple] = None
    model_precision: Optional[str] = None


class RTX3050PerformanceMonitor:
    """
    Performance monitoring system optimized for RTX 3050 Laptop GPU
    
    Features:
    - Real-time GPU memory tracking
    - Performance bottleneck detection
    - Automatic optimization recommendations
    - Historical performance analysis
    - Memory leak detection
    """
    
    def __init__(self,
                 device: str = "cuda:0",
                 monitoring_interval: float = 1.0,
                 history_length: int = 1000,
                 enable_auto_recommendations: bool = True):
        """
        Initialize performance monitor
        
        Args:
            device: CUDA device to monitor
            monitoring_interval: Monitoring interval in seconds
            history_length: Number of snapshots to keep in history
            enable_auto_recommendations: Enable automatic recommendations
        """
        self.device = torch.device(device) if torch.cuda.is_available() else torch.device("cpu")
        self.monitoring_interval = monitoring_interval
        self.history_length = history_length
        self.enable_auto_recommendations = enable_auto_recommendations
        
        # Performance history
        self.performance_history: List[PerformanceSnapshot] = []
        self.recommendations: List[str] = []
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        
        # GPU specifications (RTX 3050 Laptop)
        if torch.cuda.is_available():
            props = torch.cuda.get_device_properties(self.device)
            self.gpu_total_memory = props.total_memory
            self.gpu_name = props.name
            self.gpu_compute_capability = f"{props.major}.{props.minor}"
        else:
            self.gpu_total_memory = 0
            self.gpu_name = "No GPU"
            self.gpu_compute_capability = "N/A"
        
        # Performance thresholds for RTX 3050
        self.thresholds = {
            'memory_warning': 0.80,  # 80% memory usage warning
            'memory_critical': 0.95,  # 95% memory usage critical
            'inference_time_warning': 5000,  # 5 seconds warning
            'cpu_warning': 80,  # 80% CPU usage warning
            'ram_warning': 85   # 85% RAM usage warning
        }
        
        print(f"🔍 Performance monitor initialized:")
        print(f"   - Device: {self.device}")
        print(f"   - GPU: {self.gpu_name}")
        print(f"   - Total VRAM: {self.gpu_total_memory / 1e9:.1f}GB")
        print(f"   - Monitoring interval: {monitoring_interval}s")
    
    def start_monitoring(self):
        """Start background performance monitoring"""
        if self.is_monitoring:
            print("⚠️ Monitoring already active")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        print("✅ Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop background performance monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        print("🛑 Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.is_monitoring:
            try:
                snapshot = self._capture_snapshot()
                self._add_snapshot(snapshot)
                
                # Generate recommendations if enabled
                if self.enable_auto_recommendations:
                    self._analyze_performance(snapshot)
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                print(f"❌ Monitoring error: {e}")
                time.sleep(self.monitoring_interval)
    
    def _capture_snapshot(self) -> PerformanceSnapshot:
        """Capture current performance snapshot"""
        # GPU metrics
        if torch.cuda.is_available():
            gpu_allocated = torch.cuda.memory_allocated(self.device)
            gpu_cached = torch.cuda.memory_reserved(self.device)
            gpu_total = self.gpu_total_memory
            
            # Try to get GPU utilization (may not be available on all systems)
            try:
                import pynvml
                pynvml.nvmlInit()
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                gpu_util = utilization.gpu
            except:
                gpu_util = 0.0  # Fallback if pynvml not available
        else:
            gpu_allocated = gpu_cached = gpu_total = gpu_util = 0.0
        
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=None)
        ram_percent = psutil.virtual_memory().percent
        
        return PerformanceSnapshot(
            timestamp=datetime.now(),
            gpu_memory_allocated_mb=gpu_allocated / 1e6,
            gpu_memory_cached_mb=gpu_cached / 1e6,
            gpu_memory_total_mb=gpu_total / 1e6,
            gpu_utilization_percent=gpu_util,
            cpu_percent=cpu_percent,
            ram_percent=ram_percent
        )
    
    def _add_snapshot(self, snapshot: PerformanceSnapshot):
        """Add snapshot to history"""
        self.performance_history.append(snapshot)
        
        # Maintain history length
        if len(self.performance_history) > self.history_length:
            self.performance_history.pop(0)
    
    def _analyze_performance(self, snapshot: PerformanceSnapshot):
        """Analyze performance and generate recommendations"""
        new_recommendations = []
        
        # Memory usage analysis
        memory_usage = snapshot.gpu_memory_allocated_mb / snapshot.gpu_memory_total_mb
        
        if memory_usage > self.thresholds['memory_critical']:
            new_recommendations.append(
                f"🚨 CRITICAL: GPU memory usage at {memory_usage:.1%}. "
                f"Reduce batch size or image resolution immediately."
            )
        elif memory_usage > self.thresholds['memory_warning']:
            new_recommendations.append(
                f"⚠️ WARNING: GPU memory usage at {memory_usage:.1%}. "
                f"Consider enabling FP16 or reducing batch size."
            )
        
        # CPU usage analysis
        if snapshot.cpu_percent > self.thresholds['cpu_warning']:
            new_recommendations.append(
                f"⚠️ High CPU usage: {snapshot.cpu_percent:.1f}%. "
                f"Consider reducing data loading workers."
            )
        
        # RAM usage analysis
        if snapshot.ram_percent > self.thresholds['ram_warning']:
            new_recommendations.append(
                f"⚠️ High RAM usage: {snapshot.ram_percent:.1f}%. "
                f"Consider reducing dataset cache size."
            )
        
        # Add new recommendations
        for rec in new_recommendations:
            if rec not in self.recommendations:
                self.recommendations.append(rec)
                print(rec)
        
        # Keep only recent recommendations
        if len(self.recommendations) > 10:
            self.recommendations = self.recommendations[-10:]
    
    def log_inference(self,
                     inference_time_ms: float,
                     batch_size: int,
                     image_size: tuple,
                     model_precision: str = "fp32"):
        """
        Log inference performance
        
        Args:
            inference_time_ms: Inference time in milliseconds
            batch_size: Batch size used
            image_size: Image size (height, width)
            model_precision: Model precision (fp16/fp32)
        """
        # Update latest snapshot with inference info
        if self.performance_history:
            latest = self.performance_history[-1]
            latest.inference_time_ms = inference_time_ms
            latest.batch_size = batch_size
            latest.image_size = image_size
            latest.model_precision = model_precision
        
        # Check inference time
        if inference_time_ms > self.thresholds['inference_time_warning']:
            recommendation = (
                f"⚠️ Slow inference: {inference_time_ms:.0f}ms. "
                f"Try reducing image size from {image_size} or enabling FP16."
            )
            if recommendation not in self.recommendations:
                self.recommendations.append(recommendation)
                print(recommendation)
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        if not self.performance_history:
            return {'message': 'No performance data available'}
        
        latest = self.performance_history[-1]
        
        # Calculate averages over last 10 snapshots
        recent_snapshots = self.performance_history[-10:]
        avg_memory = sum(s.gpu_memory_allocated_mb for s in recent_snapshots) / len(recent_snapshots)
        avg_cpu = sum(s.cpu_percent for s in recent_snapshots) / len(recent_snapshots)
        avg_ram = sum(s.ram_percent for s in recent_snapshots) / len(recent_snapshots)
        
        return {
            'current': {
                'gpu_memory_mb': latest.gpu_memory_allocated_mb,
                'gpu_memory_percent': (latest.gpu_memory_allocated_mb / latest.gpu_memory_total_mb) * 100,
                'gpu_utilization': latest.gpu_utilization_percent,
                'cpu_percent': latest.cpu_percent,
                'ram_percent': latest.ram_percent,
                'inference_time_ms': latest.inference_time_ms,
                'batch_size': latest.batch_size,
                'image_size': latest.image_size,
                'model_precision': latest.model_precision
            },
            'averages': {
                'gpu_memory_mb': avg_memory,
                'cpu_percent': avg_cpu,
                'ram_percent': avg_ram
            },
            'gpu_info': {
                'name': self.gpu_name,
                'total_memory_gb': self.gpu_total_memory / 1e9,
                'compute_capability': self.gpu_compute_capability
            },
            'recommendations': self.recommendations[-5:],  # Last 5 recommendations
            'monitoring_active': self.is_monitoring
        }
    
    def get_optimization_recommendations(self) -> List[str]:
        """Get specific optimization recommendations for RTX 3050"""
        recommendations = []
        
        if not self.performance_history:
            return ["Start monitoring to get recommendations"]
        
        latest = self.performance_history[-1]
        memory_usage = latest.gpu_memory_allocated_mb / latest.gpu_memory_total_mb
        
        # Memory-based recommendations
        if memory_usage > 0.8:
            recommendations.extend([
                "🔧 Enable mixed precision (FP16) training/inference",
                "🔧 Reduce batch size to 1-2 for training, 2-4 for inference",
                "🔧 Use gradient checkpointing for training",
                "🔧 Limit image resolution to 512x512 or lower",
                "🔧 Enable gradient accumulation instead of large batches"
            ])
        elif memory_usage > 0.6:
            recommendations.extend([
                "🔧 Consider enabling FP16 for better performance",
                "🔧 Optimal batch size: 2-4 for your GPU",
                "🔧 Image resolution up to 640x640 should work well"
            ])
        else:
            recommendations.extend([
                "✅ Memory usage is optimal",
                "🔧 You can try larger batch sizes (4-8)",
                "🔧 Image resolution up to 768x768 may be possible"
            ])
        
        # Performance-based recommendations
        if latest.inference_time_ms and latest.inference_time_ms > 3000:
            recommendations.append("🔧 Inference is slow - enable FP16 and reduce image size")
        
        return recommendations
    
    def save_performance_report(self, filepath: str):
        """Save performance report to file"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'gpu_info': {
                'name': self.gpu_name,
                'total_memory_gb': self.gpu_total_memory / 1e9,
                'compute_capability': self.gpu_compute_capability
            },
            'current_stats': self.get_current_stats(),
            'recommendations': self.get_optimization_recommendations(),
            'performance_history': [asdict(snapshot) for snapshot in self.performance_history[-100:]]  # Last 100 snapshots
        }
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📊 Performance report saved to {filepath}")
    
    def plot_performance_history(self, save_path: Optional[str] = None):
        """Plot performance history"""
        if not MATPLOTLIB_AVAILABLE:
            print("❌ matplotlib not available for plotting")
            return
        
        if len(self.performance_history) < 2:
            print("❌ Not enough data for plotting")
            return
        
        # Extract data
        timestamps = [s.timestamp for s in self.performance_history]
        memory_usage = [s.gpu_memory_allocated_mb for s in self.performance_history]
        cpu_usage = [s.cpu_percent for s in self.performance_history]
        
        # Create plot
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # GPU memory plot
        ax1.plot(timestamps, memory_usage, 'b-', label='GPU Memory (MB)')
        ax1.axhline(y=self.gpu_total_memory/1e6 * 0.8, color='orange', linestyle='--', label='80% Warning')
        ax1.axhline(y=self.gpu_total_memory/1e6 * 0.95, color='red', linestyle='--', label='95% Critical')
        ax1.set_ylabel('GPU Memory (MB)')
        ax1.set_title('RTX 3050 Performance Monitor')
        ax1.legend()
        ax1.grid(True)
        
        # CPU usage plot
        ax2.plot(timestamps, cpu_usage, 'g-', label='CPU Usage (%)')
        ax2.axhline(y=80, color='orange', linestyle='--', label='80% Warning')
        ax2.set_ylabel('CPU Usage (%)')
        ax2.set_xlabel('Time')
        ax2.legend()
        ax2.grid(True)
        
        # Format x-axis
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(DateFormatter('%H:%M:%S'))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"📈 Performance plot saved to {save_path}")
        else:
            plt.show()


# Test function
def test_performance_monitor():
    """Test performance monitoring"""
    print("Testing RTX 3050 Performance Monitor...")
    
    # Initialize monitor
    monitor = RTX3050PerformanceMonitor(
        monitoring_interval=0.5,
        enable_auto_recommendations=True
    )
    
    # Start monitoring
    monitor.start_monitoring()
    
    # Simulate some activity
    print("🔄 Simulating GPU activity...")
    time.sleep(2)
    
    # Log some inference
    monitor.log_inference(
        inference_time_ms=1500,
        batch_size=2,
        image_size=(512, 512),
        model_precision="fp16"
    )
    
    time.sleep(2)
    
    # Get current stats
    stats = monitor.get_current_stats()
    print(f"✅ Current stats: {list(stats.keys())}")
    
    # Get recommendations
    recommendations = monitor.get_optimization_recommendations()
    print(f"✅ Recommendations: {len(recommendations)} items")
    for rec in recommendations[:3]:
        print(f"   {rec}")
    
    # Stop monitoring
    monitor.stop_monitoring()
    
    return monitor


if __name__ == "__main__":
    test_performance_monitor()
