"""
Simple RTX 3050 Optimization Test

A standalone test script that validates your RTX 3050 setup without complex dependencies.
Tests core optimizations like FP16, memory management, and optimal batch sizing.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import time
import gc
import sys
import os
from typing import Dict, Any, List


class SimpleRTX3050Tester:
    """Simple optimization tester for RTX 3050"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.results = {}
        
        print("🚀 SIMPLE RTX 3050 OPTIMIZATION TEST")
        print("="*50)
        
        if torch.cuda.is_available():
            props = torch.cuda.get_device_properties(self.device)
            print(f"GPU: {props.name}")
            print(f"VRAM: {props.total_memory / 1e9:.1f}GB")
            print(f"Compute Capability: {props.major}.{props.minor}")
            self.total_memory = props.total_memory
        else:
            print("❌ CUDA not available")
            self.total_memory = 0
        
        print("="*50)
    
    def test_cuda_availability(self) -> Dict[str, str]:
        """Test basic CUDA functionality"""
        print("\n🔍 TESTING CUDA AVAILABILITY")
        print("-" * 30)
        
        results = {}
        
        # Test CUDA availability
        if torch.cuda.is_available():
            results['cuda_available'] = "✅ CUDA available"
            results['device_count'] = f"✅ {torch.cuda.device_count()} GPU(s)"
            results['current_device'] = f"✅ Using {self.device}"
        else:
            results['cuda_available'] = "❌ CUDA not available"
            return results
        
        # Test basic tensor operations
        try:
            test_tensor = torch.randn(100, 100, device=self.device)
            result = torch.matmul(test_tensor, test_tensor)
            results['tensor_ops'] = "✅ Tensor operations work"
        except Exception as e:
            results['tensor_ops'] = f"❌ Tensor operations failed: {e}"
        
        # Test memory allocation
        try:
            memory_before = torch.cuda.memory_allocated(self.device)
            large_tensor = torch.randn(1000, 1000, device=self.device)
            memory_after = torch.cuda.memory_allocated(self.device)
            memory_used = (memory_after - memory_before) / 1e6
            results['memory_allocation'] = f"✅ Memory allocation works ({memory_used:.1f}MB)"
            del large_tensor
        except Exception as e:
            results['memory_allocation'] = f"❌ Memory allocation failed: {e}"
        
        for key, value in results.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
        
        return results
    
    def test_fp16_support(self) -> Dict[str, str]:
        """Test FP16 mixed precision support"""
        print("\n🎯 TESTING FP16 SUPPORT")
        print("-" * 30)
        
        results = {}
        
        if not torch.cuda.is_available():
            results['fp16_support'] = "❌ CUDA not available"
            return results
        
        try:
            # Test FP16 tensor creation
            fp16_tensor = torch.randn(512, 512, dtype=torch.float16, device=self.device)
            results['fp16_creation'] = "✅ FP16 tensor creation works"
            
            # Test FP16 operations
            fp16_result = torch.matmul(fp16_tensor, fp16_tensor)
            results['fp16_operations'] = "✅ FP16 operations work"
            
            # Test autocast
            from torch.cuda.amp import autocast
            with autocast():
                autocast_result = torch.matmul(fp16_tensor, fp16_tensor)
            results['autocast'] = "✅ Autocast works"
            
            # Test memory savings
            fp32_tensor = torch.randn(512, 512, dtype=torch.float32, device=self.device)
            fp16_size = fp16_tensor.element_size() * fp16_tensor.nelement()
            fp32_size = fp32_tensor.element_size() * fp32_tensor.nelement()
            memory_savings = (1 - fp16_size / fp32_size) * 100
            results['memory_savings'] = f"✅ {memory_savings:.1f}% memory saved vs FP32"
            
            # Test performance difference
            start_time = time.time()
            for _ in range(50):
                with autocast():
                    _ = torch.matmul(fp16_tensor[:256, :256], fp16_tensor[:256, :256])
            fp16_time = time.time() - start_time
            
            start_time = time.time()
            for _ in range(50):
                _ = torch.matmul(fp32_tensor[:256, :256], fp32_tensor[:256, :256])
            fp32_time = time.time() - start_time
            
            if fp32_time > 0:
                speedup = fp32_time / fp16_time
                results['performance'] = f"✅ {speedup:.1f}x speedup vs FP32"
            else:
                results['performance'] = "✅ Performance test completed"
            
            # Cleanup
            del fp16_tensor, fp32_tensor, fp16_result, autocast_result
            
        except Exception as e:
            results['fp16_error'] = f"❌ FP16 test failed: {e}"
        
        for key, value in results.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
        
        return results
    
    def test_memory_management(self) -> Dict[str, str]:
        """Test memory management optimizations"""
        print("\n🧠 TESTING MEMORY MANAGEMENT")
        print("-" * 30)
        
        results = {}
        
        if not torch.cuda.is_available():
            results['memory_management'] = "❌ CUDA not available"
            return results
        
        try:
            # Test memory fraction setting
            torch.cuda.set_per_process_memory_fraction(0.85)
            results['memory_fraction'] = "✅ Memory fraction set to 85%"
            
            # Test cuDNN benchmark
            torch.backends.cudnn.benchmark = True
            results['cudnn_benchmark'] = "✅ cuDNN benchmark enabled"
            
            # Test memory cleanup
            initial_memory = torch.cuda.memory_allocated(self.device)
            
            # Allocate some memory
            tensors = []
            for i in range(20):
                tensor = torch.randn(200, 200, device=self.device)
                tensors.append(tensor)
            
            peak_memory = torch.cuda.memory_allocated(self.device)
            
            # Clean up
            del tensors
            torch.cuda.empty_cache()
            gc.collect()
            
            final_memory = torch.cuda.memory_allocated(self.device)
            
            memory_cleaned = (peak_memory - final_memory) / 1e6
            results['memory_cleanup'] = f"✅ Cleaned {memory_cleaned:.1f}MB"
            
            # Test memory monitoring
            total_memory = torch.cuda.get_device_properties(self.device).total_memory
            current_usage = torch.cuda.memory_allocated(self.device)
            usage_percent = (current_usage / total_memory) * 100
            results['memory_monitoring'] = f"✅ Current usage: {usage_percent:.1f}%"
            
        except Exception as e:
            results['memory_error'] = f"❌ Memory test failed: {e}"
        
        for key, value in results.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
        
        return results
    
    def test_optimal_batch_sizes(self) -> Dict[str, str]:
        """Test optimal batch sizes for RTX 3050"""
        print("\n📊 TESTING OPTIMAL BATCH SIZES")
        print("-" * 30)
        
        results = {}
        
        if not torch.cuda.is_available():
            results['batch_sizing'] = "❌ CUDA not available"
            return results
        
        # Test different batch sizes with 512x512 images
        batch_sizes = [1, 2, 4, 8]
        image_size = 512
        successful_batches = []
        
        for batch_size in batch_sizes:
            try:
                # Clear memory before test
                torch.cuda.empty_cache()
                
                # Create test batch with FP16
                test_batch = torch.randn(
                    batch_size, 3, image_size, image_size,
                    dtype=torch.float16,
                    device=self.device
                )
                
                # Simulate some processing
                with torch.no_grad():
                    # Simulate convolution
                    conv = nn.Conv2d(3, 64, 3, padding=1, dtype=torch.float16).to(self.device)
                    processed = conv(test_batch)
                    
                    # Simulate more processing
                    processed = F.relu(processed)
                    processed = F.max_pool2d(processed, 2)
                
                memory_used = torch.cuda.memory_allocated(self.device)
                memory_mb = memory_used / 1e6
                memory_percent = (memory_used / self.total_memory) * 100
                
                results[f'batch_{batch_size}'] = f"✅ {memory_mb:.1f}MB ({memory_percent:.1f}%)"
                successful_batches.append(batch_size)
                
                # Cleanup
                del test_batch, processed, conv
                torch.cuda.empty_cache()
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    results[f'batch_{batch_size}'] = "❌ Out of memory"
                    torch.cuda.empty_cache()
                    break
                else:
                    results[f'batch_{batch_size}'] = f"❌ Error: {e}"
        
        # Determine optimal batch size
        if successful_batches:
            optimal_batch = max(successful_batches)
            results['optimal_batch'] = f"✅ Recommended batch size: {optimal_batch}"
        else:
            results['optimal_batch'] = "❌ No successful batch sizes"
        
        for key, value in results.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
        
        return results
    
    def test_image_size_limits(self) -> Dict[str, str]:
        """Test maximum image sizes for RTX 3050"""
        print("\n🖼️ TESTING IMAGE SIZE LIMITS")
        print("-" * 30)
        
        results = {}
        
        if not torch.cuda.is_available():
            results['image_sizing'] = "❌ CUDA not available"
            return results
        
        # Test different image sizes with batch size 1
        image_sizes = [256, 384, 512, 640, 768]
        successful_sizes = []
        
        for size in image_sizes:
            try:
                torch.cuda.empty_cache()
                
                # Create test image with FP16
                test_image = torch.randn(
                    1, 3, size, size,
                    dtype=torch.float16,
                    device=self.device
                )
                
                # Simulate processing
                with torch.no_grad():
                    # Simulate U-Net like processing
                    conv1 = nn.Conv2d(3, 64, 3, padding=1, dtype=torch.float16).to(self.device)
                    conv2 = nn.Conv2d(64, 128, 3, padding=1, dtype=torch.float16).to(self.device)
                    
                    x = conv1(test_image)
                    x = F.relu(x)
                    x = conv2(x)
                    x = F.relu(x)
                
                memory_used = torch.cuda.memory_allocated(self.device)
                memory_mb = memory_used / 1e6
                memory_percent = (memory_used / self.total_memory) * 100
                
                results[f'size_{size}'] = f"✅ {memory_mb:.1f}MB ({memory_percent:.1f}%)"
                successful_sizes.append(size)
                
                # Cleanup
                del test_image, x, conv1, conv2
                torch.cuda.empty_cache()
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    results[f'size_{size}'] = "❌ Out of memory"
                    torch.cuda.empty_cache()
                    break
                else:
                    results[f'size_{size}'] = f"❌ Error: {e}"
        
        # Determine optimal image size
        if successful_sizes:
            optimal_size = max(successful_sizes)
            results['optimal_size'] = f"✅ Recommended max size: {optimal_size}x{optimal_size}"
        else:
            results['optimal_size'] = "❌ No successful image sizes"
        
        for key, value in results.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
        
        return results
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all optimization tests"""
        print("🧪 RUNNING RTX 3050 OPTIMIZATION TESTS")
        print("="*50)
        
        all_results = {}
        
        # Run tests
        all_results['cuda_availability'] = self.test_cuda_availability()
        all_results['fp16_support'] = self.test_fp16_support()
        all_results['memory_management'] = self.test_memory_management()
        all_results['batch_sizing'] = self.test_optimal_batch_sizes()
        all_results['image_sizing'] = self.test_image_size_limits()
        
        # Generate summary
        print("\n" + "="*50)
        print("🎯 RTX 3050 TEST SUMMARY")
        print("="*50)
        
        total_tests = 0
        passed_tests = 0
        
        for category, results in all_results.items():
            print(f"\n{category.replace('_', ' ').title()}:")
            for test, result in results.items():
                total_tests += 1
                if "✅" in str(result):
                    passed_tests += 1
                    status = "PASS"
                else:
                    status = "FAIL"
                print(f"  {test}: {status}")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n📊 OVERALL RESULTS:")
        print(f"   Tests Passed: {passed_tests}/{total_tests}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("\n🎉 Your RTX 3050 is well optimized for CtrlColor!")
            print("✅ Recommended settings:")
            print("   - Use FP16 precision")
            print("   - Batch size: 1-2 for inference")
            print("   - Image size: up to 512x512")
            print("   - Enable memory monitoring")
        elif success_rate >= 60:
            print("\n⚠️ Some optimizations need attention")
            print("🔧 Check failed tests and apply fixes")
        else:
            print("\n❌ Multiple issues detected")
            print("🛠️ Review CUDA setup and system configuration")
        
        return all_results


def main():
    """Main test function"""
    tester = SimpleRTX3050Tester()
    results = tester.run_all_tests()
    
    print(f"\n📄 Test completed!")
    print("\n🚀 Next steps:")
    print("1. If tests passed, you're ready for optimized CtrlColor")
    print("2. Use FP16 precision for best performance")
    print("3. Monitor memory usage during inference")
    print("4. Start with small batch sizes and increase as needed")


if __name__ == "__main__":
    main()
