"""
RTX 3050 Optimization Test Suite

Validates all optimizations work correctly on your RTX 3050 Laptop GPU:
- Memory-efficient inference
- FP16 mixed precision
- Optimal batch sizing
- Performance monitoring
- Memory management

Run this to ensure everything is working optimally on your hardware.
"""

import torch
import torch.nn as nn
import time
import numpy as np
from typing import Dict, Any
import sys
import os

# Add parent directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from .optimized_inference import OptimizedCtrlColorInference
from .memory_efficient_training import MemoryEfficientTrainer, create_rtx3050_training_config
from .performance_monitor import RTX3050PerformanceMonitor


class RTX3050OptimizationTester:
    """
    Comprehensive test suite for RTX 3050 optimizations
    """
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.results = {}
        
        print("🚀 RTX 3050 OPTIMIZATION TEST SUITE")
        print("="*60)
        
        if torch.cuda.is_available():
            props = torch.cuda.get_device_properties(self.device)
            print(f"GPU: {props.name}")
            print(f"VRAM: {props.total_memory / 1e9:.1f}GB")
            print(f"Compute Capability: {props.major}.{props.minor}")
        else:
            print("❌ CUDA not available - tests will run on CPU")
        
        print("="*60)
    
    def test_memory_management(self) -> Dict[str, Any]:
        """Test memory management optimizations"""
        print("\n🧠 TESTING MEMORY MANAGEMENT")
        print("-" * 40)
        
        results = {}
        
        try:
            # Test memory fraction setting
            if torch.cuda.is_available():
                torch.cuda.set_per_process_memory_fraction(0.85)
                results['memory_fraction'] = "✅ Set to 85%"
            else:
                results['memory_fraction'] = "⚠️ CUDA not available"
            
            # Test cuDNN optimizations
            torch.backends.cudnn.benchmark = True
            results['cudnn_benchmark'] = "✅ Enabled"
            
            # Test memory cleanup
            initial_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
            
            # Create some tensors
            test_tensors = []
            for i in range(10):
                tensor = torch.randn(100, 100, device=self.device)
                test_tensors.append(tensor)
            
            peak_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
            
            # Clean up
            del test_tensors
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
            final_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
            
            results['memory_cleanup'] = f"✅ {initial_memory/1e6:.1f}MB → {peak_memory/1e6:.1f}MB → {final_memory/1e6:.1f}MB"
            
            print(f"Memory fraction: {results['memory_fraction']}")
            print(f"cuDNN benchmark: {results['cudnn_benchmark']}")
            print(f"Memory cleanup: {results['memory_cleanup']}")
            
        except Exception as e:
            results['error'] = f"❌ {str(e)}"
            print(f"Error: {results['error']}")
        
        return results
    
    def test_fp16_precision(self) -> Dict[str, Any]:
        """Test FP16 mixed precision"""
        print("\n🎯 TESTING FP16 MIXED PRECISION")
        print("-" * 40)
        
        results = {}
        
        try:
            # Test FP16 tensor creation
            fp16_tensor = torch.randn(512, 512, dtype=torch.float16, device=self.device)
            results['fp16_creation'] = "✅ FP16 tensors work"
            
            # Test autocast
            from torch.cuda.amp import autocast
            
            with autocast():
                result = torch.matmul(fp16_tensor, fp16_tensor)
            
            results['autocast'] = "✅ Autocast works"
            
            # Test memory savings
            fp32_tensor = torch.randn(512, 512, dtype=torch.float32, device=self.device)
            fp16_size = fp16_tensor.element_size() * fp16_tensor.nelement()
            fp32_size = fp32_tensor.element_size() * fp32_tensor.nelement()
            
            memory_savings = (1 - fp16_size / fp32_size) * 100
            results['memory_savings'] = f"✅ {memory_savings:.1f}% memory saved"
            
            # Test performance
            start_time = time.time()
            for _ in range(100):
                with autocast():
                    _ = torch.matmul(fp16_tensor[:256, :256], fp16_tensor[:256, :256])
            fp16_time = time.time() - start_time
            
            start_time = time.time()
            for _ in range(100):
                _ = torch.matmul(fp32_tensor[:256, :256], fp32_tensor[:256, :256])
            fp32_time = time.time() - start_time
            
            speedup = fp32_time / fp16_time
            results['performance'] = f"✅ {speedup:.1f}x speedup"
            
            print(f"FP16 creation: {results['fp16_creation']}")
            print(f"Autocast: {results['autocast']}")
            print(f"Memory savings: {results['memory_savings']}")
            print(f"Performance: {results['performance']}")
            
            # Cleanup
            del fp16_tensor, fp32_tensor, result
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
        except Exception as e:
            results['error'] = f"❌ {str(e)}"
            print(f"Error: {results['error']}")
        
        return results
    
    def test_optimal_batch_sizing(self) -> Dict[str, Any]:
        """Test optimal batch sizing for RTX 3050"""
        print("\n📊 TESTING OPTIMAL BATCH SIZING")
        print("-" * 40)
        
        results = {}
        
        try:
            # Test different batch sizes
            batch_sizes = [1, 2, 4, 8]
            image_size = 512
            
            for batch_size in batch_sizes:
                try:
                    # Create test batch
                    test_batch = torch.randn(
                        batch_size, 3, image_size, image_size,
                        dtype=torch.float16,
                        device=self.device
                    )
                    
                    # Simulate processing
                    with torch.no_grad():
                        processed = torch.nn.functional.conv2d(
                            test_batch, 
                            torch.randn(64, 3, 3, 3, dtype=torch.float16, device=self.device),
                            padding=1
                        )
                    
                    memory_used = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
                    results[f'batch_{batch_size}'] = f"✅ {memory_used/1e6:.1f}MB"
                    
                    # Cleanup
                    del test_batch, processed
                    torch.cuda.empty_cache() if torch.cuda.is_available() else None
                    
                except RuntimeError as e:
                    if "out of memory" in str(e):
                        results[f'batch_{batch_size}'] = "❌ OOM"
                        torch.cuda.empty_cache() if torch.cuda.is_available() else None
                        break
                    else:
                        raise e
            
            # Determine optimal batch size
            optimal_batch = 1
            for batch_size in batch_sizes:
                if f'batch_{batch_size}' in results and "✅" in results[f'batch_{batch_size}']:
                    optimal_batch = batch_size
            
            results['optimal_batch'] = f"✅ Optimal batch size: {optimal_batch}"
            
            for batch_size in batch_sizes:
                if f'batch_{batch_size}' in results:
                    print(f"Batch {batch_size}: {results[f'batch_{batch_size}']}")
            print(f"Recommendation: {results['optimal_batch']}")
            
        except Exception as e:
            results['error'] = f"❌ {str(e)}"
            print(f"Error: {results['error']}")
        
        return results
    
    def test_optimized_inference(self) -> Dict[str, Any]:
        """Test optimized inference pipeline"""
        print("\n⚡ TESTING OPTIMIZED INFERENCE")
        print("-" * 40)
        
        results = {}
        
        try:
            # Initialize optimized inference
            inference = OptimizedCtrlColorInference(
                model_path="dummy_model.ckpt",
                device=str(self.device),
                use_fp16=True,
                max_memory_usage=0.85
            )
            
            results['initialization'] = "✅ Inference initialized"
            
            # Test with different image sizes
            image_sizes = [256, 512]
            
            for size in image_sizes:
                try:
                    # Create test input
                    grayscale = torch.rand(1, 1, size, size)
                    exemplar = torch.rand(1, 3, size, size)
                    
                    start_time = time.time()
                    
                    # Generate colorization
                    result = inference.generate_colorization(
                        grayscale_image=grayscale,
                        text_prompt="test prompt",
                        exemplar_image=exemplar,
                        num_inference_steps=5  # Reduced for testing
                    )
                    
                    inference_time = time.time() - start_time
                    
                    if 'error' not in result:
                        results[f'inference_{size}'] = f"✅ {inference_time:.2f}s"
                        
                        # Check memory stats
                        if 'memory_stats' in result:
                            peak_memory = result['memory_stats']['peak_memory_mb']
                            results[f'memory_{size}'] = f"✅ {peak_memory:.1f}MB peak"
                    else:
                        results[f'inference_{size}'] = f"❌ {result['error']}"
                    
                except Exception as e:
                    results[f'inference_{size}'] = f"❌ {str(e)}"
            
            # Get performance stats
            stats = inference.get_performance_stats()
            if 'error' not in stats:
                results['performance_stats'] = "✅ Stats available"
            
            for size in image_sizes:
                if f'inference_{size}' in results:
                    print(f"Inference {size}x{size}: {results[f'inference_{size}']}")
                if f'memory_{size}' in results:
                    print(f"Memory {size}x{size}: {results[f'memory_{size}']}")
            
            print(f"Performance stats: {results.get('performance_stats', 'N/A')}")
            
        except Exception as e:
            results['error'] = f"❌ {str(e)}"
            print(f"Error: {results['error']}")
        
        return results
    
    def test_performance_monitoring(self) -> Dict[str, Any]:
        """Test performance monitoring"""
        print("\n📈 TESTING PERFORMANCE MONITORING")
        print("-" * 40)
        
        results = {}
        
        try:
            # Initialize monitor
            monitor = RTX3050PerformanceMonitor(
                device=str(self.device),
                monitoring_interval=0.5,
                enable_auto_recommendations=True
            )
            
            results['monitor_init'] = "✅ Monitor initialized"
            
            # Start monitoring
            monitor.start_monitoring()
            results['monitoring_start'] = "✅ Monitoring started"
            
            # Simulate some activity
            time.sleep(1)
            
            # Log inference
            monitor.log_inference(
                inference_time_ms=1500,
                batch_size=2,
                image_size=(512, 512),
                model_precision="fp16"
            )
            results['inference_logging'] = "✅ Inference logged"
            
            # Get stats
            stats = monitor.get_current_stats()
            if 'current' in stats:
                results['stats_collection'] = "✅ Stats collected"
            
            # Get recommendations
            recommendations = monitor.get_optimization_recommendations()
            results['recommendations'] = f"✅ {len(recommendations)} recommendations"
            
            # Stop monitoring
            monitor.stop_monitoring()
            results['monitoring_stop'] = "✅ Monitoring stopped"
            
            for key, value in results.items():
                print(f"{key.replace('_', ' ').title()}: {value}")
            
        except Exception as e:
            results['error'] = f"❌ {str(e)}"
            print(f"Error: {results['error']}")
        
        return results
    
    def test_memory_efficient_training(self) -> Dict[str, Any]:
        """Test memory-efficient training setup"""
        print("\n🏋️ TESTING MEMORY-EFFICIENT TRAINING")
        print("-" * 40)
        
        results = {}
        
        try:
            # Create dummy model
            model = nn.Conv2d(8, 4, 3, padding=1).to(self.device)
            
            # Create memory-efficient trainer
            trainer = MemoryEfficientTrainer(
                model=model,
                micro_batch_size=2,
                effective_batch_size=8,
                use_fp16=True,
                gradient_checkpointing=True,
                max_steps=100,
                stage_name="test_rtx3050"
            )
            
            results['trainer_init'] = "✅ Trainer initialized"
            results['micro_batch'] = f"✅ Micro batch: {trainer.micro_batch_size}"
            results['effective_batch'] = f"✅ Effective batch: {trainer.effective_batch_size}"
            results['gradient_accum'] = f"✅ Grad accum: {trainer.gradient_accumulation_steps}"
            
            # Test RTX 3050 configurations
            configs = create_rtx3050_training_config()
            results['configs'] = f"✅ {len(configs)} stage configs"
            
            for key, value in results.items():
                print(f"{key.replace('_', ' ').title()}: {value}")
            
            # Print config summary
            print("\nRTX 3050 Training Configurations:")
            for name, config in configs.items():
                print(f"  {name}: {config.max_steps} steps, batch {config.effective_batch_size}")
            
        except Exception as e:
            results['error'] = f"❌ {str(e)}"
            print(f"Error: {results['error']}")
        
        return results
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all optimization tests"""
        print("🧪 RUNNING ALL RTX 3050 OPTIMIZATION TESTS")
        print("="*60)
        
        all_results = {}
        
        # Run individual tests
        all_results['memory_management'] = self.test_memory_management()
        all_results['fp16_precision'] = self.test_fp16_precision()
        all_results['batch_sizing'] = self.test_optimal_batch_sizing()
        all_results['optimized_inference'] = self.test_optimized_inference()
        all_results['performance_monitoring'] = self.test_performance_monitoring()
        all_results['memory_efficient_training'] = self.test_memory_efficient_training()
        
        # Generate summary
        print("\n" + "="*60)
        print("🎯 RTX 3050 OPTIMIZATION TEST SUMMARY")
        print("="*60)
        
        total_tests = 0
        passed_tests = 0
        
        for category, results in all_results.items():
            print(f"\n{category.replace('_', ' ').title()}:")
            for test, result in results.items():
                total_tests += 1
                if "✅" in str(result):
                    passed_tests += 1
                    status = "PASS"
                else:
                    status = "FAIL"
                print(f"  {test}: {status}")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n📊 OVERALL RESULTS:")
        print(f"   Tests Passed: {passed_tests}/{total_tests}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 RTX 3050 optimizations are working well!")
            print("✅ Your system is ready for optimized CtrlColor usage")
        elif success_rate >= 60:
            print("⚠️ Most optimizations working, some issues detected")
            print("🔧 Check failed tests and apply recommended fixes")
        else:
            print("❌ Multiple optimization issues detected")
            print("🛠️ Review your CUDA setup and system configuration")
        
        return all_results


def main():
    """Main test function"""
    tester = RTX3050OptimizationTester()
    results = tester.run_all_tests()
    
    # Save results
    import json
    with open('rtx3050_optimization_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Test results saved to: rtx3050_optimization_test_results.json")
    print("\n🚀 Next steps:")
    print("1. Review any failed tests")
    print("2. Apply RTX 3050 optimization guide recommendations")
    print("3. Start using optimized CtrlColor inference and training")


if __name__ == "__main__":
    main()
