

"""

Evaluation Metrics for CtrlColor



Implements all quantitative metrics mentioned in the paper:

- FID (Fréchet Inception Distance)

- LPIPS (Learned Perceptual Image Patch Similarity)

- PSNR (Peak Signal-to-Noise Ratio)

- SSIM (Structural Similarity Index)

- Colorfulness (<PERSON>ler & Süsstrunk metric)

- CLIP Score (for text-image alignment)



Reference: CtrlColor paper Section 4.2 and supplementary material

"""



from typing import List, Optional



import numpy as np

import torch

import torch.nn.functional as F



try:

    import lpips



    LPIPS_AVAILABLE = True

except ImportError:

    LPIPS_AVAILABLE = False

    print("Warning: LPIPS not available")



try:

    from pytorch_fid import fid_score



    FID_AVAILABLE = True

except ImportError:

    FID_AVAILABLE = False

    print("Warning: pytorch-fid not available")



try:

    from transformers import CLIPModel, CLIPProcessor



    CLIP_AVAILABLE = True

except ImportError:

    CLIP_AVAILABLE = False

    print("Warning: CLIP not available for CLIP score")





class PSNRMetric:

    """Peak Signal-to-Noise Ratio metric"""



    @staticmethod

    def compute_psnr(

        img1: torch.Tensor, img2: torch.Tensor, max_val: float = 1.0

    ) -> torch.Tensor:

        """

        Compute PSNR between two images



        Args:

            img1: First image [B, C, H, W] or [C, H, W]

            img2: Second image [B, C, H, W] or [C, H, W]

            max_val: Maximum possible pixel value



        Returns:

            PSNR value(s)

        """

        mse = torch.mean((img1 - img2) ** 2, dim=(-3, -2, -1))

        psnr = 20 * torch.log10(max_val / torch.sqrt(mse + 1e-8))

        return psnr





class SSIMMetric:

    """Structural Similarity Index Metric"""



    def __init__(self, window_size: int = 11, sigma: float = 1.5):

        self.window_size = window_size

        self.sigma = sigma

        self.window = self._create_window(window_size, sigma)



    def _create_window(self, window_size: int, sigma: float) -> torch.Tensor:

        """Create Gaussian window for SSIM computation"""

        coords = torch.arange(window_size, dtype=torch.float32)

        coords -= window_size // 2



        g = torch.exp(-(coords**2) / (2 * sigma**2))

        g /= g.sum()



        window = g.outer(g)

        return window.unsqueeze(0).unsqueeze(0)



    def compute_ssim(self, img1: torch.Tensor, img2: torch.Tensor) -> torch.Tensor:

        """

        Compute SSIM between two images



        Args:

            img1: First image [B, C, H, W]

            img2: Second image [B, C, H, W]



        Returns:

            SSIM value(s)

        """

        if img1.device != self.window.device:

            self.window = self.window.to(img1.device)



        # Constants for SSIM

        C1 = 0.01**2

        C2 = 0.03**2



        # Ensure window has correct number of channels

        num_channels = img1.shape[1]

        if self.window.shape[0] != num_channels:

            window = self.window.repeat(num_channels, 1, 1, 1)

        else:

            window = self.window



        # Compute means

        mu1 = F.conv2d(img1, window, padding=self.window_size // 2, groups=num_channels)

        mu2 = F.conv2d(img2, window, padding=self.window_size // 2, groups=num_channels)



        mu1_sq = mu1**2

        mu2_sq = mu2**2

        mu1_mu2 = mu1 * mu2



        # Compute variances and covariance

        sigma1_sq = (

            F.conv2d(

                img1**2,

                window,

                padding=self.window_size // 2,

                groups=num_channels,

            )

            - mu1_sq

        )

        sigma2_sq = (

            F.conv2d(

                img2**2,

                window,

                padding=self.window_size // 2,

                groups=num_channels,

            )

            - mu2_sq

        )

        sigma12 = (

            F.conv2d(

                img1 * img2,

                window,

                padding=self.window_size // 2,

                groups=num_channels,

            )

            - mu1_mu2

        )



        # Compute SSIM

        numerator = (2 * mu1_mu2 + C1) * (2 * sigma12 + C2)

        denominator = (mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2)



        ssim_map = numerator / denominator

        return torch.mean(ssim_map, dim=(-3, -2, -1))





class ColorfulnessMetric:

    """

    Colorfulness metric based on Hasler & Süsstrunk



    Reference: "Measuring colourfulness in natural images" (2003)

    """



    @staticmethod

    def compute_colorfulness(image: torch.Tensor) -> torch.Tensor:

        """

        Compute colorfulness metric



        Args:

            image: RGB image [B, 3, H, W] or [3, H, W] in range [0, 1]



        Returns:

            Colorfulness value(s)

        """

        # Handle single image

        if len(image.shape) == 3:

            image = image.unsqueeze(0)



        batch_size = image.shape[0]

        colorfulness_values = []



        for i in range(batch_size):

            img = image[i]  # [3, H, W]



            # Convert to numpy for computation

            img_np = (img.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)



            # Split into R, G, B channels

            R, G, B = img_np[:, :, 0], img_np[:, :, 1], img_np[:, :, 2]



            # Compute rg and yb

            rg = R - G

            yb = 0.5 * (R + G) - B



            # Compute means and standard deviations

            rg_mean, rg_std = np.mean(rg), np.std(rg)

            yb_mean, yb_std = np.mean(yb), np.std(yb)



            # Compute colorfulness

            std_rgyb = np.sqrt(rg_std**2 + yb_std**2)

            mean_rgyb = np.sqrt(rg_mean**2 + yb_mean**2)



            colorfulness = std_rgyb + 0.3 * mean_rgyb

            colorfulness_values.append(colorfulness)



        return torch.tensor(colorfulness_values, device=image.device)





class LPIPSMetric:

    """LPIPS (Learned Perceptual Image Patch Similarity) metric"""



    def __init__(self, net: str = "alex"):

        """

        Initialize LPIPS metric



        Args:

            net: Network to use ('alex', 'vgg', 'squeeze')

        """

        if LPIPS_AVAILABLE:

            self.lpips_model = lpips.LPIPS(net=net)

            # Fix device placement issue

            if self.lpips_model is not None:

                self.lpips_model = (

                    self.lpips_model.cuda()

                    if torch.cuda.is_available()

                    else self.lpips_model

                )

        else:

            self.lpips_model = None

            print("Warning: LPIPS not available, using dummy implementation")



    def compute_lpips(self, img1: torch.Tensor, img2: torch.Tensor) -> torch.Tensor:

        """

        Compute LPIPS between two images



        Args:

            img1: First image [B, 3, H, W] in range [-1, 1] or [0, 1]

            img2: Second image [B, 3, H, W] in range [-1, 1] or [0, 1]



        Returns:

            LPIPS distance(s)

        """

        if self.lpips_model is None:

            # Dummy implementation

            return torch.mean((img1 - img2) ** 2, dim=(1, 2, 3))



        # Ensure images are in [-1, 1] range

        if img1.max() <= 1.0 and img1.min() >= 0.0:

            img1 = img1 * 2.0 - 1.0

        if img2.max() <= 1.0 and img2.min() >= 0.0:

            img2 = img2 * 2.0 - 1.0



        # Ensure tensors are on the same device as the model

        device = next(self.lpips_model.parameters()).device

        img1 = img1.to(device)

        img2 = img2.to(device)



        with torch.no_grad():

            lpips_dist = self.lpips_model(img1, img2)



        return lpips_dist.squeeze()





class FIDMetric:

    """Fréchet Inception Distance metric"""



    @staticmethod

    def compute_fid(

        real_images: torch.Tensor, generated_images: torch.Tensor, batch_size: int = 50

    ) -> float:

        """

        Compute FID between real and generated images



        Args:

            real_images: Real images [N, 3, H, W] in range [0, 1]

            generated_images: Generated images [N, 3, H, W] in range [0, 1]

            batch_size: Batch size for processing



        Returns:

            FID score

        """

        if not FID_AVAILABLE:

            print("Warning: FID computation not available, returning dummy value")

            return 50.0  # Dummy FID value



        # Convert to numpy and scale to [0, 255]

        real_np = (real_images.cpu().numpy() * 255).astype(np.uint8)

        gen_np = (generated_images.cpu().numpy() * 255).astype(np.uint8)



        # Transpose to [N, H, W, 3] format expected by FID

        real_np = real_np.transpose(0, 2, 3, 1)

        gen_np = gen_np.transpose(0, 2, 3, 1)



        try:

            # Compute FID using pytorch-fid

            fid_value = fid_score.calculate_fid_given_paths(

                real_np, gen_np, batch_size=batch_size, device="cuda"

            )

            return fid_value

        except Exception as e:

            print(f"FID computation failed: {e}")

            return 50.0





class CLIPScoreMetric:

    """CLIP Score for text-image alignment"""



    def __init__(self, model_name: str = "openai/clip-vit-base-patch32"):

        """Initialize CLIP score metric"""

        if CLIP_AVAILABLE:

            self.processor = CLIPProcessor.from_pretrained(model_name)

            self.model = CLIPModel.from_pretrained(model_name)

        else:

            self.processor = None

            self.model = None

            print("Warning: CLIP not available for CLIP score")



    def compute_clip_score(

        self, images: torch.Tensor, texts: List[str]

    ) -> torch.Tensor:

        """

        Compute CLIP score between images and texts



        Args:

            images: Images [B, 3, H, W] in range [0, 1]

            texts: List of text descriptions



        Returns:

            CLIP scores [B]

        """

        if self.model is None:

            # Return dummy scores

            return torch.ones(len(texts)) * 0.5



        batch_size = images.shape[0]

        clip_scores = []



        for i in range(batch_size):

            # Convert image to PIL format

            img_tensor = images[i]

            img_np = (img_tensor.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)



            # Process inputs

            inputs = self.processor(

                text=texts[i], images=img_np, return_tensors="pt", padding=True

            )



            # Compute similarity

            with torch.no_grad():

                outputs = self.model(**inputs)

                logits_per_image = outputs.logits_per_image

                clip_score = torch.softmax(logits_per_image, dim=1)[0, 0]



            clip_scores.append(clip_score)



        return torch.stack(clip_scores)





class MetricsCalculator:

    """Combined metrics calculator for comprehensive evaluation"""



    def __init__(self):

        self.psnr = PSNRMetric()

        self.ssim = SSIMMetric()

        self.colorfulness = ColorfulnessMetric()

        self.lpips = LPIPSMetric()

        self.fid = FIDMetric()

        self.clip_score = CLIPScoreMetric()



    def compute_all_metrics(

        self,

        generated_images: torch.Tensor,

        reference_images: Optional[torch.Tensor] = None,

        texts: Optional[List[str]] = None,

    ) -> dict:

        """

        Compute all available metrics



        Args:

            generated_images: Generated images [B, 3, H, W]

            reference_images: Reference images [B, 3, H, W] (for PSNR, SSIM, LPIPS)

            texts: Text descriptions (for CLIP score)



        Returns:

            Dictionary of computed metrics

        """

        metrics = {}



        # Colorfulness (no reference needed)

        metrics["colorfulness"] = self.colorfulness.compute_colorfulness(

            generated_images

        )



        # Metrics requiring reference images

        if reference_images is not None:

            metrics["psnr"] = self.psnr.compute_psnr(generated_images, reference_images)

            metrics["ssim"] = self.ssim.compute_ssim(generated_images, reference_images)

            metrics["lpips"] = self.lpips.compute_lpips(

                generated_images, reference_images

            )



            # FID (computed on batch level)

            metrics["fid"] = self.fid.compute_fid(reference_images, generated_images)



        # CLIP score (requires text descriptions)

        if texts is not None:

            metrics["clip_score"] = self.clip_score.compute_clip_score(

                generated_images, texts

            )



        return metrics



    def print_metrics(self, metrics: dict):

        """Print metrics in a formatted way"""

        print("\n" + "=" * 50)

        print("EVALUATION METRICS")

        print("=" * 50)



        for metric_name, values in metrics.items():

            if isinstance(values, torch.Tensor):

                if values.numel() == 1:

                    print(f"{metric_name.upper():>15}: {values.item():.4f}")

                else:

                    mean_val = values.mean().item()

                    std_val = values.std().item()

                    print(f"{metric_name.upper():>15}: {mean_val:.4f} ± {std_val:.4f}")

            else:

                print(f"{metric_name.upper():>15}: {values:.4f}")



        print("=" * 50)





# Test function

def test_metrics():

    """Test all metrics implementations"""

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")



    print("Testing Evaluation Metrics...")



    # Create test data

    batch_size = 4

    generated = torch.rand(batch_size, 3, 256, 256).to(device)

    reference = torch.rand(batch_size, 3, 256, 256).to(device)

    texts = ["a red car", "a blue house", "a green tree", "a yellow flower"]



    # Initialize calculator

    calculator = MetricsCalculator()



    # Compute metrics

    metrics = calculator.compute_all_metrics(

        generated_images=generated, reference_images=reference, texts=texts

    )



    # Print results

    calculator.print_metrics(metrics)



    return metrics





if __name__ == "__main__":

    test_metrics()

