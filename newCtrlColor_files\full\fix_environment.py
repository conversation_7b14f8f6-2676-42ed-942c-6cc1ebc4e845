"""
Environment Fix Script for CtrlColor

Fixes common environment issues:
- OpenMP duplicate library warnings
- TorchVision image extension warnings
- Missing dependency warnings
"""

import os
import sys
import warnings


def fix_openmp_issue():
    """Fix OpenMP duplicate library issue"""
    os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
    print("✅ Fixed OpenMP duplicate library issue")


def fix_torchvision_warnings():
    """Suppress TorchVision image extension warnings"""
    warnings.filterwarnings("ignore", message="Failed to load image Python extension")
    print("✅ Suppressed TorchVision image extension warnings")


def check_cuda_availability():
    """Check CUDA availability and provide info"""
    try:
        import torch
        if torch.cuda.is_available():
            device_name = torch.cuda.get_device_name(0)
            print(f"✅ CUDA available: {device_name}")
            return True
        else:
            print("⚠️ CUDA not available, using CPU")
            return False
    except ImportError:
        print("❌ PyTorch not installed")
        return False


def check_dependencies():
    """Check and report dependency status"""
    dependencies = {
        'torch': 'PyTorch',
        'torchvision': 'TorchVision', 
        'numpy': 'NumPy',
        'PIL': 'Pillow',
        'cv2': 'OpenCV',
        'transformers': 'Transformers',
        'diffusers': 'Diffusers',
        'pytorch_lightning': 'PyTorch Lightning',
        'wandb': 'Weights & Biases',
        'lpips': 'LPIPS',
        'pytorch_fid': 'FID',
        'gradio': 'Gradio',
        'matplotlib': 'Matplotlib',
        'seaborn': 'Seaborn'
    }
    
    print("\n📦 Dependency Status:")
    print("=" * 40)
    
    available = []
    missing = []
    
    for module, name in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {name}: Available")
            available.append(name)
        except ImportError:
            print(f"❌ {name}: Missing")
            missing.append(name)
    
    print(f"\nSummary: {len(available)}/{len(dependencies)} dependencies available")
    
    if missing:
        print(f"\nMissing dependencies: {', '.join(missing)}")
        print("Run: python install_dependencies.py")
    
    return len(missing) == 0


def apply_all_fixes():
    """Apply all environment fixes"""
    print("🔧 Applying Environment Fixes")
    print("=" * 40)
    
    fix_openmp_issue()
    fix_torchvision_warnings()
    cuda_available = check_cuda_availability()
    all_deps_available = check_dependencies()
    
    print("\n" + "=" * 40)
    if all_deps_available:
        print("🎉 Environment is ready!")
    else:
        print("⚠️ Some dependencies are missing")
        print("Run 'python install_dependencies.py' to install them")
    
    return all_deps_available


if __name__ == "__main__":
    apply_all_fixes()
