"""
Universal Import Configuration - Simple Version

Just import this module at the top of any file in the 'full' folder
to automatically resolve all import issues.

Usage:
    import import_config  # That's it! All imports are now fixed.
"""

import sys
import os
from pathlib import Path

# Get the project root directory (parent of 'full' folder)
_current_file = Path(__file__).absolute()
_full_folder = _current_file.parent
_project_root = _full_folder.parent

# Add project root to Python path if not already there
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

# Verify the setup worked by testing a critical import
try:
    import cldm.cldm
    import ldm.modules.diffusionmodules.util
    _IMPORTS_WORKING = True
except ImportError:
    _IMPORTS_WORKING = False

# Optional: Print status (uncomment for debugging)
# print(f"🔧 Import config loaded. Project root: {_project_root}")
# print(f"✅ External imports working: {_IMPORTS_WORKING}")
