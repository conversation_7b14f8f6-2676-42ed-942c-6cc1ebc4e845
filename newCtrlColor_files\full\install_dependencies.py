"""
Dependency Installation Script for CtrlColor Complete Implementation

Automatically installs all required and optional dependencies for the complete
CtrlColor implementation with proper error handling and fallbacks.
"""

import subprocess
import sys
import os
from typing import List, Dict, Tuple


class DependencyInstaller:
    """Handles installation of CtrlColor dependencies"""

    def __init__(self):
        self.required_packages = [
            "torch>=1.12.0",
            "torchvision>=0.13.0",
            "numpy>=1.21.0",
            "pillow>=8.3.0",
            "opencv-python>=4.6.0",
            "scikit-image>=0.19.0",
            "transformers>=4.20.0"
        ]

        self.optional_packages = [
            "diffusers>=0.15.0",
            "pytorch-lightning>=1.6.0",
            "wandb>=0.13.0",
            "lpips>=0.1.4",
            "pytorch-fid>=0.3.0",
            "gradio>=3.35.0",
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0"
        ]

        self.development_packages = [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "jupyter>=1.0.0"
        ]

        self.installation_results = {
            'required': [],
            'optional': [],
            'development': [],
            'failed': []
        }

    def check_python_version(self) -> bool:
        """Check if Python version is compatible"""
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ is required")
            print(f"Current version: {sys.version}")
            return False

        print(f"✅ Python version: {sys.version}")
        return True

    def install_package(self, package: str) -> bool:
        """Install a single package"""
        try:
            print(f"📦 Installing {package}...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True,
                text=True,
                check=True
            )
            print(f"✅ Successfully installed {package}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e.stderr}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error installing {package}: {e}")
            return False

    def install_packages(self, packages: List[str], category: str) -> Tuple[List[str], List[str]]:
        """Install a list of packages"""
        print(f"\n🔧 Installing {category} packages...")
        print("=" * 50)

        successful = []
        failed = []

        for package in packages:
            if self.install_package(package):
                successful.append(package)
            else:
                failed.append(package)

        return successful, failed

    def install_pytorch_with_cuda(self) -> bool:
        """Install PyTorch with CUDA support if available"""
        print("\n🚀 Installing PyTorch with CUDA support...")

        try:
            # Check if CUDA is available
            import torch
            if torch.cuda.is_available():
                print("✅ CUDA already available")
                return True
        except ImportError:
            pass

        # Try to install PyTorch with CUDA
        cuda_command = [
            sys.executable, "-m", "pip", "install",
            "torch", "torchvision", "torchaudio",
            "--index-url", "https://download.pytorch.org/whl/cu118"
        ]

        try:
            print("📦 Installing PyTorch with CUDA 11.8...")
            subprocess.run(cuda_command, check=True, capture_output=True)
            print("✅ PyTorch with CUDA installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("⚠️ CUDA installation failed, falling back to CPU version")
            return self.install_package("torch torchvision torchaudio")

    def verify_installation(self) -> Dict[str, bool]:
        """Verify that key packages are working"""
        print("\n🔍 Verifying installation...")
        print("=" * 50)

        verification_results = {}

        # Test core packages
        test_imports = {
            'torch': 'import torch; print(f"PyTorch {torch.__version__}")',
            'torchvision': 'import torchvision; print(f"TorchVision {torchvision.__version__}")',
            'numpy': 'import numpy as np; print(f"NumPy {np.__version__}")',
            'PIL': 'from PIL import Image; print("PIL/Pillow working")',
            'cv2': 'import cv2; print(f"OpenCV {cv2.__version__}")',
            'transformers': 'import transformers; print(f"Transformers {transformers.__version__}")'
        }

        for package, test_code in test_imports.items():
            try:
                exec(test_code)
                verification_results[package] = True
                print(f"✅ {package}: Working")
            except Exception as e:
                verification_results[package] = False
                print(f"❌ {package}: Failed - {e}")

        # Test CUDA availability
        try:
            import torch
            if torch.cuda.is_available():
                print(f"✅ CUDA: Available (Device: {torch.cuda.get_device_name(0)})")
                verification_results['cuda'] = True
            else:
                print("⚠️ CUDA: Not available (CPU only)")
                verification_results['cuda'] = False
        except:
            verification_results['cuda'] = False

        return verification_results

    def create_environment_info(self):
        """Create environment information file"""
        print("\n📋 Creating environment information...")

        env_info = [
            "# CtrlColor Environment Information",
            f"Python Version: {sys.version}",
            f"Platform: {sys.platform}",
            "",
            "## Installed Packages",
        ]

        # Add installation results
        for category, packages in self.installation_results.items():
            if packages:
                env_info.append(f"\n### {category.title()} Packages:")
                for package in packages:
                    env_info.append(f"- {package}")

        # Save to file
        env_file = os.path.join(os.path.dirname(__file__), "environment_info.md")
        with open(env_file, 'w') as f:
            f.write('\n'.join(env_info))

        print(f"✅ Environment info saved to: {env_file}")

    def run_installation(self, include_optional: bool = True, include_dev: bool = False):
        """Run complete installation process"""
        print("🚀 CtrlColor Dependency Installation")
        print("=" * 60)

        # Check Python version
        if not self.check_python_version():
            return False

        # Install PyTorch with CUDA
        self.install_pytorch_with_cuda()

        # Install required packages
        successful, failed = self.install_packages(self.required_packages, "required")
        self.installation_results['required'] = successful
        self.installation_results['failed'].extend(failed)

        # Install optional packages
        if include_optional:
            successful, failed = self.install_packages(self.optional_packages, "optional")
            self.installation_results['optional'] = successful
            self.installation_results['failed'].extend(failed)

        # Install development packages
        if include_dev:
            successful, failed = self.install_packages(self.development_packages, "development")
            self.installation_results['development'] = successful
            self.installation_results['failed'].extend(failed)

        # Verify installation
        verification_results = self.verify_installation()

        # Create environment info
        self.create_environment_info()

        # Print summary
        self.print_summary(verification_results)

        return len(self.installation_results['failed']) == 0

    def print_summary(self, verification_results: Dict[str, bool]):
        """Print installation summary"""
        print("\n" + "=" * 60)
        print("INSTALLATION SUMMARY")
        print("=" * 60)

        total_required = len(self.required_packages)
        successful_required = len(self.installation_results['required'])

        print(f"Required packages: {successful_required}/{total_required} installed")
        print(f"Optional packages: {len(self.installation_results['optional'])} installed")
        print(f"Development packages: {len(self.installation_results['development'])} installed")
        print(f"Failed installations: {len(self.installation_results['failed'])}")

        if self.installation_results['failed']:
            print("\n❌ Failed packages:")
            for package in self.installation_results['failed']:
                print(f"   - {package}")

        # Verification summary
        working_packages = sum(1 for v in verification_results.values() if v)
        total_packages = len(verification_results)

        print(f"\nVerification: {working_packages}/{total_packages} packages working")

        if working_packages == total_packages:
            print("\n🎉 Installation completed successfully!")
            print("You can now run: python test_implementation.py")
        else:
            print("\n⚠️ Some packages may not be working correctly")
            print("Check the verification results above")


def main():
    """Main installation function"""
    import argparse

    parser = argparse.ArgumentParser(description="Install CtrlColor dependencies")
    parser.add_argument('--no-optional', action='store_true',
                       help='Skip optional packages')
    parser.add_argument('--include-dev', action='store_true',
                       help='Include development packages')

    args = parser.parse_args()

    installer = DependencyInstaller()
    success = installer.run_installation(
        include_optional=not args.no_optional,
        include_dev=args.include_dev
    )

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
