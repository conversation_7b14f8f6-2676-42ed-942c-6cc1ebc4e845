"""
Simple script to install only missing dependencies
"""

import subprocess
import sys


def install_pytorch_lightning():
    """Install PyTorch Lightning"""
    try:
        print("📦 Installing PyTorch Lightning...")
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "pytorch-lightning>=1.6.0"],
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ PyTorch Lightning installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install PyTorch Lightning: {e.stderr}")
        return False


def check_installation():
    """Check if PyTorch Lightning is now available"""
    try:
        import pytorch_lightning as pl
        print(f"✅ PyTorch Lightning {pl.__version__} is now available")
        return True
    except ImportError:
        print("❌ PyTorch Lightning still not available")
        return False


if __name__ == "__main__":
    print("🔧 Installing Missing Dependencies")
    print("=" * 40)
    
    success = install_pytorch_lightning()
    
    if success:
        check_installation()
        print("\n🎉 Installation complete!")
        print("You can now run: python test_implementation.py")
    else:
        print("\n❌ Installation failed")
        print("Try manually: pip install pytorch-lightning")
