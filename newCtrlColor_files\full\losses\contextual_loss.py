

"""

Contextual Loss Implementation for CtrlColor Exemplar-based Colorization



Based on paper equations 101-106:

- VGG19-based feature extraction

- Cosine similarity computation

- Contextual loss calculation



Reference: "The Contextual Loss for Image Transformation with Non-Aligned Data"

"""



from typing import List



import torch

import torch.nn as nn

import torch.nn.functional as F

import torchvision.models as models





class VGG19FeatureExtractor(nn.Module):

    """VGG19 feature extractor for contextual loss computation"""



    def __init__(

        self,

        layers: List[str] = ["relu1_1", "relu2_1", "relu3_1", "relu4_1", "relu5_1"],

    ):

        super().__init__()

        self.layers = layers



        # Load pre-trained VGG19

        try:

            vgg19 = models.vgg19(weights=models.VGG19_Weights.IMAGENET1K_V1)

        except AttributeError:

            # Fallback for older torchvision versions

            vgg19 = models.vgg19(pretrained=True)

        self.features = vgg19.features



        # Freeze parameters

        for param in self.parameters():

            param.requires_grad = False



        # Layer mapping for VGG19

        self.layer_map = {

            "relu1_1": 1,

            "relu1_2": 3,

            "relu2_1": 6,

            "relu2_2": 8,

            "relu3_1": 11,

            "relu3_2": 13,

            "relu3_3": 15,

            "relu3_4": 17,

            "relu4_1": 20,

            "relu4_2": 22,

            "relu4_3": 24,

            "relu4_4": 26,

            "relu5_1": 29,

            "relu5_2": 31,

            "relu5_3": 33,

            "relu5_4": 35,

        }



    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:

        """Extract features from specified layers"""

        features = []



        for i, layer in enumerate(self.features):

            x = layer(x)

            if i in [self.layer_map[layer_name] for layer_name in self.layers]:

                features.append(x)



        return features





class ContextualLoss(nn.Module):

    """

    Contextual Loss for exemplar-based colorization



    Implements equations 101-106 from the paper:

    - Feature extraction using VGG19

    - Cosine similarity computation

    - Contextual distance calculation

    """



    def __init__(

        self,

        layers: List[str] = ["relu3_1", "relu4_1"],

        band_width: float = 0.1,

        use_vgg: bool = True,

    ):

        super().__init__()



        self.band_width = band_width

        self.use_vgg = use_vgg



        if use_vgg:

            self.feature_extractor = VGG19FeatureExtractor(layers)



        # Normalization for VGG input

        self.register_buffer(

            "mean", torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)

        )

        self.register_buffer(

            "std", torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)

        )



    def normalize_tensor(self, x: torch.Tensor) -> torch.Tensor:

        """Normalize tensor for VGG input"""

        return (x - self.mean) / self.std



    def compute_cosine_distance(self, x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:

        """

        Compute cosine distance between feature maps

        Equation 102: d_ij = 1 - cos(x_i, y_j)

        """

        # Flatten spatial dimensions

        N, C, H, W = x.shape

        x_flat = x.view(N, C, H * W).permute(0, 2, 1)  # [N, HW, C]

        y_flat = y.view(N, C, H * W).permute(0, 2, 1)  # [N, HW, C]



        # Normalize features

        x_norm = F.normalize(x_flat, dim=2)

        y_norm = F.normalize(y_flat, dim=2)



        # Compute cosine similarity

        cosine_sim = torch.bmm(x_norm, y_norm.transpose(1, 2))  # [N, HW, HW]



        # Convert to distance

        cosine_dist = 1 - cosine_sim



        return cosine_dist



    def compute_contextual_distance(self, cosine_dist: torch.Tensor) -> torch.Tensor:

        """

        Compute contextual distance using relative similarities

        Equation 103-104: CX_ij = exp((1-d_ij/min_k(d_ik))/h)

        """

        # Find minimum distance for each point

        min_dist, _ = torch.min(cosine_dist, dim=2, keepdim=True)  # [N, HW, 1]



        # Compute relative distance

        relative_dist = cosine_dist / (min_dist + 1e-8)



        # Apply exponential with bandwidth

        contextual_dist = torch.exp((1 - relative_dist) / self.band_width)



        return contextual_dist



    def compute_contextual_loss_single(

        self, x: torch.Tensor, y: torch.Tensor

    ) -> torch.Tensor:

        """Compute contextual loss for a single feature layer"""

        # Compute cosine distance

        cosine_dist = self.compute_cosine_distance(x, y)



        # Compute contextual distance

        contextual_dist = self.compute_contextual_distance(cosine_dist)



        # Compute contextual loss (Equation 105)

        # CX(X,Y) = -log(1/N * sum_i(max_j(CX_ij)))

        max_sim, _ = torch.max(contextual_dist, dim=2)  # [N, HW]

        cx_loss = -torch.log(torch.mean(max_sim, dim=1))  # [N]



        return torch.mean(cx_loss)



    def forward(self, generated: torch.Tensor, exemplar: torch.Tensor) -> torch.Tensor:

        """

        Compute contextual loss between generated and exemplar images



        Args:

            generated: Generated colorized image [B, 3, H, W]

            exemplar: Exemplar reference image [B, 3, H, W]



        Returns:

            Contextual loss value

        """

        if self.use_vgg:

            # Normalize inputs for VGG

            generated_norm = self.normalize_tensor(generated)

            exemplar_norm = self.normalize_tensor(exemplar)



            # Extract features

            gen_features = self.feature_extractor(generated_norm)

            ex_features = self.feature_extractor(exemplar_norm)



            # Compute loss for each layer

            total_loss = 0.0

            for gen_feat, ex_feat in zip(gen_features, ex_features):

                layer_loss = self.compute_contextual_loss_single(gen_feat, ex_feat)

                total_loss += layer_loss



            return total_loss / len(gen_features)

        else:

            # Direct computation on RGB images

            return self.compute_contextual_loss_single(generated, exemplar)





# Test function

def test_contextual_loss():

    """Test the contextual loss implementation"""

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")



    # Create test tensors

    batch_size, channels, height, width = 2, 3, 256, 256

    generated = torch.randn(batch_size, channels, height, width).to(device)

    exemplar = torch.randn(batch_size, channels, height, width).to(device)



    # Initialize loss

    contextual_loss = ContextualLoss().to(device)



    # Compute loss

    loss = contextual_loss(generated, exemplar)



    print(f"Contextual loss: {loss.item():.4f}")

    print(f"Loss shape: {loss.shape}")



    return loss





if __name__ == "__main__":

    test_contextual_loss()

