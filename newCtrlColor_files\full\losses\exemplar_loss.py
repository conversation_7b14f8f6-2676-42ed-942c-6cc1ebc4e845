

"""

Combined Exemplar Loss Implementation for CtrlColor



Based on paper equations 124-126:

- Combines contextual loss and grayscale loss

- Weighted combination for optimal exemplar-based colorization

- Supports different weighting strategies



Reference: CtrlColor paper Section 3.3 Exemplar Control

"""



import torch

import torch.nn as nn

from typing import Dict, Optional







class ExemplarLoss(nn.Module):

    """

    Combined Exemplar Loss for CtrlColor

    

    Implements equation 124-126 from the paper:

    L_exemplar = λ_cx * L_contextual + λ_gray * L_grayscale

    

    Where:

    - L_contextual: Contextual loss using VGG19 features

    - L_grayscale: Grayscale consistency loss

    - λ_cx, λ_gray: Weighting coefficients

    """

    

    def __init__(self,

                 contextual_weight: float = 1.0,

                 grayscale_weight: float = 0.5,

                 contextual_layers: list = ['relu3_1', 'relu4_1'],

                 grayscale_type: str = 'l1',

                 adaptive_grayscale: bool = False,

                 band_width: float = 0.1):

        """

        Initialize combined exemplar loss

        

        Args:

            contextual_weight: Weight for contextual loss (λ_cx)

            grayscale_weight: Weight for grayscale loss (λ_gray)

            contextual_layers: VGG19 layers for contextual loss

            grayscale_type: Type of grayscale loss ('l1', 'l2', 'smooth_l1')

            adaptive_grayscale: Whether to use adaptive grayscale loss

            band_width: Band width for contextual loss

        """

        super().__init__()

        

        self.contextual_weight = contextual_weight

        self.grayscale_weight = grayscale_weight

        

        # Initialize contextual loss

        self.contextual_loss = ContextualLoss(

            layers=contextual_layers,

            band_width=band_width,

            use_vgg=True

        )

        

        # Initialize grayscale loss

        if adaptive_grayscale:

            self.grayscale_loss = AdaptiveGrayscaleLoss(loss_type=grayscale_type)

        else:

            self.grayscale_loss = GrayscaleLoss(loss_type=grayscale_type)

    

    def forward(self, 

                generated: torch.Tensor, 

                exemplar: torch.Tensor,

                return_components: bool = False) -> torch.Tensor:

        """

        Compute combined exemplar loss

        

        Args:

            generated: Generated colorized image [B, 3, H, W]

            exemplar: Exemplar reference image [B, 3, H, W]

            return_components: Whether to return individual loss components

            

        Returns:

            Combined loss value or dict of components

        """

        # Compute individual losses

        contextual_loss_val = self.contextual_loss(generated, exemplar)

        grayscale_loss_val = self.grayscale_loss(generated, exemplar)

        

        # Combine losses

        total_loss = (self.contextual_weight * contextual_loss_val + 

                     self.grayscale_weight * grayscale_loss_val)

        

        if return_components:

            return {

                'total_loss': total_loss,

                'contextual_loss': contextual_loss_val,

                'grayscale_loss': grayscale_loss_val,

                'contextual_weight': self.contextual_weight,

                'grayscale_weight': self.grayscale_weight

            }

        

        return total_loss





class AdaptiveExemplarLoss(nn.Module):

    """

    Adaptive Exemplar Loss with dynamic weighting

    

    Adjusts the balance between contextual and grayscale losses

    based on training progress or image characteristics

    """

    

    def __init__(self,

                 initial_contextual_weight: float = 1.0,

                 initial_grayscale_weight: float = 0.5,

                 contextual_layers: list = ['relu3_1', 'relu4_1'],

                 adaptation_strategy: str = 'fixed'):

        """

        Initialize adaptive exemplar loss

        

        Args:

            initial_contextual_weight: Initial weight for contextual loss

            initial_grayscale_weight: Initial weight for grayscale loss

            contextual_layers: VGG19 layers for contextual loss

            adaptation_strategy: Strategy for weight adaptation

                - 'fixed': Fixed weights

                - 'curriculum': Curriculum learning (start with grayscale)

                - 'dynamic': Dynamic based on loss magnitudes

        """

        super().__init__()

        

        self.adaptation_strategy = adaptation_strategy

        self.step_count = 0

        

        # Initialize base loss

        self.base_loss = ExemplarLoss(

            contextual_weight=initial_contextual_weight,

            grayscale_weight=initial_grayscale_weight,

            contextual_layers=contextual_layers

        )

        

        # Store initial weights

        self.initial_contextual_weight = initial_contextual_weight

        self.initial_grayscale_weight = initial_grayscale_weight

    

    def update_weights(self, contextual_loss: float, grayscale_loss: float):

        """Update loss weights based on adaptation strategy"""

        if self.adaptation_strategy == 'curriculum':

            # Curriculum learning: start with grayscale, gradually add contextual

            progress = min(self.step_count / 10000, 1.0)  # 10k steps curriculum

            self.base_loss.contextual_weight = self.initial_contextual_weight * progress

            self.base_loss.grayscale_weight = self.initial_grayscale_weight

            

        elif self.adaptation_strategy == 'dynamic':

            # Dynamic weighting based on loss magnitudes

            total_loss = contextual_loss + grayscale_loss

            if total_loss > 0:

                # Normalize weights based on relative magnitudes

                cx_ratio = contextual_loss / total_loss

                gray_ratio = grayscale_loss / total_loss

                

                # Adjust weights to balance losses

                self.base_loss.contextual_weight = self.initial_contextual_weight / (cx_ratio + 1e-8)

                self.base_loss.grayscale_weight = self.initial_grayscale_weight / (gray_ratio + 1e-8)

        

        self.step_count += 1

    

    def forward(self, generated: torch.Tensor, exemplar: torch.Tensor) -> torch.Tensor:

        """Compute adaptive exemplar loss"""

        # Get individual loss components

        loss_dict = self.base_loss(generated, exemplar, return_components=True)

        

        # Update weights based on current losses

        self.update_weights(

            loss_dict['contextual_loss'].item(),

            loss_dict['grayscale_loss'].item()

        )

        

        return loss_dict['total_loss']





class MultiScaleExemplarLoss(nn.Module):

    """

    Multi-scale Exemplar Loss

    

    Computes exemplar loss at multiple scales for better

    global and local feature matching

    """

    

    def __init__(self,

                 scales: list = [1.0, 0.5, 0.25],

                 scale_weights: Optional[list] = None,

                 **loss_kwargs):

        """

        Initialize multi-scale exemplar loss

        

        Args:

            scales: List of scale factors

            scale_weights: Weights for each scale (default: equal)

            **loss_kwargs: Arguments for base ExemplarLoss

        """

        super().__init__()

        

        self.scales = scales

        

        if scale_weights is None:

            scale_weights = [1.0] * len(scales)

        self.scale_weights = scale_weights

        

        # Initialize base loss

        self.base_loss = ExemplarLoss(**loss_kwargs)

    

    def forward(self, generated: torch.Tensor, exemplar: torch.Tensor) -> torch.Tensor:

        """Compute multi-scale exemplar loss"""

        total_loss = 0.0

        

        for scale, weight in zip(self.scales, self.scale_weights):

            if scale != 1.0:

                # Resize images

                size = (int(generated.shape[2] * scale), int(generated.shape[3] * scale))

                gen_scaled = torch.nn.functional.interpolate(

                    generated, size=size, mode='bilinear', align_corners=False

                )

                ex_scaled = torch.nn.functional.interpolate(

                    exemplar, size=size, mode='bilinear', align_corners=False

                )

            else:

                gen_scaled = generated

                ex_scaled = exemplar

            

            # Compute loss at this scale

            scale_loss = self.base_loss(gen_scaled, ex_scaled)

            total_loss += weight * scale_loss

        

        return total_loss / sum(self.scale_weights)





# Test function

def test_exemplar_losses():

    """Test all exemplar loss implementations"""

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    

    # Create test tensors

    batch_size, channels, height, width = 2, 3, 256, 256

    generated = torch.randn(batch_size, channels, height, width).to(device)

    exemplar = torch.randn(batch_size, channels, height, width).to(device)

    

    print("Testing Exemplar Loss implementations...")

    

    # Test basic exemplar loss

    exemplar_loss = ExemplarLoss().to(device)

    loss1 = exemplar_loss(generated, exemplar)

    print(f"Basic exemplar loss: {loss1.item():.4f}")

    

    # Test with components

    loss_dict = exemplar_loss(generated, exemplar, return_components=True)

    print(f"  - Contextual: {loss_dict['contextual_loss'].item():.4f}")

    print(f"  - Grayscale: {loss_dict['grayscale_loss'].item():.4f}")

    

    # Test adaptive exemplar loss

    adaptive_loss = AdaptiveExemplarLoss().to(device)

    loss2 = adaptive_loss(generated, exemplar)

    print(f"Adaptive exemplar loss: {loss2.item():.4f}")

    

    # Test multi-scale exemplar loss

    multiscale_loss = MultiScaleExemplarLoss().to(device)

    loss3 = multiscale_loss(generated, exemplar)

    print(f"Multi-scale exemplar loss: {loss3.item():.4f}")

    

    return loss1, loss2, loss3





if __name__ == "__main__":

    test_exemplar_losses()

