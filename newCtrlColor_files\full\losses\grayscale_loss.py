

"""

Grayscale Loss Implementation for CtrlColor Exemplar-based Colorization



Based on paper equations 111-113:

- Ensures grayscale consistency between generated and exemplar images

- Converts images to grayscale and computes L1/L2 loss

- Helps maintain luminance structure from exemplar



Reference: CtrlColor paper Section 3.3 Exemplar Control

"""



from typing import Optional



import torch

import torch.nn as nn

import torch.nn.functional as F





class GrayscaleLoss(nn.Module):

    """

    Grayscale Loss for exemplar-based colorization



    Implements equations 111-113 from the paper:

    L_gray = ||rgb2gray(I_gen) - rgb2gray(I_ex)||_p



    Where:

    - I_gen: Generated colorized image

    - I_ex: Exemplar reference image

    - rgb2gray: RGB to grayscale conversion

    - p: Loss norm (1 or 2)

    """



    def __init__(

        self, loss_type: str = "l1", rgb_weights: Optional[torch.Tensor] = None

    ):

        """

        Initialize grayscale loss



        Args:

            loss_type: Type of loss ('l1', 'l2', 'smooth_l1')

            rgb_weights: Custom RGB to grayscale conversion weights

                        Default: [0.299, 0.587, 0.114] (ITU-R BT.601)

        """

        super().__init__()



        self.loss_type = loss_type



        # RGB to grayscale conversion weights (ITU-R BT.601 standard)

        if rgb_weights is None:

            rgb_weights = torch.tensor([0.299, 0.587, 0.114])



        self.register_buffer("rgb_weights", rgb_weights.view(1, 3, 1, 1))



        # Loss function selection

        if loss_type == "l1":

            self.loss_fn = nn.L1Loss()

        elif loss_type == "l2":

            self.loss_fn = nn.MSELoss()

        elif loss_type == "smooth_l1":

            self.loss_fn = nn.SmoothL1Loss()

        else:

            raise ValueError(f"Unsupported loss type: {loss_type}")



    def rgb_to_grayscale(self, rgb_image: torch.Tensor) -> torch.Tensor:

        """

        Convert RGB image to grayscale using weighted sum



        Args:

            rgb_image: RGB image tensor [B, 3, H, W]



        Returns:

            Grayscale image tensor [B, 1, H, W]

        """

        # Ensure input is in correct range [0, 1] or [-1, 1]

        # Apply weighted sum: 0.299*R + 0.587*G + 0.114*B

        grayscale = torch.sum(rgb_image * self.rgb_weights, dim=1, keepdim=True)



        return grayscale



    def forward(self, generated: torch.Tensor, exemplar: torch.Tensor) -> torch.Tensor:

        """

        Compute grayscale loss between generated and exemplar images



        Args:

            generated: Generated colorized image [B, 3, H, W]

            exemplar: Exemplar reference image [B, 3, H, W]



        Returns:

            Grayscale loss value

        """

        # Convert both images to grayscale

        generated_gray = self.rgb_to_grayscale(generated)

        exemplar_gray = self.rgb_to_grayscale(exemplar)



        # Compute loss

        loss = self.loss_fn(generated_gray, exemplar_gray)



        return loss





class AdaptiveGrayscaleLoss(nn.Module):

    """

    Adaptive Grayscale Loss with spatial weighting



    Applies different weights to different regions based on:

    - Edge information (higher weight on edges)

    - Gradient magnitude (higher weight on high-gradient regions)

    """



    def __init__(

        self,

        loss_type: str = "l1",

        edge_weight: float = 2.0,

        gradient_weight: float = 1.5,

    ):

        super().__init__()



        self.base_loss = GrayscaleLoss(loss_type)

        self.edge_weight = edge_weight

        self.gradient_weight = gradient_weight



        # Sobel kernels for edge detection

        sobel_x = torch.tensor(

            [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32

        )

        sobel_y = torch.tensor(

            [[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32

        )



        self.register_buffer("sobel_x", sobel_x.view(1, 1, 3, 3))

        self.register_buffer("sobel_y", sobel_y.view(1, 1, 3, 3))



    def compute_edge_weight(self, image: torch.Tensor) -> torch.Tensor:

        """Compute edge-based weighting"""

        # Convert to grayscale for edge detection

        gray = self.base_loss.rgb_to_grayscale(image)



        # Apply Sobel filters

        grad_x = F.conv2d(gray, self.sobel_x, padding=1)

        grad_y = F.conv2d(gray, self.sobel_y, padding=1)



        # Compute gradient magnitude

        gradient_mag = torch.sqrt(grad_x**2 + grad_y**2 + 1e-8)



        # Normalize and apply weighting

        gradient_mag = gradient_mag / (gradient_mag.max() + 1e-8)

        weight = 1.0 + self.gradient_weight * gradient_mag



        return weight



    def forward(self, generated: torch.Tensor, exemplar: torch.Tensor) -> torch.Tensor:

        """Compute adaptive grayscale loss"""

        # Convert to grayscale

        generated_gray = self.base_loss.rgb_to_grayscale(generated)

        exemplar_gray = self.base_loss.rgb_to_grayscale(exemplar)



        # Compute spatial weights based on exemplar edges

        spatial_weight = self.compute_edge_weight(exemplar)



        # Compute weighted loss

        diff = torch.abs(generated_gray - exemplar_gray)

        weighted_diff = diff * spatial_weight



        loss = torch.mean(weighted_diff)



        return loss





class PerceptualGrayscaleLoss(nn.Module):

    """

    Perceptual Grayscale Loss using feature-based comparison



    Computes grayscale loss in feature space rather than pixel space

    for better perceptual quality

    """



    def __init__(self, feature_layers: list = ["relu2_2", "relu3_2"]):

        super().__init__()



        try:

            from .contextual_loss import VGG19FeatureExtractor



            self.feature_extractor = VGG19FeatureExtractor(feature_layers)

        except ImportError:

            print("Warning: VGG19FeatureExtractor not available")

            self.feature_extractor = None

        self.base_loss = GrayscaleLoss()



    def forward(self, generated: torch.Tensor, exemplar: torch.Tensor) -> torch.Tensor:

        """Compute perceptual grayscale loss"""

        # Convert to grayscale

        generated_gray = self.base_loss.rgb_to_grayscale(generated)

        exemplar_gray = self.base_loss.rgb_to_grayscale(exemplar)



        # Expand to 3 channels for VGG

        generated_gray_3ch = generated_gray.repeat(1, 3, 1, 1)

        exemplar_gray_3ch = exemplar_gray.repeat(1, 3, 1, 1)



        # Extract features

        gen_features = self.feature_extractor(generated_gray_3ch)

        ex_features = self.feature_extractor(exemplar_gray_3ch)



        # Compute feature-based loss

        total_loss = 0.0

        for gen_feat, ex_feat in zip(gen_features, ex_features):

            layer_loss = F.mse_loss(gen_feat, ex_feat)

            total_loss += layer_loss



        return total_loss / len(gen_features)





# Test function

def test_grayscale_losses():

    """Test all grayscale loss implementations"""

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")



    # Create test tensors

    batch_size, channels, height, width = 2, 3, 256, 256

    generated = torch.randn(batch_size, channels, height, width).to(device)

    exemplar = torch.randn(batch_size, channels, height, width).to(device)



    # Test basic grayscale loss

    gray_loss = GrayscaleLoss().to(device)

    loss1 = gray_loss(generated, exemplar)

    print(f"Basic grayscale loss: {loss1.item():.4f}")



    # Test adaptive grayscale loss

    adaptive_loss = AdaptiveGrayscaleLoss().to(device)

    loss2 = adaptive_loss(generated, exemplar)

    print(f"Adaptive grayscale loss: {loss2.item():.4f}")



    # Test perceptual grayscale loss

    try:

        perceptual_loss = PerceptualGrayscaleLoss().to(device)

        loss3 = perceptual_loss(generated, exemplar)

        print(f"Perceptual grayscale loss: {loss3.item():.4f}")

    except ImportError:

        print("Perceptual grayscale loss requires contextual_loss module")



    return loss1, loss2





if __name__ == "__main__":

    test_grayscale_losses()

