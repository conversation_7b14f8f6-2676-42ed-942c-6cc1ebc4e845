"""
Exemplar Processing Module for CtrlColor

Handles exemplar image processing for exemplar-based colorization:
- CLIP image encoding
- Exemplar feature extraction
- Color palette extraction
- Exemplar-guided conditioning

Reference: CtrlColor paper Section 3.3 Exemplar Control
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, Tuple, List
from PIL import Image
import cv2

try:
    from transformers import CLIPImageProcessor, CLIPVisionModel
    CLIP_AVAILABLE = True
except ImportError:
    CLIP_AVAILABLE = False


class ExemplarProcessor(nn.Module):
    """
    Exemplar Processing for CtrlColor
    
    Processes exemplar images to extract features and color information
    for guiding the colorization process
    """
    
    def __init__(self,
                 clip_model_name: str = "openai/clip-vit-base-patch32",
                 feature_dim: int = 512,
                 max_colors: int = 16):
        """
        Initialize exemplar processor
        
        Args:
            clip_model_name: CLIP model name for image encoding
            feature_dim: Feature dimension for processing
            max_colors: Maximum number of colors to extract from exemplar
        """
        super().__init__()
        
        self.feature_dim = feature_dim
        self.max_colors = max_colors
        
        if CLIP_AVAILABLE:
            # Initialize CLIP components
            self.clip_processor = CLIPImageProcessor.from_pretrained(clip_model_name)
            self.clip_model = CLIPVisionModel.from_pretrained(clip_model_name)
            
            # Freeze CLIP model initially
            for param in self.clip_model.parameters():
                param.requires_grad = False
        else:
            print("Warning: CLIP not available, using dummy encoder")
            self.clip_processor = None
            self.clip_model = None
        
        # Feature projection layers
        clip_dim = 768 if 'base' in clip_model_name else 1024
        self.feature_projection = nn.Sequential(
            nn.Linear(clip_dim, feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.LayerNorm(feature_dim)
        )
        
        # Color palette extraction
        self.color_extractor = ColorPaletteExtractor(max_colors=max_colors)
    
    def encode_exemplar_clip(self, exemplar_images: torch.Tensor) -> torch.Tensor:
        """
        Encode exemplar images using CLIP
        
        Args:
            exemplar_images: Exemplar images [B, 3, H, W] in range [-1, 1] or [0, 1]
            
        Returns:
            CLIP features [B, feature_dim]
        """
        if not CLIP_AVAILABLE or self.clip_model is None:
            # Return dummy features if CLIP not available
            batch_size = exemplar_images.shape[0]
            return torch.randn(batch_size, self.feature_dim, device=exemplar_images.device)
        
        # Convert to PIL images for CLIP processor
        batch_size = exemplar_images.shape[0]
        clip_features = []
        
        for i in range(batch_size):
            # Convert tensor to PIL image
            img_tensor = exemplar_images[i]
            
            # Normalize to [0, 1] if needed
            if img_tensor.min() < 0:
                img_tensor = (img_tensor + 1) / 2
            
            # Convert to numpy and PIL
            img_np = (img_tensor.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
            img_pil = Image.fromarray(img_np)
            
            # Process with CLIP
            inputs = self.clip_processor(images=img_pil, return_tensors="pt")
            inputs = {k: v.to(exemplar_images.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.clip_model(**inputs)
                clip_feat = outputs.pooler_output  # [1, clip_dim]
            
            clip_features.append(clip_feat)
        
        # Stack features
        clip_features = torch.cat(clip_features, dim=0)  # [B, clip_dim]
        
        # Project to desired feature dimension
        projected_features = self.feature_projection(clip_features)
        
        return projected_features
    
    def extract_color_palette(self, exemplar_images: torch.Tensor) -> torch.Tensor:
        """
        Extract dominant color palette from exemplar images
        
        Args:
            exemplar_images: Exemplar images [B, 3, H, W]
            
        Returns:
            Color palette [B, max_colors, 3]
        """
        batch_size = exemplar_images.shape[0]
        color_palettes = []
        
        for i in range(batch_size):
            img_tensor = exemplar_images[i]
            
            # Convert to numpy
            if img_tensor.min() < 0:
                img_tensor = (img_tensor + 1) / 2
            
            img_np = (img_tensor.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
            
            # Extract color palette
            palette = self.color_extractor.extract_palette(img_np)
            color_palettes.append(palette)
        
        # Stack palettes
        color_palettes = torch.stack(color_palettes, dim=0)
        
        return color_palettes
    
    def process_exemplar(self, 
                        exemplar_images: torch.Tensor,
                        extract_colors: bool = True) -> dict:
        """
        Complete exemplar processing pipeline
        
        Args:
            exemplar_images: Exemplar images [B, 3, H, W]
            extract_colors: Whether to extract color palette
            
        Returns:
            Dictionary containing:
            - clip_features: CLIP-encoded features [B, feature_dim]
            - color_palette: Dominant colors [B, max_colors, 3] (if extract_colors=True)
            - exemplar_stats: Image statistics
        """
        # Encode with CLIP
        clip_features = self.encode_exemplar_clip(exemplar_images)
        
        result = {
            'clip_features': clip_features,
            'exemplar_stats': self._compute_image_stats(exemplar_images)
        }
        
        # Extract color palette if requested
        if extract_colors:
            color_palette = self.extract_color_palette(exemplar_images)
            result['color_palette'] = color_palette
        
        return result
    
    def _compute_image_stats(self, images: torch.Tensor) -> dict:
        """Compute basic image statistics"""
        return {
            'mean': torch.mean(images, dim=[2, 3]),  # [B, 3]
            'std': torch.std(images, dim=[2, 3]),    # [B, 3]
            'min': torch.min(images.view(images.shape[0], images.shape[1], -1), dim=2)[0],
            'max': torch.max(images.view(images.shape[0], images.shape[1], -1), dim=2)[0]
        }
    
    def forward(self, exemplar_images: torch.Tensor) -> dict:
        """Forward pass for exemplar processing"""
        return self.process_exemplar(exemplar_images)


class ColorPaletteExtractor:
    """Extract dominant color palette from images using K-means clustering"""
    
    def __init__(self, max_colors: int = 16, min_colors: int = 4):
        self.max_colors = max_colors
        self.min_colors = min_colors
    
    def extract_palette(self, image: np.ndarray) -> torch.Tensor:
        """
        Extract color palette from image using K-means
        
        Args:
            image: RGB image as numpy array [H, W, 3]
            
        Returns:
            Color palette tensor [max_colors, 3]
        """
        # Reshape image to list of pixels
        pixels = image.reshape(-1, 3).astype(np.float32)
        
        # Remove duplicate colors and sample if too many pixels
        unique_pixels = np.unique(pixels, axis=0)
        if len(unique_pixels) > 10000:
            indices = np.random.choice(len(unique_pixels), 10000, replace=False)
            pixels = unique_pixels[indices]
        else:
            pixels = unique_pixels
        
        # Determine optimal number of clusters
        n_colors = min(len(np.unique(pixels, axis=0)), self.max_colors)
        n_colors = max(n_colors, self.min_colors)
        
        try:
            # K-means clustering
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
            _, labels, centers = cv2.kmeans(
                pixels, n_colors, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS
            )
            
            # Sort colors by frequency
            unique_labels, counts = np.unique(labels, return_counts=True)
            sorted_indices = np.argsort(counts)[::-1]
            sorted_centers = centers[sorted_indices]
            
        except:
            # Fallback: use most frequent colors
            unique_colors, counts = np.unique(pixels, axis=0, return_counts=True)
            sorted_indices = np.argsort(counts)[::-1]
            sorted_centers = unique_colors[sorted_indices[:n_colors]]
        
        # Pad with zeros if needed
        if len(sorted_centers) < self.max_colors:
            padding = np.zeros((self.max_colors - len(sorted_centers), 3))
            sorted_centers = np.vstack([sorted_centers, padding])
        
        # Convert to tensor and normalize to [0, 1]
        palette = torch.from_numpy(sorted_centers[:self.max_colors] / 255.0).float()
        
        return palette


class ExemplarConditioner(nn.Module):
    """
    Exemplar Conditioner for integrating exemplar features into diffusion process
    """
    
    def __init__(self,
                 exemplar_dim: int = 512,
                 condition_dim: int = 768,
                 num_layers: int = 2):
        super().__init__()
        
        self.exemplar_dim = exemplar_dim
        self.condition_dim = condition_dim
        
        # Feature fusion network
        self.fusion_network = nn.Sequential()
        
        current_dim = exemplar_dim
        for i in range(num_layers):
            self.fusion_network.add_module(
                f'linear_{i}',
                nn.Linear(current_dim, condition_dim if i == num_layers-1 else current_dim)
            )
            if i < num_layers - 1:
                self.fusion_network.add_module(f'relu_{i}', nn.ReLU())
                self.fusion_network.add_module(f'dropout_{i}', nn.Dropout(0.1))
        
        # Layer normalization
        self.layer_norm = nn.LayerNorm(condition_dim)
    
    def forward(self, exemplar_features: torch.Tensor) -> torch.Tensor:
        """
        Convert exemplar features to conditioning format
        
        Args:
            exemplar_features: Exemplar features [B, exemplar_dim]
            
        Returns:
            Conditioning features [B, 1, condition_dim]
        """
        # Process through fusion network
        conditioned = self.fusion_network(exemplar_features)
        conditioned = self.layer_norm(conditioned)
        
        # Add sequence dimension for cross-attention
        conditioned = conditioned.unsqueeze(1)  # [B, 1, condition_dim]
        
        return conditioned


# Test function
def test_exemplar_processor():
    """Test exemplar processing components"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create test exemplar images
    batch_size, channels, height, width = 2, 3, 256, 256
    exemplar_images = torch.randn(batch_size, channels, height, width).to(device)
    
    print("Testing Exemplar Processor...")
    
    # Test exemplar processor
    processor = ExemplarProcessor().to(device)
    result = processor(exemplar_images)
    
    print(f"CLIP features shape: {result['clip_features'].shape}")
    if 'color_palette' in result:
        print(f"Color palette shape: {result['color_palette'].shape}")
    
    # Test exemplar conditioner
    conditioner = ExemplarConditioner().to(device)
    conditioning = conditioner(result['clip_features'])
    print(f"Conditioning shape: {conditioning.shape}")
    
    return result, conditioning


if __name__ == "__main__":
    test_exemplar_processor()
