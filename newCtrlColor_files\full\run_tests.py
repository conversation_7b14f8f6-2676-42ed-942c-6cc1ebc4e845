"""
Windows-compatible test runner for CtrlColor
"""

import os
import sys
import subprocess


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}")
    print("=" * 50)
    
    try:
        result = subprocess.run(
            [sys.executable, command],
            cwd=os.path.dirname(__file__),
            check=True,
            text=True
        )
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error in {description}: {e}")
        return False


def main():
    """Main test runner"""
    print("🚀 CtrlColor Windows Test Runner")
    print("=" * 60)
    
    # Step 1: Fix environment
    print("\n🔧 Step 1: Applying environment fixes...")
    try:
        exec(open('fix_environment.py').read())
    except Exception as e:
        print(f"⚠️ Environment fix warning: {e}")
    
    # Step 2: Install missing dependencies
    print("\n📦 Step 2: Installing missing dependencies...")
    try:
        exec(open('install_missing.py').read())
    except Exception as e:
        print(f"⚠️ Dependency installation warning: {e}")
    
    # Step 3: Run tests
    print("\n🧪 Step 3: Running comprehensive tests...")
    try:
        exec(open('test_implementation.py').read())
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
    
    print("\n" + "=" * 60)
    print("✅ Test runner completed!")
    print("Check the output above for any issues.")


if __name__ == "__main__":
    main()
