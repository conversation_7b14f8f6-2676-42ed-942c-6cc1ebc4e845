"""
Setup Script for CtrlColor Complete Implementation

Provides easy installation and setup for the complete CtrlColor implementation.
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "CtrlColor Complete Implementation"

# Read requirements
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="ctrlcolor-complete",
    version="1.0.0",
    description="Complete implementation of CtrlColor: Multi-modal Image Colorization",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="CtrlColor Implementation Team",
    author_email="<EMAIL>",
    url="https://github.com/example/ctrlcolor-complete",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Processing",
        "Topic :: Multimedia :: Graphics",
    ],
    python_requires=">=3.8",
    install_requires=[
        "torch>=1.12.0",
        "torchvision>=0.13.0",
        "numpy>=1.21.0",
        "pillow>=8.3.0",
        "opencv-python>=4.6.0",
        "scikit-image>=0.19.0",
        "transformers>=4.20.0",
    ],
    extras_require={
        "full": [
            "diffusers>=0.15.0",
            "pytorch-lightning>=1.6.0",
            "wandb>=0.13.0",
            "lpips>=0.1.4",
            "pytorch-fid>=0.3.0",
            "gradio>=3.35.0",
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
        ],
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "jupyter>=1.0.0",
        ],
        "video": [
            "lightglue>=0.1.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "ctrlcolor-test=test_implementation:main",
            "ctrlcolor-install=install_dependencies:main",
            "ctrlcolor-reproduce=scripts.reproduce_paper_results:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.yaml", "*.yml"],
    },
    keywords=[
        "image colorization",
        "deep learning",
        "computer vision",
        "stable diffusion",
        "controlnet",
        "exemplar-based",
        "multi-modal",
    ],
    project_urls={
        "Bug Reports": "https://github.com/example/ctrlcolor-complete/issues",
        "Source": "https://github.com/example/ctrlcolor-complete",
        "Documentation": "https://github.com/example/ctrlcolor-complete/blob/main/README.md",
    },
)
