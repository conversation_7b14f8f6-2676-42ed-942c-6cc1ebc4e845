"""
Universal Import Configuration for CtrlColor Full Implementation

This module provides a universal solution to resolve all import issues
for the 'full' folder, making it compatible with the entire codebase.

Usage:
    # At the top of any Python file in the 'full' folder:
    from setup_imports import setup_universal_imports
    setup_universal_imports()
    
    # Or simply:
    import setup_imports  # Auto-setup on import
"""

import sys
import os
from pathlib import Path
from typing import List, Optional

class UniversalImportResolver:
    """Universal import resolver for CtrlColor full implementation"""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent.absolute()
        self.project_root = self.current_dir.parent
        self.paths_added = []
        self.setup_complete = False
    
    def add_to_path(self, path: Path, description: str = "") -> bool:
        """Add a path to sys.path if not already present"""
        path_str = str(path)
        if path_str not in sys.path and path.exists():
            sys.path.insert(0, path_str)
            self.paths_added.append((path_str, description))
            return True
        return False
    
    def setup_project_paths(self) -> None:
        """Setup all necessary paths for the project"""
        # Add project root (for cldm, ldm imports)
        self.add_to_path(self.project_root, "Project root (for cldm, ldm)")
        
        # Add full folder itself (for internal imports)
        self.add_to_path(self.current_dir, "Full folder (for internal imports)")
        
        # Add specific subdirectories that might be needed
        subdirs = ['cldm', 'ldm', 'ldm/modules', 'ldm/models', 'ldm/util']
        for subdir in subdirs:
            subdir_path = self.project_root / subdir
            if subdir_path.exists():
                self.add_to_path(subdir_path, f"Subdir: {subdir}")
    
    def verify_critical_imports(self) -> dict:
        """Verify that critical external imports work"""
        results = {}
        
        # Test critical imports
        critical_imports = [
            ('cldm.cldm', ['ControlLDM', 'ControlNet']),
            ('ldm.modules.diffusionmodules.util', ['timestep_embedding', 'conv_nd']),
            ('ldm.modules.encoders.modules', ['FrozenCLIPEmbedder']),
            ('ldm.models.diffusion.ddpm', ['LatentDiffusion', 'DDPM']),
            ('ldm.util', ['instantiate_from_config']),
        ]
        
        for module_name, items in critical_imports:
            try:
                module = __import__(module_name, fromlist=items)
                for item in items:
                    getattr(module, item)  # Verify item exists
                results[module_name] = True
            except (ImportError, AttributeError) as e:
                results[module_name] = False
        
        return results
    
    def setup(self, verbose: bool = False) -> bool:
        """Setup universal imports"""
        if self.setup_complete:
            return True
        
        # Setup paths
        self.setup_project_paths()
        
        # Verify imports work
        import_results = self.verify_critical_imports()
        
        if verbose:
            print("🔧 Universal Import Setup Complete")
            print(f"📁 Project root: {self.project_root}")
            print(f"📁 Full folder: {self.current_dir}")
            print(f"✅ Added {len(self.paths_added)} paths to sys.path")
            
            for path, desc in self.paths_added:
                print(f"   + {desc}: {path}")
            
            print("\n📦 Import Verification:")
            for module, success in import_results.items():
                status = "✅" if success else "❌"
                print(f"   {status} {module}")
        
        self.setup_complete = True
        return all(import_results.values())

# Global resolver instance
_resolver = UniversalImportResolver()

def setup_universal_imports(verbose: bool = False) -> bool:
    """
    Universal function to setup all imports for the full folder
    
    Args:
        verbose: Whether to print setup information
        
    Returns:
        bool: True if all critical imports are working
    """
    return _resolver.setup(verbose=verbose)

def get_import_status() -> dict:
    """Get the status of all import paths and verifications"""
    return {
        'setup_complete': _resolver.setup_complete,
        'paths_added': _resolver.paths_added,
        'project_root': str(_resolver.project_root),
        'current_dir': str(_resolver.current_dir),
        'import_results': _resolver.verify_critical_imports() if _resolver.setup_complete else {}
    }

def reset_imports() -> None:
    """Reset the import setup (for testing purposes)"""
    global _resolver
    # Remove added paths
    for path, _ in _resolver.paths_added:
        if path in sys.path:
            sys.path.remove(path)
    
    # Reset resolver
    _resolver = UniversalImportResolver()

# Auto-setup when this module is imported
setup_universal_imports(verbose=False)
