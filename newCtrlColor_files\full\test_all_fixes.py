#!/usr/bin/env python3
"""
Comprehensive Test Script for CtrlColor Bug Fixes

This script tests all the bug fixes applied to the CtrlColor implementation
to ensure everything works correctly.
"""


import sys
import traceback
import torch
import numpy as np
from typing import Dict, Any

def test_import_fixes():
    """Test that all import issues are resolved"""
    print("=" * 60)
    print("TESTING IMPORT FIXES")
    print("=" * 60)
    
    try:
        # Test contextual loss imports
        from losses.contextual_loss import ContextualLoss, VGG19FeatureExtractor
        print("✅ ContextualLoss imports working")
        
        # Test grayscale loss imports
        from losses.grayscale_loss import GrayscaleLoss, AdaptiveGrayscaleLoss, PerceptualGrayscaleLoss
        print("✅ GrayscaleLoss imports working")
        
        # Test exemplar loss imports
        from losses.exemplar_loss import ExemplarLoss, AdaptiveExemplarLoss, MultiScaleExemplarLoss
        print("✅ ExemplarLoss imports working")
        
        # Test exemplar processor imports
        from modules.exemplar_processor import ExemplarProcessor, Exemplar<PERSON>onditioner, ColorPaletteExtractor
        print("✅ ExemplarProcessor imports working")
        
        # Test exemplar CLDM imports
        from cldm.exemplar_cldm import ExemplarControlLDM
        print("✅ ExemplarControlLDM imports working")
        
        # Test data processor imports
        from data.data_processor import SLICProcessor, ColorJitterer, LabColorProcessor
        print("✅ DataProcessor imports working")
        
        # Test evaluation metrics imports
        from evaluation.metrics import MetricsCalculator, PSNRMetric, SSIMMetric, ColorfulnessMetric
        print("✅ Metrics imports working")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        traceback.print_exc()
        return False

def test_loss_functions():
    """Test that all loss functions work correctly"""
    print("\n" + "=" * 60)
    print("TESTING LOSS FUNCTIONS")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    batch_size, channels, height, width = 2, 3, 64, 64
    
    try:
        # Create test tensors
        generated = torch.randn(batch_size, channels, height, width).to(device)
        exemplar = torch.randn(batch_size, channels, height, width).to(device)
        
        # Test contextual loss
        from losses.contextual_loss import ContextualLoss
        contextual_loss = ContextualLoss().to(device)
        ctx_loss = contextual_loss(generated, exemplar)
        print(f"✅ ContextualLoss: {ctx_loss.item():.4f}")
        
        # Test grayscale loss
        from losses.grayscale_loss import GrayscaleLoss, AdaptiveGrayscaleLoss
        gray_loss = GrayscaleLoss().to(device)
        g_loss = gray_loss(generated, exemplar)
        print(f"✅ GrayscaleLoss: {g_loss.item():.4f}")
        
        adaptive_gray_loss = AdaptiveGrayscaleLoss().to(device)
        ag_loss = adaptive_gray_loss(generated, exemplar)
        print(f"✅ AdaptiveGrayscaleLoss: {ag_loss.item():.4f}")
        
        # Test exemplar loss
        from losses.exemplar_loss import ExemplarLoss
        exemplar_loss = ExemplarLoss().to(device)
        ex_loss = exemplar_loss(generated, exemplar)
        print(f"✅ ExemplarLoss: {ex_loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Loss function test failed: {e}")
        traceback.print_exc()
        return False

def test_exemplar_processing():
    """Test exemplar processing components"""
    print("\n" + "=" * 60)
    print("TESTING EXEMPLAR PROCESSING")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        # Test exemplar processor
        from modules.exemplar_processor import ExemplarProcessor, ExemplarConditioner
        
        processor = ExemplarProcessor().to(device)
        batch_size, channels, height, width = 2, 3, 256, 256
        exemplar_images = torch.randn(batch_size, channels, height, width).to(device)
        
        result = processor(exemplar_images)
        print(f"✅ ExemplarProcessor: CLIP features shape {result['clip_features'].shape}")
        
        # Test exemplar conditioner
        conditioner = ExemplarConditioner().to(device)
        conditioning = conditioner(result['clip_features'])
        print(f"✅ ExemplarConditioner: Conditioning shape {conditioning.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Exemplar processing test failed: {e}")
        traceback.print_exc()
        return False

def test_data_processing():
    """Test data processing components"""
    print("\n" + "=" * 60)
    print("TESTING DATA PROCESSING")
    print("=" * 60)
    
    try:
        from data.data_processor import SLICProcessor, ColorJitterer, LabColorProcessor
        
        # Test SLIC processor
        slic_processor = SLICProcessor(n_segments=20)
        test_image = np.random.randint(0, 255, (128, 128, 3), dtype=np.uint8)
        segments = slic_processor.generate_superpixels(test_image)
        print(f"✅ SLICProcessor: Generated {len(np.unique(segments))} segments")
        
        # Test color jitterer
        jitterer = ColorJitterer(jitter_probability=1.0)
        test_tensor = torch.rand(3, 64, 64)
        jittered = jitterer.apply_jitter(test_tensor)
        print(f"✅ ColorJitterer: Input range [{test_tensor.min():.3f}, {test_tensor.max():.3f}] -> Output range [{jittered.min():.3f}, {jittered.max():.3f}]")
        
        # Test Lab color processor
        lab_processor = LabColorProcessor()
        rgb_image = torch.rand(3, 32, 32)
        lab_image = lab_processor.rgb_to_lab(rgb_image)
        rgb_reconstructed = lab_processor.lab_to_rgb(lab_image)
        error = torch.mean((rgb_image - rgb_reconstructed) ** 2)
        print(f"✅ LabColorProcessor: RGB->Lab->RGB reconstruction error {error:.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data processing test failed: {e}")
        traceback.print_exc()
        return False

def test_evaluation_metrics():
    """Test evaluation metrics"""
    print("\n" + "=" * 60)
    print("TESTING EVALUATION METRICS")
    print("=" * 60)
    
    try:
        from evaluation.metrics import MetricsCalculator, PSNRMetric, SSIMMetric, ColorfulnessMetric
        
        # Create test data
        batch_size = 2
        generated = torch.rand(batch_size, 3, 64, 64)
        reference = torch.rand(batch_size, 3, 64, 64)
        
        # Test individual metrics
        psnr = PSNRMetric.compute_psnr(generated, reference)
        print(f"✅ PSNR: {psnr.mean().item():.4f}")
        
        ssim_metric = SSIMMetric()
        ssim = ssim_metric.compute_ssim(generated, reference)
        print(f"✅ SSIM: {ssim.mean().item():.4f}")
        
        colorfulness = ColorfulnessMetric.compute_colorfulness(generated)
        print(f"✅ Colorfulness: {colorfulness.mean().item():.4f}")
        
        # Test metrics calculator
        calculator = MetricsCalculator()
        metrics = calculator.compute_all_metrics(
            generated_images=generated,
            reference_images=reference
        )
        print(f"✅ MetricsCalculator: Computed {len(metrics)} metrics")
        
        return True
        
    except Exception as e:
        print(f"❌ Evaluation metrics test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🔧 CTRLCOLOR BUG FIXES VERIFICATION")
    print("Testing all components after bug fixes...\n")
    
    tests = [
        ("Import Fixes", test_import_fixes),
        ("Loss Functions", test_loss_functions),
        ("Exemplar Processing", test_exemplar_processing),
        ("Data Processing", test_data_processing),
        ("Evaluation Metrics", test_evaluation_metrics),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✅ PASSED" if passed_test else "❌ FAILED"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All bug fixes verified successfully!")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
