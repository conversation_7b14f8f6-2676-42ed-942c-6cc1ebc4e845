#!/usr/bin/env python3
"""
Test only the cldm import issue
"""

import sys
import os
from pathlib import Path

# Add parent directory to Python path
parent_dir = Path(__file__).parent.parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))

print("🔧 Testing CLDM import only")
print(f"Parent dir: {parent_dir}")
print(f"Python path entries: {sys.path[:3]}")

# Check if cldm directory exists
cldm_dir = parent_dir / "cldm"
print(f"CLDM dir exists: {cldm_dir.exists()} ({cldm_dir})")

if cldm_dir.exists():
    cldm_py = cldm_dir / "cldm.py"
    cldm_init = cldm_dir / "__init__.py"
    print(f"cldm.py exists: {cldm_py.exists()}")
    print(f"__init__.py exists: {cldm_init.exists()}")

# Test direct import of cldm module
print("\n🔍 Testing direct cldm module import...")
try:
    import cldm
    print("✅ import cldm - SUCCESS")
    print(f"   cldm module: {cldm}")
    print(f"   cldm.__file__: {cldm.__file__}")
except Exception as e:
    print(f"❌ import cldm - FAILED: {e}")
    import traceback
    traceback.print_exc()

# Test import of cldm.cldm
print("\n🔍 Testing cldm.cldm import...")
try:
    import cldm.cldm
    print("✅ import cldm.cldm - SUCCESS")
    print(f"   cldm.cldm module: {cldm.cldm}")
except Exception as e:
    print(f"❌ import cldm.cldm - FAILED: {e}")
    import traceback
    traceback.print_exc()

# Test import of specific classes
print("\n🔍 Testing specific class imports...")
try:
    from cldm.cldm import ControlLDM
    print("✅ from cldm.cldm import ControlLDM - SUCCESS")
    print(f"   ControlLDM: {ControlLDM}")
except Exception as e:
    print(f"❌ from cldm.cldm import ControlLDM - FAILED: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 CLDM import test complete!")
