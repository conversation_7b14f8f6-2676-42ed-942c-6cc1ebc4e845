#!/usr/bin/env python3
"""
Test External Import Compatibility

This script tests whether components in the 'full' folder can successfully import
external components from the main CtrlColor codebase.
"""

import sys
import os

# Add parent directory to Python path to access external modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

print("🔧 TESTING EXTERNAL IMPORT COMPATIBILITY")
print(f"Parent directory added to path: {parent_dir}")
print("=" * 70)

def test_cldm_imports():
    """Test importing from cldm module"""
    print("\n📁 TESTING CLDM IMPORTS")
    print("-" * 40)
    
    try:
        # Test cldm.cldm imports
        from cldm.cldm import ControlLDM, ControlNet, ControlledUnetModel
        print("✅ cldm.cldm.ControlLDM")
        print("✅ cldm.cldm.ControlNet") 
        print("✅ cldm.cldm.ControlledUnetModel")
        
        # Test that we can inspect the classes
        print(f"   ControlLDM base classes: {[cls.__name__ for cls in ControlLDM.__mro__[1:3]]}")
        print(f"   ControlNet base classes: {[cls.__name__ for cls in ControlNet.__mro__[1:3]]}")
        
        return True
        
    except Exception as e:
        print(f"❌ CLDM import failed: {e}")
        return False

def test_ldm_imports():
    """Test importing from ldm modules"""
    print("\n📁 TESTING LDM IMPORTS")
    print("-" * 40)
    
    try:
        # Test ldm.modules.diffusionmodules.util
        from ldm.modules.diffusionmodules.util import (
            conv_nd, linear, zero_module, timestep_embedding,
            make_beta_schedule, extract_into_tensor, noise_like
        )
        print("✅ ldm.modules.diffusionmodules.util.conv_nd")
        print("✅ ldm.modules.diffusionmodules.util.linear")
        print("✅ ldm.modules.diffusionmodules.util.zero_module")
        print("✅ ldm.modules.diffusionmodules.util.timestep_embedding")
        print("✅ ldm.modules.diffusionmodules.util.make_beta_schedule")
        print("✅ ldm.modules.diffusionmodules.util.extract_into_tensor")
        print("✅ ldm.modules.diffusionmodules.util.noise_like")
        
        # Test ldm.modules.encoders.modules
        from ldm.modules.encoders.modules import FrozenCLIPEmbedder
        print("✅ ldm.modules.encoders.modules.FrozenCLIPEmbedder")
        
        # Test ldm.models.diffusion.ddpm
        from ldm.models.diffusion.ddpm import LatentDiffusion, DDPM
        print("✅ ldm.models.diffusion.ddpm.LatentDiffusion")
        print("✅ ldm.models.diffusion.ddpm.DDPM")
        
        # Test ldm.util
        from ldm.util import instantiate_from_config, exists, default
        print("✅ ldm.util.instantiate_from_config")
        print("✅ ldm.util.exists")
        print("✅ ldm.util.default")
        
        return True
        
    except Exception as e:
        print(f"❌ LDM import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_folder_with_external_imports():
    """Test that full folder components can use external imports"""
    print("\n📁 TESTING FULL FOLDER WITH EXTERNAL IMPORTS")
    print("-" * 50)
    
    try:
        # Test that our exemplar_cldm can import and use external components
        from cldm.exemplar_cldm import ExemplarControlLDM
        print("✅ Imported ExemplarControlLDM from full/cldm/exemplar_cldm.py")
        
        # Test that it properly inherits from external ControlLDM
        from cldm.cldm import ControlLDM
        assert issubclass(ExemplarControlLDM, ControlLDM), "ExemplarControlLDM should inherit from ControlLDM"
        print("✅ ExemplarControlLDM properly inherits from external ControlLDM")
        
        # Test that our losses can import external utilities
        from losses.contextual_loss import ContextualLoss
        print("✅ Imported ContextualLoss from full/losses/contextual_loss.py")
        
        # Test that data processors work
        from data.data_processor import LabColorProcessor
        print("✅ Imported LabColorProcessor from full/data/data_processor.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Full folder external import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_external_files():
    """Test importing from the specific files mentioned by user"""
    print("\n📁 TESTING SPECIFIC EXTERNAL FILES")
    print("-" * 45)
    
    try:
        # Test the specific files mentioned
        print("Testing C:\\Users\\<USER>\\Documents\\thesis2025\\clone\\newCtrlColor\\cldm\\cldm.py")
        from cldm import cldm
        print(f"✅ Successfully imported cldm module")
        print(f"   Available classes: {[name for name in dir(cldm) if not name.startswith('_') and name[0].isupper()]}")
        
        print("\nTesting C:\\Users\\<USER>\\Documents\\thesis2025\\clone\\newCtrlColor\\ldm\\modules\\diffusionmodules\\util.py")
        from ldm.modules.diffusionmodules import util
        print(f"✅ Successfully imported ldm.modules.diffusionmodules.util")
        print(f"   Available functions: {[name for name in dir(util) if not name.startswith('_') and callable(getattr(util, name))][:10]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Specific file import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_create_model_with_external_deps():
    """Test creating a model that uses external dependencies"""
    print("\n📁 TESTING MODEL CREATION WITH EXTERNAL DEPS")
    print("-" * 50)
    
    try:
        # Import required external utilities
        from ldm.modules.diffusionmodules.util import timestep_embedding
        from cldm.cldm import ControlLDM
        
        # Test that we can use external utilities
        import torch
        test_timesteps = torch.tensor([100, 200])
        embeddings = timestep_embedding(test_timesteps, 320)
        print(f"✅ Successfully used external timestep_embedding function")
        print(f"   Input shape: {test_timesteps.shape}, Output shape: {embeddings.shape}")
        
        # Test that our ExemplarControlLDM can be created and uses external base class
        from cldm.exemplar_cldm import ExemplarControlLDM
        
        # Check inheritance chain
        mro = ExemplarControlLDM.__mro__
        print(f"✅ ExemplarControlLDM inheritance chain: {[cls.__name__ for cls in mro[:5]]}")
        
        # Verify it inherits from external ControlLDM
        assert ControlLDM in mro, "ExemplarControlLDM should inherit from external ControlLDM"
        print("✅ Confirmed inheritance from external ControlLDM")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation with external deps failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all compatibility tests"""
    
    tests = [
        ("CLDM Imports", test_cldm_imports),
        ("LDM Imports", test_ldm_imports), 
        ("Full Folder with External Imports", test_full_folder_with_external_imports),
        ("Specific External Files", test_specific_external_files),
        ("Model Creation with External Deps", test_create_model_with_external_deps),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 70)
    print("EXTERNAL IMPORT COMPATIBILITY TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✅ PASSED" if passed_test else "❌ FAILED"
        print(f"{test_name:35} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL EXTERNAL IMPORT COMPATIBILITY TESTS PASSED!")
        print("✅ Components in 'full' folder can successfully import external components")
        print("✅ The full folder is compatible with the entire codebase")
        return 0
    else:
        print("\n⚠️  Some external import tests failed.")
        print("❌ The full folder has compatibility issues with external components")
        return 1

if __name__ == "__main__":
    sys.exit(main())
