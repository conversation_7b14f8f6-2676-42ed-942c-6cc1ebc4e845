#!/usr/bin/env python3
"""
Test External Imports for CtrlColor

This script specifically tests whether the external imports from the original
ControlLDM codebase are working correctly.
"""

import sys
import os
import traceback

def test_external_imports():
    """Test external imports from the original ControlLDM codebase"""
    print("=" * 60)
    print("TESTING EXTERNAL IMPORTS")
    print("=" * 60)
    
    # Add parent directory to path to access original codebase
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    
    print(f"Added to Python path: {parent_dir}")
    
    # Test 1: Check if original cldm module exists
    try:
        import cldm
        print(f"✅ Found cldm module at: {cldm.__file__}")
    except ImportError as e:
        print(f"❌ cldm module not found: {e}")
        return False
    
    # Test 2: Check if cldm.cldm exists
    try:
        from cldm.cldm import ControlLDM
        print(f"✅ Successfully imported ControlLDM from cldm.cldm")
    except ImportError as e:
        print(f"❌ Failed to import ControlLDM: {e}")
        return False
    
    # Test 3: Check if ldm module exists
    try:
        import ldm
        print(f"✅ Found ldm module at: {ldm.__file__}")
    except ImportError as e:
        print(f"❌ ldm module not found: {e}")
        return False
    
    # Test 4: Check if ldm.modules.encoders.modules exists
    try:
        from ldm.modules.encoders.modules import FrozenCLIPEmbedder
        print(f"✅ Successfully imported FrozenCLIPEmbedder from ldm.modules.encoders.modules")
    except ImportError as e:
        print(f"❌ Failed to import FrozenCLIPEmbedder: {e}")
        return False
    
    # Test 5: Test ExemplarControlLDM import with real external dependencies
    try:
        from cldm.exemplar_cldm import ExemplarControlLDM
        print(f"✅ Successfully imported ExemplarControlLDM with real external dependencies")
        
        # Try to create an instance
        model = ExemplarControlLDM(
            unet_config={"in_channels": 4, "out_channels": 4},
            control_stage_config={"in_channels": 4},
            control_key="hint",
        )
        print(f"✅ Successfully created ExemplarControlLDM instance")
        print(f"   - Base class: {type(model).__bases__[0].__name__}")
        print(f"   - Module: {type(model).__module__}")
        
    except Exception as e:
        print(f"❌ Failed to create ExemplarControlLDM: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_directory_structure():
    """Test that the expected directory structure exists"""
    print("\n" + "=" * 60)
    print("TESTING DIRECTORY STRUCTURE")
    print("=" * 60)
    
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    expected_dirs = [
        "cldm",
        "ldm",
        "ldm/modules",
        "ldm/modules/encoders",
    ]
    
    expected_files = [
        "cldm/cldm.py",
        "ldm/modules/encoders/modules.py",
    ]
    
    all_good = True
    
    for dir_path in expected_dirs:
        full_path = os.path.join(parent_dir, dir_path)
        if os.path.exists(full_path):
            print(f"✅ Directory exists: {dir_path}")
        else:
            print(f"❌ Directory missing: {dir_path}")
            all_good = False
    
    for file_path in expected_files:
        full_path = os.path.join(parent_dir, file_path)
        if os.path.exists(full_path):
            print(f"✅ File exists: {file_path}")
        else:
            print(f"❌ File missing: {file_path}")
            all_good = False
    
    return all_good

def main():
    """Run all external import tests"""
    print("🔧 CTRLCOLOR EXTERNAL IMPORTS TEST")
    print("Testing external imports from original ControlLDM codebase...\n")
    
    # Test directory structure first
    structure_ok = test_directory_structure()
    
    # Test external imports
    imports_ok = test_external_imports()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    print(f"Directory Structure: {'✅ PASSED' if structure_ok else '❌ FAILED'}")
    print(f"External Imports:    {'✅ PASSED' if imports_ok else '❌ FAILED'}")
    
    if structure_ok and imports_ok:
        print("\n🎉 All external import tests passed!")
        print("The full folder can now properly import from the original ControlLDM codebase.")
        return 0
    else:
        print("\n⚠️  Some external import tests failed.")
        print("This means the full folder cannot access the original ControlLDM dependencies.")
        print("This is expected if you're running the full folder in isolation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
