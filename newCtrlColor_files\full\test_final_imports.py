#!/usr/bin/env python3
"""
Final test to verify all imports work after adding __init__.py files
"""

print("🔧 FINAL IMPORT TEST")
print("=" * 50)

# Test the universal import config
import import_config

print("✅ import_config loaded")

# Test external imports
print("\n📁 TESTING EXTERNAL IMPORTS")
print("-" * 30)

try:
    from cldm.cldm import ControlLDM, ControlNet, ControlledUnetModel
    print("✅ cldm.cldm.ControlLDM")
    print("✅ cldm.cldm.ControlNet") 
    print("✅ cldm.cldm.ControlledUnetModel")
    external_cldm_ok = True
except Exception as e:
    print(f"❌ CLDM imports failed: {e}")
    external_cldm_ok = False

try:
    from ldm.modules.diffusionmodules.util import timestep_embedding, conv_nd, zero_module
    from ldm.modules.encoders.modules import FrozenCLIPEmbedder
    from ldm.models.diffusion.ddpm import LatentDiffusion, DDPM
    from ldm.util import instantiate_from_config
    print("✅ ldm.modules.diffusionmodules.util.*")
    print("✅ ldm.modules.encoders.modules.FrozenCLIPEmbedder")
    print("✅ ldm.models.diffusion.ddpm.*")
    print("✅ ldm.util.instantiate_from_config")
    external_ldm_ok = True
except Exception as e:
    print(f"❌ LDM imports failed: {e}")
    external_ldm_ok = False

# Test internal imports
print("\n📁 TESTING INTERNAL IMPORTS")
print("-" * 30)

try:
    from losses.contextual_loss import ContextualLoss
    from modules.exemplar_processor import ExemplarProcessor
    from data.data_processor import LabColorProcessor
    from cldm.exemplar_cldm import ExemplarControlLDM
    print("✅ losses.contextual_loss.ContextualLoss")
    print("✅ modules.exemplar_processor.ExemplarProcessor")
    print("✅ data.data_processor.LabColorProcessor")
    print("✅ cldm.exemplar_cldm.ExemplarControlLDM")
    internal_ok = True
except Exception as e:
    print(f"❌ Internal imports failed: {e}")
    import traceback
    traceback.print_exc()
    internal_ok = False

# Test inheritance
print("\n🔗 TESTING INHERITANCE")
print("-" * 30)

try:
    from cldm.cldm import ControlLDM
    from cldm.exemplar_cldm import ExemplarControlLDM
    
    assert issubclass(ExemplarControlLDM, ControlLDM)
    print("✅ ExemplarControlLDM properly inherits from ControlLDM")
    inheritance_ok = True
except Exception as e:
    print(f"❌ Inheritance test failed: {e}")
    inheritance_ok = False

# Summary
print("\n" + "=" * 50)
print("FINAL IMPORT TEST SUMMARY")
print("=" * 50)

print(f"External CLDM imports: {'✅ PASSED' if external_cldm_ok else '❌ FAILED'}")
print(f"External LDM imports:  {'✅ PASSED' if external_ldm_ok else '❌ FAILED'}")
print(f"Internal imports:      {'✅ PASSED' if internal_ok else '❌ FAILED'}")
print(f"Inheritance test:      {'✅ PASSED' if inheritance_ok else '❌ FAILED'}")

all_passed = external_cldm_ok and external_ldm_ok and internal_ok and inheritance_ok

if all_passed:
    print("\n🎉 ALL IMPORTS WORKING PERFECTLY!")
    print("✅ Universal import configuration is complete!")
    print("✅ Full folder is compatible with entire codebase!")
else:
    print("\n⚠️  Some imports still failing.")

print(f"\nOverall result: {'SUCCESS' if all_passed else 'PARTIAL SUCCESS'}")
