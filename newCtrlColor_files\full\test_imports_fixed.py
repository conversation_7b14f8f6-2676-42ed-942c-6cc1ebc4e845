#!/usr/bin/env python3
"""
Test script to verify that external imports work correctly after fixing the Python path.
"""

import sys
import os

# Add parent directory to Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

print("🔧 CTRLCOLOR IMPORT COMPATIBILITY TEST")
print("Testing imports after fixing Python path...\n")

def test_external_imports():
    """Test that we can import from the main codebase"""
    print("=" * 60)
    print("TESTING EXTERNAL IMPORTS")
    print("=" * 60)
    
    try:
        # Test cldm imports
        from cldm.cldm import ControlLDM, ControlNet
        print("✅ Successfully imported ControlLDM and ControlNet from cldm.cldm")
        
        # Test ldm imports  
        from ldm.modules.encoders.modules import FrozenCLIPEmbedder
        print("✅ Successfully imported FrozenCLIPEmbedder from ldm.modules.encoders.modules")
        
        # Test ldm diffusion models
        from ldm.models.diffusion.ddpm import LatentDiffusion
        print("✅ Successfully imported LatentDiffusion from ldm.models.diffusion.ddpm")
        
        return True
        
    except Exception as e:
        print(f"❌ External import failed: {e}")
        return False

def test_internal_imports():
    """Test that internal imports within full folder work"""
    print("\n" + "=" * 60)
    print("TESTING INTERNAL IMPORTS")
    print("=" * 60)
    
    try:
        # Test internal imports
        from cldm.exemplar_cldm import ExemplarControlLDM
        print("✅ Successfully imported ExemplarControlLDM")
        
        from losses.contextual_loss import ContextualLoss
        print("✅ Successfully imported ContextualLoss")
        
        from modules.exemplar_processor import ExemplarProcessor
        print("✅ Successfully imported ExemplarProcessor")
        
        from data.data_processor import LabColorProcessor
        print("✅ Successfully imported LabColorProcessor")
        
        return True
        
    except Exception as e:
        print(f"❌ Internal import failed: {e}")
        return False

def test_exemplar_cldm_creation():
    """Test creating ExemplarControlLDM with proper parameters"""
    print("\n" + "=" * 60)
    print("TESTING EXEMPLAR CLDM CREATION")
    print("=" * 60)
    
    try:
        from cldm.exemplar_cldm import ExemplarControlLDM
        
        # Create with minimal required parameters
        model = ExemplarControlLDM(
            control_stage_config={
                "target": "cldm.cldm.ControlNet",
                "params": {
                    "image_size": 32,
                    "in_channels": 4,
                    "model_channels": 320,
                    "hint_channels": 3,
                    "num_res_blocks": 2,
                    "attention_resolutions": [4, 2, 1],
                    "channel_mult": [1, 2, 4, 4],
                    "num_heads": 8,
                    "use_spatial_transformer": True,
                    "context_dim": 768,
                }
            },
            control_key="hint",
            timesteps=1000,
            load_loss=False,
            masked_image="masked_image",
            mask="mask"
        )
        
        print("✅ Successfully created ExemplarControlLDM instance")
        print(f"   - Type: {type(model).__name__}")
        print(f"   - Base classes: {[cls.__name__ for cls in type(model).__mro__[1:4]]}")
        
        return True
        
    except Exception as e:
        print(f"❌ ExemplarControlLDM creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    external_ok = test_external_imports()
    internal_ok = test_internal_imports()
    creation_ok = test_exemplar_cldm_creation()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    print(f"External Imports: {'✅ PASSED' if external_ok else '❌ FAILED'}")
    print(f"Internal Imports: {'✅ PASSED' if internal_ok else '❌ FAILED'}")
    print(f"Model Creation:   {'✅ PASSED' if creation_ok else '❌ FAILED'}")
    
    if external_ok and internal_ok and creation_ok:
        print("\n🎉 All import compatibility tests passed!")
        print("The full folder is now compatible with the main codebase.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
