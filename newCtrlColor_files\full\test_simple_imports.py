#!/usr/bin/env python3
"""
Simple test to verify imports work
"""

print("🔧 Testing simple imports...")

# Test the universal import config
import import_config

print("✅ import_config loaded")

# Test external imports
try:
    from cldm.cldm import ControlLDM
    print("✅ External import: ControlLDM")
except Exception as e:
    print(f"❌ External import failed: {e}")

# Test internal imports
try:
    from losses.contextual_loss import ContextualLoss
    print("✅ Internal import: ContextualLoss")
except Exception as e:
    print(f"❌ Internal import failed: {e}")

try:
    from modules.exemplar_processor import ExemplarProcessor
    print("✅ Internal import: ExemplarProcessor")
except Exception as e:
    print(f"❌ Internal import failed: {e}")

try:
    from data.data_processor import LabColorProcessor
    print("✅ Internal import: LabColorProcessor")
except Exception as e:
    print(f"❌ Internal import failed: {e}")

print("🎉 Simple import test complete!")
