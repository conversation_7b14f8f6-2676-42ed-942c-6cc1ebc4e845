#!/usr/bin/env python3
"""
Test Universal Import Configuration

This script tests all the different ways to use the universal import configuration.
"""

def test_method_1_import_config():
    """Test Method 1: Using import_config.py"""
    print("🔧 METHOD 1: Using import_config.py")
    print("-" * 50)
    
    try:
        # Clear any existing imports to test fresh
        import sys
        modules_to_remove = [m for m in sys.modules.keys() if m.startswith(('cldm', 'ldm'))]
        for module in modules_to_remove:
            del sys.modules[module]
        
        # Method 1: Import the simple config
        import import_config
        
        # Test that external imports now work
        from cldm.cldm import ControlLDM
        from ldm.modules.diffusionmodules.util import timestep_embedding
        
        print("✅ Method 1 SUCCESS: import_config.py works!")
        print(f"   - ControlLDM: {ControlLDM}")
        print(f"   - timestep_embedding: {timestep_embedding}")
        return True
        
    except Exception as e:
        print(f"❌ Method 1 FAILED: {e}")
        return False

def test_method_2_setup_imports():
    """Test Method 2: Using setup_imports.py"""
    print("\n🔧 METHOD 2: Using setup_imports.py")
    print("-" * 50)
    
    try:
        from setup_imports import setup_universal_imports
        
        # Setup with verbose output
        success = setup_universal_imports(verbose=True)
        
        if success:
            print("✅ Method 2 SUCCESS: setup_imports.py works!")
        else:
            print("❌ Method 2 FAILED: Some imports not working")
        
        return success
        
    except Exception as e:
        print(f"❌ Method 2 FAILED: {e}")
        return False

def test_method_3_package_import():
    """Test Method 3: Importing the full package"""
    print("\n🔧 METHOD 3: Importing full package")
    print("-" * 50)
    
    try:
        # This should automatically setup imports via __init__.py
        import sys
        import os
        
        # Add parent to path to import the full package
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)
        
        # Import the full package (this triggers __init__.py)
        import full
        
        # Test that external imports work
        from cldm.cldm import ControlNet
        from ldm.util import instantiate_from_config
        
        print("✅ Method 3 SUCCESS: Package import works!")
        print(f"   - ControlNet: {ControlNet}")
        print(f"   - instantiate_from_config: {instantiate_from_config}")
        return True
        
    except Exception as e:
        print(f"❌ Method 3 FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_critical_imports():
    """Test that all critical external imports work"""
    print("\n🔧 TESTING ALL CRITICAL IMPORTS")
    print("-" * 50)
    
    critical_imports = [
        # CLDM imports
        ('cldm.cldm', 'ControlLDM'),
        ('cldm.cldm', 'ControlNet'),
        ('cldm.cldm', 'ControlledUnetModel'),
        
        # LDM utility imports
        ('ldm.modules.diffusionmodules.util', 'timestep_embedding'),
        ('ldm.modules.diffusionmodules.util', 'conv_nd'),
        ('ldm.modules.diffusionmodules.util', 'zero_module'),
        ('ldm.modules.diffusionmodules.util', 'extract_into_tensor'),
        
        # LDM model imports
        ('ldm.models.diffusion.ddpm', 'LatentDiffusion'),
        ('ldm.models.diffusion.ddpm', 'DDPM'),
        
        # LDM encoder imports
        ('ldm.modules.encoders.modules', 'FrozenCLIPEmbedder'),
        
        # LDM util imports
        ('ldm.util', 'instantiate_from_config'),
        ('ldm.util', 'exists'),
        ('ldm.util', 'default'),
    ]
    
    success_count = 0
    total_count = len(critical_imports)
    
    for module_name, item_name in critical_imports:
        try:
            module = __import__(module_name, fromlist=[item_name])
            item = getattr(module, item_name)
            print(f"✅ {module_name}.{item_name}")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name}.{item_name} - {e}")
    
    print(f"\nImport Success Rate: {success_count}/{total_count} ({100*success_count/total_count:.1f}%)")
    return success_count == total_count

def test_internal_imports_with_external():
    """Test that internal full folder imports work with external dependencies"""
    print("\n🔧 TESTING INTERNAL + EXTERNAL IMPORTS")
    print("-" * 50)
    
    try:
        # Test internal imports that depend on external modules
        from cldm.exemplar_cldm import ExemplarControlLDM
        from losses.contextual_loss import ContextualLoss
        from modules.exemplar_processor import ExemplarProcessor
        from data.data_processor import LabColorProcessor
        
        # Verify inheritance from external classes
        from cldm.cldm import ControlLDM
        assert issubclass(ExemplarControlLDM, ControlLDM)
        
        print("✅ All internal imports with external dependencies work!")
        print(f"   - ExemplarControlLDM inherits from: {ControlLDM.__name__}")
        print(f"   - ContextualLoss: {ContextualLoss}")
        print(f"   - ExemplarProcessor: {ExemplarProcessor}")
        print(f"   - LabColorProcessor: {LabColorProcessor}")
        
        return True
        
    except Exception as e:
        print(f"❌ Internal+External imports failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test all universal import methods"""
    print("🚀 UNIVERSAL IMPORT CONFIGURATION TEST")
    print("=" * 70)
    
    tests = [
        ("Simple import_config.py", test_method_1_import_config),
        ("Advanced setup_imports.py", test_method_2_setup_imports),
        ("Package-level import", test_method_3_package_import),
        ("All critical imports", test_all_critical_imports),
        ("Internal + External imports", test_internal_imports_with_external),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 70)
    print("UNIVERSAL IMPORT CONFIGURATION TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:30} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 UNIVERSAL IMPORT CONFIGURATION IS WORKING PERFECTLY!")
        print("\n📋 HOW TO USE:")
        print("   Method 1 (Simple):    import import_config")
        print("   Method 2 (Advanced):  from setup_imports import setup_universal_imports; setup_universal_imports()")
        print("   Method 3 (Package):   import full  # Auto-setup via __init__.py")
        return 0
    else:
        print("\n⚠️  Some import configuration tests failed.")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
