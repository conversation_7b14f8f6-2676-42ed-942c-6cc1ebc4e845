"""
Base Trainer for CtrlColor Multi-stage Training

Implements the base training infrastructure for the 4-stage training process:
Stage 1: Stable Diffusion fine-tuning (15K steps)
Stage 2: Stroke control training (65K steps)  
Stage 3: Exemplar control training (100K steps)
Stage 4: Deformable VAE training (9K steps)

Reference: CtrlColor paper Section 4.1 Implementation Details
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import pytorch_lightning as pl
from typing import Dict, Any, Optional, List
import os
import json
from datetime import datetime
import wandb

try:
    from transformers import get_cosine_schedule_with_warmup
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False


class BaseCtrlColorTrainer(pl.LightningModule):
    """
    Base trainer for CtrlColor multi-stage training
    
    Provides common functionality for all training stages:
    - Learning rate scheduling
    - Loss computation and logging
    - Checkpoint management
    - Validation loops
    """
    
    def __init__(self,
                 model: nn.Module,
                 learning_rate: float = 1e-4,
                 weight_decay: float = 0.01,
                 warmup_steps: int = 1000,
                 max_steps: int = 100000,
                 save_every_n_steps: int = 5000,
                 validate_every_n_steps: int = 1000,
                 gradient_clip_val: float = 1.0,
                 use_ema: bool = True,
                 ema_decay: float = 0.9999,
                 stage_name: str = "base",
                 experiment_name: Optional[str] = None,
                 **kwargs):
        """
        Initialize base trainer
        
        Args:
            model: Model to train
            learning_rate: Initial learning rate
            weight_decay: Weight decay for optimizer
            warmup_steps: Number of warmup steps
            max_steps: Maximum training steps
            save_every_n_steps: Save checkpoint every N steps
            validate_every_n_steps: Run validation every N steps
            gradient_clip_val: Gradient clipping value
            use_ema: Whether to use exponential moving average
            ema_decay: EMA decay rate
            stage_name: Name of training stage
            experiment_name: Name of experiment for logging
        """
        super().__init__()
        
        self.model = model
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        self.warmup_steps = warmup_steps
        self.max_steps = max_steps
        self.save_every_n_steps = save_every_n_steps
        self.validate_every_n_steps = validate_every_n_steps
        self.gradient_clip_val = gradient_clip_val
        self.use_ema = use_ema
        self.ema_decay = ema_decay
        self.stage_name = stage_name
        self.experiment_name = experiment_name or f"ctrlcolor_{stage_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Initialize EMA if requested
        if self.use_ema:
            self.ema_model = self._create_ema_model()
        
        # Training state
        self.step_count = 0
        self.best_val_loss = float('inf')
        
        # Save hyperparameters
        self.save_hyperparameters(ignore=['model'])
    
    def _create_ema_model(self):
        """Create EMA model for stable training"""
        try:
            from copy import deepcopy
            ema_model = deepcopy(self.model)
            for param in ema_model.parameters():
                param.requires_grad = False
            return ema_model
        except:
            return None
    
    def _update_ema(self):
        """Update EMA model parameters"""
        if self.ema_model is None:
            return
        
        with torch.no_grad():
            for ema_param, param in zip(self.ema_model.parameters(), self.model.parameters()):
                ema_param.data.mul_(self.ema_decay).add_(param.data, alpha=1 - self.ema_decay)
    
    def configure_optimizers(self):
        """Configure optimizer and learning rate scheduler"""
        # Get trainable parameters
        params = [p for p in self.model.parameters() if p.requires_grad]
        
        # Initialize optimizer
        optimizer = optim.AdamW(
            params,
            lr=self.learning_rate,
            weight_decay=self.weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # Initialize scheduler
        if TRANSFORMERS_AVAILABLE:
            scheduler = get_cosine_schedule_with_warmup(
                optimizer,
                num_warmup_steps=self.warmup_steps,
                num_training_steps=self.max_steps
            )
        else:
            # Fallback scheduler
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=self.max_steps,
                eta_min=self.learning_rate * 0.01
            )
        
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "interval": "step",
                "frequency": 1,
            },
        }
    
    def training_step(self, batch: Dict[str, Any], batch_idx: int) -> torch.Tensor:
        """
        Training step - to be implemented by subclasses
        
        Args:
            batch: Training batch
            batch_idx: Batch index
            
        Returns:
            Loss tensor
        """
        raise NotImplementedError("Subclasses must implement training_step")
    
    def validation_step(self, batch: Dict[str, Any], batch_idx: int) -> Dict[str, torch.Tensor]:
        """
        Validation step - to be implemented by subclasses
        
        Args:
            batch: Validation batch
            batch_idx: Batch index
            
        Returns:
            Dictionary of validation metrics
        """
        raise NotImplementedError("Subclasses must implement validation_step")
    
    def on_train_batch_end(self, outputs, batch, batch_idx):
        """Called after each training batch"""
        self.step_count += 1
        
        # Update EMA
        if self.use_ema:
            self._update_ema()
        
        # Log learning rate
        current_lr = self.optimizers().param_groups[0]['lr']
        self.log('train/learning_rate', current_lr, on_step=True, on_epoch=False)
        
        # Save checkpoint periodically
        if self.step_count % self.save_every_n_steps == 0:
            self._save_checkpoint()
    
    def _save_checkpoint(self):
        """Save model checkpoint"""
        checkpoint_dir = f"checkpoints/{self.experiment_name}"
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        checkpoint_path = os.path.join(checkpoint_dir, f"step_{self.step_count}.ckpt")
        
        # Save main model
        torch.save({
            'step': self.step_count,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizers().state_dict(),
            'best_val_loss': self.best_val_loss,
            'hyperparameters': self.hparams,
        }, checkpoint_path)
        
        # Save EMA model if available
        if self.ema_model is not None:
            ema_path = os.path.join(checkpoint_dir, f"ema_step_{self.step_count}.ckpt")
            torch.save({
                'step': self.step_count,
                'ema_model_state_dict': self.ema_model.state_dict(),
            }, ema_path)
        
        print(f"Checkpoint saved: {checkpoint_path}")
    
    def load_checkpoint(self, checkpoint_path: str, load_ema: bool = False):
        """Load model from checkpoint"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        if load_ema and 'ema_model_state_dict' in checkpoint:
            if self.ema_model is not None:
                self.ema_model.load_state_dict(checkpoint['ema_model_state_dict'])
            print(f"EMA model loaded from {checkpoint_path}")
        else:
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.step_count = checkpoint.get('step', 0)
            self.best_val_loss = checkpoint.get('best_val_loss', float('inf'))
            print(f"Model loaded from {checkpoint_path}")
    
    def log_metrics(self, metrics: Dict[str, float], prefix: str = "train"):
        """Log metrics to wandb and lightning"""
        for key, value in metrics.items():
            self.log(f'{prefix}/{key}', value, on_step=True, on_epoch=False)
    
    def setup_logging(self, project_name: str = "ctrlcolor"):
        """Setup wandb logging"""
        try:
            wandb.init(
                project=project_name,
                name=self.experiment_name,
                config=dict(self.hparams)
            )
        except:
            print("Warning: wandb logging not available")
    
    def get_model_for_inference(self) -> nn.Module:
        """Get model for inference (EMA if available, otherwise main model)"""
        if self.use_ema and self.ema_model is not None:
            return self.ema_model
        return self.model


class TrainingConfig:
    """Configuration class for training stages"""
    
    def __init__(self,
                 stage_name: str,
                 max_steps: int,
                 learning_rate: float = 1e-4,
                 batch_size: int = 8,
                 gradient_accumulation_steps: int = 1,
                 warmup_steps: int = 1000,
                 save_every_n_steps: int = 5000,
                 validate_every_n_steps: int = 1000,
                 **kwargs):
        self.stage_name = stage_name
        self.max_steps = max_steps
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.gradient_accumulation_steps = gradient_accumulation_steps
        self.warmup_steps = warmup_steps
        self.save_every_n_steps = save_every_n_steps
        self.validate_every_n_steps = validate_every_n_steps
        
        # Store additional kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'TrainingConfig':
        """Create config from dictionary"""
        return cls(**config_dict)
    
    def save(self, path: str):
        """Save config to JSON file"""
        with open(path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, path: str) -> 'TrainingConfig':
        """Load config from JSON file"""
        with open(path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


# Predefined training configurations for each stage
STAGE_CONFIGS = {
    'stage1_sd': TrainingConfig(
        stage_name='stage1_sd',
        max_steps=15000,
        learning_rate=1e-5,
        batch_size=4,
        warmup_steps=500,
        description="Stage 1: Stable Diffusion fine-tuning"
    ),
    'stage2_stroke': TrainingConfig(
        stage_name='stage2_stroke',
        max_steps=65000,
        learning_rate=1e-4,
        batch_size=8,
        warmup_steps=1000,
        description="Stage 2: Stroke control training"
    ),
    'stage3_exemplar': TrainingConfig(
        stage_name='stage3_exemplar',
        max_steps=100000,
        learning_rate=1e-4,
        batch_size=6,
        warmup_steps=2000,
        description="Stage 3: Exemplar control training"
    ),
    'stage4_deformable': TrainingConfig(
        stage_name='stage4_deformable',
        max_steps=9000,
        learning_rate=1e-4,
        batch_size=4,
        warmup_steps=500,
        description="Stage 4: Deformable VAE training"
    )
}


def get_stage_config(stage_name: str) -> TrainingConfig:
    """Get predefined configuration for training stage"""
    if stage_name not in STAGE_CONFIGS:
        raise ValueError(f"Unknown stage: {stage_name}. Available: {list(STAGE_CONFIGS.keys())}")
    return STAGE_CONFIGS[stage_name]


# Test function
def test_base_trainer():
    """Test base trainer functionality"""
    print("Testing Base Trainer...")
    
    # Create dummy model
    model = nn.Linear(10, 1)
    
    # Create trainer
    trainer = BaseCtrlColorTrainer(
        model=model,
        max_steps=1000,
        stage_name="test"
    )
    
    print(f"✅ Trainer initialized: {trainer.stage_name}")
    print(f"✅ Max steps: {trainer.max_steps}")
    print(f"✅ EMA enabled: {trainer.use_ema}")
    
    # Test configuration
    config = get_stage_config('stage1_sd')
    print(f"✅ Stage 1 config: {config.max_steps} steps")
    
    return trainer


if __name__ == "__main__":
    test_base_trainer()
