

"""

Stage 1 Training: Stable Diffusion Fine-tuning for CtrlColor



Implements the first stage of CtrlColor training:

- Fine-tune Stable Diffusion on colorization task

- 15K training steps

- Focus on unconditional colorization

- Prepare base model for control integration



Reference: CtrlColor paper Section 4.1 Implementation Details

"""



import os

import sys

from typing import Any, Dict, Optional, Tuple



import numpy as np

import torch

import torch.nn as nn

import torch.nn.functional as F

from torch.utils.data import DataLoader, Dataset





try:

    from diffusers import DDPMScheduler



    DIFFUSERS_AVAILABLE = True

except ImportError:

    DIFFUSERS_AVAILABLE = False

    print("Warning: diffusers not available, using dummy scheduler")





class ColorizationDataset(Dataset):

    """

    Dataset for colorization training



    Loads RGB images and converts them to grayscale for training

    """



    def __init__(self, image_paths: list, image_size: int = 512, transform=None):

        """

        Initialize colorization dataset



        Args:

            image_paths: List of paths to RGB images

            image_size: Target image size

            transform: Optional transforms to apply

        """

        self.image_paths = image_paths

        self.image_size = image_size

        self.transform = transform

        self.lab_processor = LabColorProcessor()



    def __len__(self):

        return len(self.image_paths)



    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:

        """

        Get training sample



        Returns:

            Dictionary containing:

            - rgb_image: Original RGB image [3, H, W]

            - lab_image: Lab color space image [3, H, W]

            - l_channel: L channel only [1, H, W]

            - ab_channels: ab channels [2, H, W]

        """

        # Load image (dummy implementation for now)

        # In practice, load from self.image_paths[idx]

        rgb_image = torch.rand(3, self.image_size, self.image_size)



        # Apply transforms if provided

        if self.transform:

            rgb_image = self.transform(rgb_image)



        # Convert to Lab color space

        lab_image = self.lab_processor.rgb_to_lab(rgb_image)



        # Split L and ab channels

        l_channel = lab_image[0:1]  # [1, H, W]

        ab_channels = lab_image[1:3]  # [2, H, W]



        return {

            "rgb_image": rgb_image,

            "lab_image": lab_image,

            "l_channel": l_channel,

            "ab_channels": ab_channels,

            "image_path": self.image_paths[idx]

            if idx < len(self.image_paths)

            else f"dummy_{idx}",

        }





class Stage1SDTrainer(BaseCtrlColorTrainer):

    """

    Stage 1 Trainer: Stable Diffusion Fine-tuning



    Fine-tunes Stable Diffusion for unconditional colorization:

    - Input: L channel (grayscale)

    - Output: ab channels (color)

    - Loss: MSE between predicted and ground truth ab channels

    """



    def __init__(

        self,

        model: nn.Module,

        vae_model: Optional[nn.Module] = None,

        noise_scheduler: Optional[Any] = None,

        **kwargs,

    ):

        """

        Initialize Stage 1 trainer



        Args:

            model: Stable Diffusion UNet model

            vae_model: VAE for encoding/decoding

            noise_scheduler: Noise scheduler for diffusion process

        """

        # Get stage 1 configuration

        config = get_stage_config("stage1_sd")



        # Update kwargs with stage config

        kwargs.update(

            {

                "max_steps": config.max_steps,

                "learning_rate": config.learning_rate,

                "warmup_steps": config.warmup_steps,

                "stage_name": config.stage_name,

            }

        )



        super().__init__(model=model, **kwargs)



        self.vae_model = vae_model



        # Initialize noise scheduler

        if noise_scheduler is None and DIFFUSERS_AVAILABLE:

            self.noise_scheduler = DDPMScheduler(

                num_train_timesteps=1000,

                beta_start=0.00085,

                beta_end=0.012,

                beta_schedule="scaled_linear",

            )

        else:

            self.noise_scheduler = noise_scheduler



        # Initialize metrics calculator

        self.metrics_calculator = MetricsCalculator()



        # Training statistics

        self.train_losses = []

        self.val_losses = []



    def encode_images(self, images: torch.Tensor) -> torch.Tensor:

        """

        Encode images to latent space using VAE



        Args:

            images: Input images [B, C, H, W]



        Returns:

            Latent representations [B, latent_dim, H//8, W//8]

        """

        if self.vae_model is not None:

            with torch.no_grad():

                latents = self.vae_model.encode(images).latent_dist.sample()

                latents = latents * 0.18215  # Scale factor for SD

            return latents

        else:

            # Dummy latent encoding

            B, C, H, W = images.shape

            return torch.randn(B, 4, H // 8, W // 8, device=images.device)



    def decode_latents(self, latents: torch.Tensor) -> torch.Tensor:

        """

        Decode latents to image space using VAE



        Args:

            latents: Latent representations [B, latent_dim, H//8, W//8]



        Returns:

            Decoded images [B, C, H, W]

        """

        if self.vae_model is not None:

            with torch.no_grad():

                latents = latents / 0.18215  # Unscale

                images = self.vae_model.decode(latents).sample

            return images

        else:

            # Dummy latent decoding

            B, C, H, W = latents.shape

            return torch.randn(B, 3, H * 8, W * 8, device=latents.device)



    def add_noise(

        self, latents: torch.Tensor, timesteps: torch.Tensor

    ) -> Tuple[torch.Tensor, torch.Tensor]:

        """

        Add noise to latents according to diffusion schedule



        Args:

            latents: Clean latents [B, C, H, W]

            timesteps: Timesteps [B]



        Returns:

            Tuple of (noisy_latents, noise)

        """

        noise = torch.randn_like(latents)



        if self.noise_scheduler is not None:

            noisy_latents = self.noise_scheduler.add_noise(latents, noise, timesteps)

        else:

            # Simple noise addition fallback

            alpha = 1.0 - timesteps.float() / 1000.0

            alpha = alpha.view(-1, 1, 1, 1)

            noisy_latents = alpha * latents + (1 - alpha) * noise



        return noisy_latents, noise



    def training_step(self, batch: Dict[str, Any], batch_idx: int) -> torch.Tensor:

        """

        Training step for Stage 1



        Args:

            batch: Training batch containing RGB images and Lab channels

            batch_idx: Batch index



        Returns:

            Loss tensor

        """

        # Extract data from batch

        rgb_images = batch["rgb_image"]  # [B, 3, H, W]

        l_channel = batch["l_channel"]  # [B, 1, H, W]

        ab_channels = batch["ab_channels"]  # [B, 2, H, W]



        batch_size = rgb_images.shape[0]



        # Encode RGB images to latents (target)

        target_latents = self.encode_images(rgb_images)



        # Encode L channel to latents (condition)

        l_channel_3ch = l_channel.repeat(1, 3, 1, 1)  # Convert to 3-channel

        condition_latents = self.encode_images(l_channel_3ch)



        # Sample random timesteps

        timesteps = torch.randint(

            0, 1000, (batch_size,), device=self.device, dtype=torch.long

        )



        # Add noise to target latents

        noisy_latents, noise = self.add_noise(target_latents, timesteps)



        # Concatenate condition with noisy latents

        model_input = torch.cat([noisy_latents, condition_latents], dim=1)



        # Predict noise

        noise_pred = self.model(model_input, timesteps)



        # Compute loss

        loss = F.mse_loss(noise_pred, noise)



        # Log metrics

        self.log_metrics({"loss": loss.item(), "step": self.step_count}, prefix="train")



        self.train_losses.append(loss.item())



        return loss



    def validation_step(

        self, batch: Dict[str, Any], batch_idx: int

    ) -> Dict[str, torch.Tensor]:

        """

        Validation step for Stage 1



        Args:

            batch: Validation batch

            batch_idx: Batch index



        Returns:

            Dictionary of validation metrics

        """

        with torch.no_grad():

            # Extract data

            rgb_images = batch["rgb_image"]

            l_channel = batch["l_channel"]



            # Generate colorized images

            generated_images = self.generate_colorization(l_channel)



            # Compute metrics

            metrics = self.metrics_calculator.compute_all_metrics(

                generated_images=generated_images, reference_images=rgb_images

            )



            # Compute validation loss

            val_loss = self.training_step(batch, batch_idx)

            metrics["val_loss"] = val_loss



            # Log validation metrics

            val_metrics = {

                k: v.mean() if isinstance(v, torch.Tensor) else v

                for k, v in metrics.items()

            }

            self.log_metrics(val_metrics, prefix="val")



            self.val_losses.append(val_loss.item())



            return metrics



    def generate_colorization(

        self, l_channel: torch.Tensor, num_inference_steps: int = 20

    ) -> torch.Tensor:

        """

        Generate colorization from L channel



        Args:

            l_channel: L channel input [B, 1, H, W]

            num_inference_steps: Number of denoising steps



        Returns:

            Generated RGB images [B, 3, H, W]

        """

        batch_size = l_channel.shape[0]



        # Encode L channel as condition

        l_channel_3ch = l_channel.repeat(1, 3, 1, 1)

        condition_latents = self.encode_images(l_channel_3ch)



        # Start with random noise

        latents = torch.randn_like(condition_latents)



        # Denoising loop (simplified)

        for i in range(num_inference_steps):

            timestep = torch.full(

                (batch_size,),

                1000 - i * (1000 // num_inference_steps),

                device=self.device,

                dtype=torch.long,

            )



            # Concatenate condition

            model_input = torch.cat([latents, condition_latents], dim=1)



            # Predict noise

            noise_pred = self.model(model_input, timestep)



            # Remove noise (simplified scheduler)

            alpha = 1.0 - timestep.float() / 1000.0

            alpha = alpha.view(-1, 1, 1, 1)

            latents = (latents - (1 - alpha) * noise_pred) / alpha



        # Decode to image space

        generated_images = self.decode_latents(latents)



        # Ensure output is in [0, 1] range

        generated_images = torch.clamp(generated_images, 0, 1)



        return generated_images



    def on_validation_epoch_end(self):

        """Called at the end of validation epoch"""

        if len(self.val_losses) > 0:

            avg_val_loss = np.mean(self.val_losses[-10:])  # Average of last 10 batches



            # Save best model

            if avg_val_loss < self.best_val_loss:

                self.best_val_loss = avg_val_loss

                self._save_best_model()



            print(

                f"Validation loss: {avg_val_loss:.4f} (best: {self.best_val_loss:.4f})"

            )



    def _save_best_model(self):

        """Save the best model checkpoint"""

        checkpoint_dir = f"checkpoints/{self.experiment_name}"

        os.makedirs(checkpoint_dir, exist_ok=True)



        best_model_path = os.path.join(checkpoint_dir, "best_model.ckpt")

        torch.save(

            {

                "step": self.step_count,

                "model_state_dict": self.model.state_dict(),

                "best_val_loss": self.best_val_loss,

                "stage": "stage1_sd",

            },

            best_model_path,

        )



        print(f"Best model saved: {best_model_path}")





def create_stage1_trainer(

    model: nn.Module, vae_model: Optional[nn.Module] = None, **kwargs

) -> Stage1SDTrainer:

    """

    Create Stage 1 trainer with default configuration



    Args:

        model: Stable Diffusion UNet model

        vae_model: VAE model for encoding/decoding

        **kwargs: Additional trainer arguments



    Returns:

        Configured Stage 1 trainer

    """

    return Stage1SDTrainer(model=model, vae_model=vae_model, **kwargs)





def create_dummy_dataset(

    num_samples: int = 1000, image_size: int = 512

) -> ColorizationDataset:

    """Create dummy dataset for testing"""

    dummy_paths = [f"dummy_image_{i}.jpg" for i in range(num_samples)]

    return ColorizationDataset(dummy_paths, image_size=image_size)





# Test function

def test_stage1_trainer():

    """Test Stage 1 trainer"""

    print("Testing Stage 1 Trainer...")



    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")



    # Create dummy model

    model = nn.Conv2d(8, 4, 3, padding=1).to(

        device

    )  # 8 input channels (4 noisy + 4 condition)



    # Create trainer

    trainer = create_stage1_trainer(model)

    trainer = trainer.to(device)



    # Create dummy dataset

    dataset = create_dummy_dataset(num_samples=10, image_size=64)

    dataloader = DataLoader(dataset, batch_size=2, shuffle=True)



    # Test training step

    batch = next(iter(dataloader))

    batch = {

        k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()

    }



    loss = trainer.training_step(batch, 0)

    print(f"✅ Training step loss: {loss.item():.4f}")



    # Test validation step

    val_metrics = trainer.validation_step(batch, 0)

    print(f"✅ Validation metrics: {list(val_metrics.keys())}")



    # Test generation

    l_channel = batch["l_channel"]

    generated = trainer.generate_colorization(l_channel, num_inference_steps=5)

    print(f"✅ Generated images shape: {generated.shape}")



    return trainer





if __name__ == "__main__":

    test_stage1_trainer()

