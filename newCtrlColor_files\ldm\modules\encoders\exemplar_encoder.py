"""
CLIP-based Exemplar Image Encoder for CtrlColor

Processes exemplar images through CLIP image encoder to extract semantic features
for exemplar-based colorization conditioning.

Based on CtrlColor paper methodology for exemplar processing.
"""

from typing import Dict

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import CLIPImageProcessor, CLIPVisionModel


class CLIPExemplarEncoder(nn.Module):
    """
    CLIP-based exemplar image encoder for CtrlColor exemplar-based colorization.

    Processes exemplar images through CLIP vision encoder to extract semantic features
    that can be used for conditioning the diffusion model.
    """

    def __init__(
        self,
        clip_model_name: str = "openai/clip-vit-base-patch32",
        feature_dim: int = 512,
        output_dim: int = 768,  # Match text encoder dimension
        freeze_clip: bool = True,
        use_projection: bool = True,
    ):
        """
        Args:
            clip_model_name: CLIP model identifier
            feature_dim: CLIP feature dimension
            output_dim: Output feature dimension to match text encoder
            freeze_clip: Whether to freeze CLIP parameters
            use_projection: Whether to use projection layer
        """
        super().__init__()

        self.feature_dim = feature_dim
        self.output_dim = output_dim
        self.use_projection = use_projection

        # Load CLIP vision model and processor
        self.clip_vision = CLIPVisionModel.from_pretrained(clip_model_name)
        self.clip_processor = CLIPImageProcessor.from_pretrained(clip_model_name)

        # Freeze CLIP parameters if requested
        if freeze_clip:
            for param in self.clip_vision.parameters():
                param.requires_grad = False

        # Projection layer to match text encoder dimension
        if use_projection:
            self.projection = nn.Sequential(
                nn.Linear(feature_dim, output_dim),
                nn.LayerNorm(output_dim),
                nn.GELU(),
                nn.Linear(output_dim, output_dim),
            )
        else:
            self.projection = nn.Identity()

        # Color palette extraction (optional enhancement)
        self.extract_color_palette = True
        self.palette_size = 16  # Number of dominant colors to extract

    def preprocess_image(self, image: torch.Tensor) -> torch.Tensor:
        """
        Preprocess image for CLIP encoder.

        Args:
            image: Input image tensor [B, 3, H, W] in range [0, 1] or [-1, 1]

        Returns:
            Preprocessed image tensor
        """
        # Convert to [0, 1] range if needed
        if image.min() < 0:
            image = (image + 1.0) / 2.0

        # CLIP processor expects PIL images or numpy arrays
        # For batch processing, we'll handle tensor directly
        # Normalize to CLIP's expected range and format
        mean = (
            torch.tensor([0.48145466, 0.4578275, 0.40821073])
            .view(1, 3, 1, 1)
            .to(image.device)
        )
        std = (
            torch.tensor([0.26862954, 0.26130258, 0.27577711])
            .view(1, 3, 1, 1)
            .to(image.device)
        )

        # Resize to 224x224 (CLIP input size)
        image_resized = F.interpolate(
            image, size=(224, 224), mode="bilinear", align_corners=False
        )

        # Normalize
        image_normalized = (image_resized - mean) / std

        return image_normalized

    def extract_dominant_colors(self, image: torch.Tensor, k: int = 16) -> torch.Tensor:
        """
        Extract dominant colors from exemplar image using k-means clustering.

        Args:
            image: Input image [B, 3, H, W] in range [0, 1]
            k: Number of dominant colors to extract

        Returns:
            Dominant colors [B, k, 3]
        """
        B, C, H, W = image.shape

        # Reshape image to [B, H*W, 3]
        pixels = image.permute(0, 2, 3, 1).reshape(B, H * W, 3)

        # Simple color quantization (placeholder for k-means)
        # In practice, you might want to use sklearn.cluster.KMeans
        dominant_colors = []

        for b in range(B):
            # Sample random colors as placeholder
            # TODO: Implement proper k-means clustering
            sampled_indices = torch.randperm(H * W)[:k]
            colors = pixels[b, sampled_indices]  # [k, 3]
            dominant_colors.append(colors)

        return torch.stack(dominant_colors, dim=0)  # [B, k, 3]

    def encode_exemplar(self, exemplar_image: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Encode exemplar image through CLIP vision encoder.

        Args:
            exemplar_image: Exemplar image [B, 3, H, W] in range [0, 1] or [-1, 1]

        Returns:
            Dictionary containing:
            - 'features': CLIP features [B, output_dim]
            - 'pooled_features': Pooled features [B, output_dim]
            - 'color_palette': Dominant colors [B, palette_size, 3] (optional)
        """
        # Preprocess image
        processed_image = self.preprocess_image(exemplar_image)

        # Extract CLIP features
        with torch.no_grad() if not self.training else torch.enable_grad():
            clip_outputs = self.clip_vision(pixel_values=processed_image)

        # Get pooled features (CLS token)
        pooled_features = clip_outputs.pooler_output  # [B, feature_dim]

        # Get sequence features (all patches)
        sequence_features = clip_outputs.last_hidden_state  # [B, seq_len, feature_dim]

        # Apply projection if enabled
        projected_features = self.projection(pooled_features)  # [B, output_dim]

        # Extract color palette if enabled
        color_palette = None
        if self.extract_color_palette:
            color_palette = self.extract_dominant_colors(
                exemplar_image, self.palette_size
            )

        return {
            "features": projected_features,
            "pooled_features": pooled_features,
            "sequence_features": sequence_features,
            "color_palette": color_palette,
        }

    def forward(self, exemplar_image: torch.Tensor) -> torch.Tensor:
        """
        Forward pass for exemplar encoding.

        Args:
            exemplar_image: Exemplar image [B, 3, H, W]

        Returns:
            Encoded exemplar features [B, output_dim]
        """
        encoding_result = self.encode_exemplar(exemplar_image)
        return encoding_result["features"]


class ExemplarTextFusion(nn.Module):
    """
    Fusion module for combining exemplar features with text features.

    Implements multi-modal conditioning by fusing CLIP image features
    with CLIP text features for enhanced exemplar-based colorization.
    """

    def __init__(
        self,
        feature_dim: int = 768,
        fusion_method: str = "concat",  # 'concat', 'add', 'cross_attention'
        hidden_dim: int = 1024,
    ):
        super().__init__()

        self.feature_dim = feature_dim
        self.fusion_method = fusion_method

        if fusion_method == "concat":
            self.fusion_layer = nn.Sequential(
                nn.Linear(feature_dim * 2, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.GELU(),
                nn.Linear(hidden_dim, feature_dim),
            )
        elif fusion_method == "cross_attention":
            self.cross_attention = nn.MultiheadAttention(
                embed_dim=feature_dim, num_heads=8, batch_first=True
            )
            self.norm = nn.LayerNorm(feature_dim)
        else:  # 'add'
            self.fusion_layer = nn.Identity()

    def forward(
        self, text_features: torch.Tensor, exemplar_features: torch.Tensor
    ) -> torch.Tensor:
        """
        Fuse text and exemplar features.

        Args:
            text_features: Text features [B, seq_len, feature_dim]
            exemplar_features: Exemplar features [B, feature_dim]

        Returns:
            Fused features [B, seq_len, feature_dim]
        """
        B, seq_len, _ = text_features.shape

        # Expand exemplar features to match text sequence length
        exemplar_expanded = exemplar_features.unsqueeze(1).expand(B, seq_len, -1)

        if self.fusion_method == "concat":
            # Concatenate and project
            fused = torch.cat([text_features, exemplar_expanded], dim=-1)
            fused = self.fusion_layer(fused)
        elif self.fusion_method == "cross_attention":
            # Cross attention between text and exemplar
            attended, _ = self.cross_attention(
                query=text_features, key=exemplar_expanded, value=exemplar_expanded
            )
            fused = self.norm(text_features + attended)
        else:  # 'add'
            # Simple addition
            fused = text_features + exemplar_expanded

        return fused


def test_clip_encoder():
    """Test the CLIP exemplar encoder implementation"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Testing CLIP exemplar encoder on device: {device}")

    # Create test tensors
    batch_size = 2
    exemplar_image = torch.rand(batch_size, 3, 256, 256).to(device)

    # Test exemplar encoder
    print("Testing exemplar encoder...")
    encoder = CLIPExemplarEncoder().to(device)

    with torch.no_grad():
        encoding_result = encoder.encode_exemplar(exemplar_image)

    print(f"Features shape: {encoding_result['features'].shape}")
    print(f"Pooled features shape: {encoding_result['pooled_features'].shape}")
    print(f"Sequence features shape: {encoding_result['sequence_features'].shape}")
    if encoding_result["color_palette"] is not None:
        print(f"Color palette shape: {encoding_result['color_palette'].shape}")

    # Test fusion module
    print("Testing exemplar-text fusion...")
    text_features = torch.rand(batch_size, 77, 768).to(
        device
    )  # Standard CLIP text length
    exemplar_features = encoding_result["features"]

    fusion = ExemplarTextFusion().to(device)
    fused_features = fusion(text_features, exemplar_features)

    print(f"Fused features shape: {fused_features.shape}")

    # Test gradient computation
    exemplar_image.requires_grad_(True)
    features = encoder(exemplar_image)
    loss = features.mean()
    loss.backward()

    print(f"Exemplar grad norm: {exemplar_image.grad.norm().item():.6f}")
    print("✅ CLIP exemplar encoder test passed!")

    return encoding_result


if __name__ == "__main__":
    test_clip_encoder()
