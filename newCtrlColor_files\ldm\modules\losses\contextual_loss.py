"""
VGG19-based Contextual Loss for CtrlColor Exemplar-based Colorization

Implements equations 101-106 from the CtrlColor paper:

Cosine distance computation (Eq. 102):
$$d^l(i,j) = \cos(\phi^l_{I_e}(i), \phi^l_{I_g}(j))$$

Relative distance normalization (Eq. 103):
$$\tilde{d}^l(i,j) = \frac{d^l(i,j)}{\min_k d^l(i,k)}$$

Contextual attention weights (Eq. 104):
$$A^l(i,j) = \text{softmax}_j\left(\frac{1-\tilde{d}^l(i,j)}{h}\right)$$

Final contextual loss (Eq. 105-106):
$$\mathcal{L}_{\text{context}} = \sum_{l \in \{3,5\}} w_l \left[-\log\left(\frac{1}{N_l} \sum_i \max_j A^l(i,j)\right)\right]$$

Reference: CtrlColor paper Section 3.2.2
"""

from typing import Dict, List

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models


class VGG19ContextualLoss(nn.Module):
    """
    VGG19-based contextual loss for exemplar-based colorization.

    Implements the contextual loss from CtrlColor paper equations 101-106.
    Uses VGG19 conv3_1 and conv5_1 features with weights [8, 4, 2] for layers [5, 4, 3].
    """

    def __init__(
        self,
        layers: List[int] = [3, 5],  # conv3_1, conv5_1 as in paper
        layer_weights: Dict[int, float] = {3: 2.0, 4: 4.0, 5: 8.0},  # w_l from paper
        h: float = 0.01,
    ):  # bandwidth parameter from paper
        super().__init__()

        self.layers = layers
        self.layer_weights = layer_weights
        self.h = h

        # Load pre-trained VGG19
        try:
            vgg19 = models.vgg19(weights=models.VGG19_Weights.IMAGENET1K_V1)
        except AttributeError:
            # Fallback for older torchvision versions
            vgg19 = models.vgg19(pretrained=True)

        self.features = vgg19.features

        # Freeze VGG19 parameters
        for param in self.features.parameters():
            param.requires_grad = False

        # VGG19 layer indices for conv layers
        self.layer_indices = {
            1: 2,  # conv1_1 (after ReLU)
            2: 7,  # conv2_1 (after ReLU)
            3: 12,  # conv3_1 (after ReLU)
            4: 21,  # conv4_1 (after ReLU)
            5: 30,  # conv5_1 (after ReLU)
        }

        # ImageNet normalization for VGG19
        self.register_buffer(
            "mean", torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
        )
        self.register_buffer(
            "std", torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)
        )

    def normalize_for_vgg(self, x: torch.Tensor) -> torch.Tensor:
        """Normalize input tensor for VGG19 (ImageNet normalization)"""
        return (x - self.mean) / self.std

    def extract_features(self, x: torch.Tensor) -> Dict[int, torch.Tensor]:
        """Extract VGG19 features from specified layers"""
        features = {}

        for i, layer in enumerate(self.features):
            x = layer(x)
            # Check if this layer index corresponds to one of our target layers
            for layer_num, layer_idx in self.layer_indices.items():
                if i == layer_idx and layer_num in self.layers:
                    features[layer_num] = x

        return features

    def compute_cosine_similarity(
        self, feat1: torch.Tensor, feat2: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute cosine similarity between feature maps.
        Implements d^l(i,j) = cos(φ^l_I_e(i), φ^l_I_g(j)) from equation 102.
        """
        B, C, H, W = feat1.shape

        # Reshape to [B, C, H*W] and then [B, H*W, C]
        feat1_flat = feat1.view(B, C, H * W).permute(0, 2, 1)  # [B, HW, C]
        feat2_flat = feat2.view(B, C, H * W).permute(0, 2, 1)  # [B, HW, C]

        # L2 normalize features
        feat1_norm = F.normalize(feat1_flat, dim=2)  # [B, HW, C]
        feat2_norm = F.normalize(feat2_flat, dim=2)  # [B, HW, C]

        # Compute cosine similarity matrix
        cosine_sim = torch.bmm(feat1_norm, feat2_norm.transpose(1, 2))  # [B, HW, HW]

        return cosine_sim

    def compute_contextual_attention(self, cosine_sim: torch.Tensor) -> torch.Tensor:
        """
        Compute contextual attention weights.
        Implements A^l(i,j) = softmax_j(1-d̃^l(i,j)/h) from equation 103.
        """
        # Convert cosine similarity to distance: d^l(i,j) = 1 - cos_sim
        cosine_dist = 1.0 - cosine_sim  # [B, HW, HW]

        # Compute relative distance d̃^l(i,j) = d^l(i,j) / min_k(d^l(i,k))
        min_dist, _ = torch.min(cosine_dist, dim=2, keepdim=True)  # [B, HW, 1]
        relative_dist = cosine_dist / (min_dist + 1e-8)  # [B, HW, HW]

        # Compute attention weights: A^l(i,j) = softmax_j((1-d̃^l(i,j))/h)
        attention_logits = (1.0 - relative_dist) / self.h  # [B, HW, HW]
        attention_weights = F.softmax(attention_logits, dim=2)  # [B, HW, HW]

        return attention_weights

    def compute_layer_loss(
        self, feat_exemplar: torch.Tensor, feat_generated: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute contextual loss for a single layer.
        Implements L_context^l = -log(1/N_l Σ_i max_j(A^l(i,j))) from equation 104.
        """
        # Compute cosine similarity
        cosine_sim = self.compute_cosine_similarity(feat_exemplar, feat_generated)

        # Compute contextual attention
        attention = self.compute_contextual_attention(cosine_sim)  # [B, HW, HW]

        # Compute contextual loss: -log(1/N * Σ_i max_j(A^l(i,j)))
        max_attention, _ = torch.max(attention, dim=2)  # [B, HW]
        mean_max_attention = torch.mean(max_attention, dim=1)  # [B]
        layer_loss = -torch.log(mean_max_attention + 1e-8)  # [B]

        return torch.mean(layer_loss)  # Scalar

    def forward(self, exemplar: torch.Tensor, generated: torch.Tensor) -> torch.Tensor:
        """
        Compute VGG19-based contextual loss.

        Args:
            exemplar: Exemplar image [B, 3, H, W] in range [0, 1]
            generated: Generated image [B, 3, H, W] in range [0, 1]

        Returns:
            Contextual loss scalar
        """
        # Normalize inputs for VGG19
        exemplar_norm = self.normalize_for_vgg(exemplar)
        generated_norm = self.normalize_for_vgg(generated)

        # Extract features
        exemplar_features = self.extract_features(exemplar_norm)
        generated_features = self.extract_features(generated_norm)

        # Compute weighted loss across layers
        total_loss = 0.0
        total_weight = 0.0

        for layer in self.layers:
            if layer in exemplar_features and layer in generated_features:
                layer_loss = self.compute_layer_loss(
                    exemplar_features[layer], generated_features[layer]
                )
                weight = self.layer_weights.get(layer, 1.0)
                total_loss += weight * layer_loss
                total_weight += weight

        # Normalize by total weight
        if total_weight > 0:
            total_loss = total_loss / total_weight

        return total_loss


def test_contextual_loss():
    """Test the VGG19 contextual loss implementation"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Testing on device: {device}")

    # Create test tensors
    batch_size = 2
    exemplar = torch.rand(batch_size, 3, 256, 256).to(device)
    generated = torch.rand(batch_size, 3, 256, 256).to(device)

    # Initialize loss
    contextual_loss = VGG19ContextualLoss().to(device)

    # Compute loss
    with torch.no_grad():
        loss = contextual_loss(exemplar, generated)

    print(f"Contextual loss: {loss.item():.6f}")
    print(f"Loss requires_grad: {loss.requires_grad}")

    # Test gradient computation
    exemplar.requires_grad_(True)
    generated.requires_grad_(True)

    loss = contextual_loss(exemplar, generated)
    loss.backward()

    print(f"Exemplar grad norm: {exemplar.grad.norm().item():.6f}")
    print(f"Generated grad norm: {generated.grad.norm().item():.6f}")
    print("✅ Contextual loss test passed!")

    return loss


if __name__ == "__main__":
    test_contextual_loss()
