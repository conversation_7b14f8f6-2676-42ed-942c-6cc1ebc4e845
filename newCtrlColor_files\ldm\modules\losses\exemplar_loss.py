"""
Combined Exemplar Loss for CtrlColor Exemplar-based Colorization

Implements equations 124-126 from the CtrlColor paper:

Combined exemplar loss (Eq. 125):
$$\mathcal{L}_{\text{exemplar}} = \mathcal{L}_{\text{context}} + w_e \cdot \mathcal{L}_{\text{gray}}$$

Where:
- $\mathcal{L}_{\text{context}}$: VGG19-based contextual loss (equations 101-106)
- $\mathcal{L}_{\text{gray}}$: Grayscale consistency loss (equations 111-113)
- $w_e = 1000$: Weight for grayscale loss (empirically determined in paper)

This loss ensures both color similarity to exemplar and content preservation.

Reference: CtrlColor paper Section 3.2.2
"""

from typing import Dict, Optional

import torch
import torch.nn as nn

from .contextual_loss import VGG19ContextualLoss
from .grayscale_loss import GrayscaleConsistencyLoss, PerceptualGrayscaleLoss


class ExemplarLoss(nn.Module):
    """
    Combined exemplar loss for CtrlColor exemplar-based colorization.

    Implements L_exemplar = L_context + w_e * L_gray from equation 125.
    This loss ensures both color similarity to exemplar and content preservation.
    """

    def __init__(
        self,
        w_e: float = 1000.0,  # Grayscale loss weight from paper
        contextual_layers: list = [3, 5],  # VGG conv3_1, conv5_1
        contextual_weights: Dict[int, float] = {3: 2.0, 4: 4.0, 5: 8.0},
        contextual_bandwidth: float = 0.01,
        use_perceptual_gray: bool = False,
        device: Optional[torch.device] = None,
    ):
        """
        Args:
            w_e: Weight for grayscale loss (1000 in paper)
            contextual_layers: VGG layers for contextual loss
            contextual_weights: Layer weights for contextual loss
            contextual_bandwidth: Bandwidth parameter h for contextual loss
            use_perceptual_gray: Whether to use perceptual grayscale loss
            device: Device to place the loss modules
        """
        super().__init__()

        self.w_e = w_e
        self.use_perceptual_gray = use_perceptual_gray

        # Initialize contextual loss (VGG19-based)
        self.contextual_loss = VGG19ContextualLoss(
            layers=contextual_layers,
            layer_weights=contextual_weights,
            h=contextual_bandwidth,
        )

        # Initialize grayscale consistency loss
        if use_perceptual_gray:
            self.grayscale_loss = PerceptualGrayscaleLoss()
        else:
            self.grayscale_loss = GrayscaleConsistencyLoss()

        if device is not None:
            self.to(device)

    def forward(
        self,
        generated: torch.Tensor,
        exemplar: torch.Tensor,
        input_image: torch.Tensor,
        return_components: bool = False,
    ) -> torch.Tensor:
        """
        Compute combined exemplar loss.

        Args:
            generated: Generated colorized image [B, 3, H, W] in range [0, 1]
            exemplar: Exemplar reference image [B, 3, H, W] in range [0, 1]
            input_image: Original input image [B, 3, H, W] in range [0, 1]
            return_components: If True, return dict with loss components

        Returns:
            Combined exemplar loss or dict with components
        """
        # Compute contextual loss: L_context
        contextual_loss_val = self.contextual_loss(exemplar, generated)

        # Compute grayscale consistency loss: L_gray
        grayscale_loss_val = self.grayscale_loss(input_image, generated)

        # Combine losses: L_exemplar = L_context + w_e * L_gray
        total_loss = contextual_loss_val + self.w_e * grayscale_loss_val

        if return_components:
            return {
                "total_loss": total_loss,
                "contextual_loss": contextual_loss_val,
                "grayscale_loss": grayscale_loss_val,
                "weighted_grayscale_loss": self.w_e * grayscale_loss_val,
            }
        else:
            return total_loss

    def get_loss_weights(self) -> Dict[str, float]:
        """Get current loss weights for logging/debugging"""
        return {"contextual_weight": 1.0, "grayscale_weight": self.w_e}

    def set_grayscale_weight(self, w_e: float):
        """Update grayscale loss weight"""
        self.w_e = w_e


class AdaptiveExemplarLoss(ExemplarLoss):
    """
    Adaptive exemplar loss with dynamic weight adjustment.

    This variant can adjust the grayscale weight based on training progress
    or loss component magnitudes for better training stability.
    """

    def __init__(
        self,
        w_e_initial: float = 1000.0,
        w_e_final: float = 1000.0,
        adaptation_steps: int = 10000,
        adaptation_mode: str = "linear",  # 'linear', 'cosine', 'adaptive'
        **kwargs,
    ):
        super().__init__(w_e=w_e_initial, **kwargs)

        self.w_e_initial = w_e_initial
        self.w_e_final = w_e_final
        self.adaptation_steps = adaptation_steps
        self.adaptation_mode = adaptation_mode
        self.current_step = 0

    def update_weights(self, step: int):
        """Update loss weights based on training step"""
        self.current_step = step

        if step >= self.adaptation_steps:
            self.w_e = self.w_e_final
        else:
            progress = step / self.adaptation_steps

            if self.adaptation_mode == "linear":
                self.w_e = self.w_e_initial + progress * (
                    self.w_e_final - self.w_e_initial
                )
            elif self.adaptation_mode == "cosine":
                import math

                self.w_e = self.w_e_initial + 0.5 * (
                    self.w_e_final - self.w_e_initial
                ) * (1 + math.cos(math.pi * progress))
            else:  # adaptive mode would require loss history
                self.w_e = self.w_e_initial

    def forward(
        self, generated, exemplar, input_image, step: Optional[int] = None, **kwargs
    ):
        """Forward pass with optional step-based weight update"""
        if step is not None:
            self.update_weights(step)

        return super().forward(generated, exemplar, input_image, **kwargs)


def test_exemplar_loss():
    """Test the combined exemplar loss implementation"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Testing on device: {device}")

    # Create test tensors
    batch_size = 2
    generated = torch.rand(batch_size, 3, 256, 256).to(device)
    exemplar = torch.rand(batch_size, 3, 256, 256).to(device)
    input_image = torch.rand(batch_size, 3, 256, 256).to(device)

    # Test basic exemplar loss
    print("Testing basic exemplar loss...")
    exemplar_loss = ExemplarLoss().to(device)

    with torch.no_grad():
        loss_components = exemplar_loss(
            generated, exemplar, input_image, return_components=True
        )

    print(f"Total exemplar loss: {loss_components['total_loss'].item():.6f}")
    print(f"Contextual loss: {loss_components['contextual_loss'].item():.6f}")
    print(f"Grayscale loss: {loss_components['grayscale_loss'].item():.6f}")
    print(
        f"Weighted grayscale loss: {loss_components['weighted_grayscale_loss'].item():.6f}"
    )

    # Test adaptive exemplar loss
    print("\nTesting adaptive exemplar loss...")
    adaptive_loss = AdaptiveExemplarLoss(
        w_e_initial=500.0, w_e_final=1500.0, adaptation_steps=1000
    ).to(device)

    # Test weight adaptation
    for step in [0, 250, 500, 750, 1000, 1500]:
        adaptive_loss.update_weights(step)
        print(f"Step {step}: w_e = {adaptive_loss.w_e:.1f}")

    # Test gradient computation
    generated.requires_grad_(True)
    exemplar.requires_grad_(True)
    input_image.requires_grad_(True)

    loss = exemplar_loss(generated, exemplar, input_image)
    loss.backward()

    print("\nGradient norms:")
    print(f"Generated grad norm: {generated.grad.norm().item():.6f}")
    print(f"Exemplar grad norm: {exemplar.grad.norm().item():.6f}")
    print(f"Input grad norm: {input_image.grad.norm().item():.6f}")
    print("✅ Exemplar loss test passed!")

    return loss_components


if __name__ == "__main__":
    test_exemplar_loss()
