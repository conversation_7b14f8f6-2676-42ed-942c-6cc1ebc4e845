"""
Grayscale Consistency Loss for CtrlColor Exemplar-based Colorization

Implements equations 111-113 from the CtrlColor paper:

Grayscale consistency loss (Eq. 112):
$$\mathcal{L}_{\text{gray}} = \left\|\frac{\sum_{c \in \{R,G,B\}} I_i^c}{3} - \frac{\sum_{c \in \{R,G,B\}} I_g^c}{3}\right\|_2$$

Where:
- $I_i^c$ is the input image channel $c$ regenerated by the autoencoder
- $I_g^c$ is the generated image channel $c$ in each diffusion timestep $t$
- The loss ensures luminance consistency between input and generated images

Reference: CtrlColor paper Section 3.2.2
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class GrayscaleConsistencyLoss(nn.Module):
    """
    Grayscale consistency loss to constrain content similarity between input and generated images.

    Implements L_gray = ||Σ_{R,G,B}I_i/3 - Σ_{R,G,B}I_g/3||_2 from equation 112.
    This ensures the generated colorized image maintains the same luminance/content as the input.
    """

    def __init__(self, reduction: str = "mean", rgb_weights: torch.Tensor = None):
        """
        Args:
            reduction: Reduction method ('mean', 'sum', 'none')
            rgb_weights: Custom weights for RGB channels. If None, uses equal weights [1/3, 1/3, 1/3]
        """
        super().__init__()
        self.reduction = reduction

        # RGB to grayscale conversion weights
        if rgb_weights is None:
            # Equal weights as specified in paper: (R+G+B)/3
            rgb_weights = torch.tensor([1 / 3, 1 / 3, 1 / 3])

        self.register_buffer("rgb_weights", rgb_weights.view(1, 3, 1, 1))

    def rgb_to_grayscale(self, rgb_image: torch.Tensor) -> torch.Tensor:
        """
        Convert RGB image to grayscale using weighted sum.

        Args:
            rgb_image: RGB image tensor [B, 3, H, W]

        Returns:
            Grayscale image tensor [B, 1, H, W]
        """
        # Apply RGB weights: (R+G+B)/3 as in equation 112
        grayscale = torch.sum(rgb_image * self.rgb_weights, dim=1, keepdim=True)
        return grayscale

    def forward(
        self, input_image: torch.Tensor, generated_image: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute grayscale consistency loss.

        Args:
            input_image: Input image I_i [B, 3, H, W] in range [0, 1] or [-1, 1]
            generated_image: Generated image I_g [B, 3, H, W] in range [0, 1] or [-1, 1]

        Returns:
            Grayscale consistency loss
        """
        # Convert both images to grayscale
        input_gray = self.rgb_to_grayscale(input_image)  # [B, 1, H, W]
        generated_gray = self.rgb_to_grayscale(generated_image)  # [B, 1, H, W]

        # Compute L2 loss between grayscale versions
        loss = F.mse_loss(input_gray, generated_gray, reduction="none")  # [B, 1, H, W]

        # Apply reduction
        if self.reduction == "mean":
            return torch.mean(loss)
        elif self.reduction == "sum":
            return torch.sum(loss)
        elif self.reduction == "none":
            return loss
        else:
            raise ValueError(f"Invalid reduction: {self.reduction}")


class PerceptualGrayscaleLoss(nn.Module):
    """
    Enhanced grayscale loss using perceptual features for better content preservation.

    This variant uses VGG features to compute grayscale consistency at multiple scales,
    providing better content preservation than simple pixel-wise comparison.
    """

    def __init__(
        self,
        use_vgg: bool = True,
        vgg_layers: list = [2, 7, 12],  # conv1_2, conv2_2, conv3_2
        layer_weights: list = [1.0, 1.0, 1.0],
        pixel_weight: float = 1.0,
    ):
        super().__init__()

        self.use_vgg = use_vgg
        self.pixel_weight = pixel_weight

        # Basic grayscale loss
        self.grayscale_loss = GrayscaleConsistencyLoss()

        if use_vgg:
            # Load VGG for perceptual features
            import torchvision.models as models

            try:
                vgg = models.vgg19(weights=models.VGG19_Weights.IMAGENET1K_V1)
            except AttributeError:
                vgg = models.vgg19(pretrained=True)

            self.vgg_features = vgg.features
            for param in self.vgg_features.parameters():
                param.requires_grad = False

            self.vgg_layers = vgg_layers
            self.layer_weights = layer_weights

            # VGG normalization
            self.register_buffer(
                "vgg_mean", torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
            )
            self.register_buffer(
                "vgg_std", torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)
            )

    def normalize_for_vgg(self, x: torch.Tensor) -> torch.Tensor:
        """Normalize tensor for VGG input"""
        return (x - self.vgg_mean) / self.vgg_std

    def extract_vgg_features(self, x: torch.Tensor) -> list:
        """Extract VGG features from specified layers"""
        features = []
        x_norm = self.normalize_for_vgg(x)

        for i, layer in enumerate(self.vgg_features):
            x_norm = layer(x_norm)
            if i in self.vgg_layers:
                features.append(x_norm)

        return features

    def forward(
        self, input_image: torch.Tensor, generated_image: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute enhanced grayscale consistency loss.

        Args:
            input_image: Input image [B, 3, H, W]
            generated_image: Generated image [B, 3, H, W]

        Returns:
            Enhanced grayscale consistency loss
        """
        # Pixel-level grayscale loss
        pixel_loss = self.grayscale_loss(input_image, generated_image)
        total_loss = self.pixel_weight * pixel_loss

        if self.use_vgg:
            # Convert to grayscale for VGG processing
            input_gray = self.grayscale_loss.rgb_to_grayscale(input_image)
            generated_gray = self.grayscale_loss.rgb_to_grayscale(generated_image)

            # Expand to 3 channels for VGG
            input_gray_3ch = input_gray.repeat(1, 3, 1, 1)
            generated_gray_3ch = generated_gray.repeat(1, 3, 1, 1)

            # Extract VGG features
            input_features = self.extract_vgg_features(input_gray_3ch)
            generated_features = self.extract_vgg_features(generated_gray_3ch)

            # Compute perceptual loss
            perceptual_loss = 0.0
            for i, (feat_in, feat_gen) in enumerate(
                zip(input_features, generated_features)
            ):
                weight = self.layer_weights[i] if i < len(self.layer_weights) else 1.0
                perceptual_loss += weight * F.mse_loss(feat_in, feat_gen)

            total_loss += perceptual_loss

        return total_loss


def test_grayscale_loss():
    """Test the grayscale consistency loss implementation"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Testing on device: {device}")

    # Create test tensors
    batch_size = 2
    input_image = torch.rand(batch_size, 3, 256, 256).to(device)
    generated_image = torch.rand(batch_size, 3, 256, 256).to(device)

    # Test basic grayscale loss
    print("Testing basic grayscale loss...")
    grayscale_loss = GrayscaleConsistencyLoss().to(device)

    with torch.no_grad():
        loss = grayscale_loss(input_image, generated_image)

    print(f"Basic grayscale loss: {loss.item():.6f}")

    # Test perceptual grayscale loss
    print("Testing perceptual grayscale loss...")
    perceptual_loss = PerceptualGrayscaleLoss().to(device)

    with torch.no_grad():
        loss = perceptual_loss(input_image, generated_image)

    print(f"Perceptual grayscale loss: {loss.item():.6f}")

    # Test gradient computation
    input_image.requires_grad_(True)
    generated_image.requires_grad_(True)

    loss = grayscale_loss(input_image, generated_image)
    loss.backward()

    print(f"Input grad norm: {input_image.grad.norm().item():.6f}")
    print(f"Generated grad norm: {generated_image.grad.norm().item():.6f}")
    print("✅ Grayscale loss test passed!")

    return loss


if __name__ == "__main__":
    test_grayscale_loss()
