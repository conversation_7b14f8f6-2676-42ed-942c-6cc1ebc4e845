name: mini

channels:

  - pytorch

  - conda-forge

  - defaults

  - https://repo.anaconda.com/pkgs/main

  - https://repo.anaconda.com/pkgs/r

  - https://repo.anaconda.com/pkgs/msys2

dependencies:

  - anyio=4.5.0

  - aom=3.6.1

  - argon2-cffi=23.1.0

  - argon2-cffi-bindings=21.2.0

  - arrow=1.3.0

  - asttokens=3.0.0

  - async-lru=2.0.4

  - atomicwrites=1.4.1

  - attrs=24.2.0

  - babel=2.16.0

  - backcall=0.2.0

  - beautifulsoup4=4.12.3

  - blas=2.131

  - blas-devel=3.9.0

  - bleach=6.1.0

  - blosc=1.21.5

  - brotli=1.0.9

  - brotli-bin=1.0.9

  - brotli-python=1.0.9

  - bzip2=1.0.8

  - c-blosc2=2.12.0

  - ca-certificates=2025.4.26

  - cached-property=1.5.2

  - cairo=1.16.0

  - certifi=2024.8.30

  - cffi=1.17.0

  - cfitsio=4.2.0

  - charls=2.4.2

  - cloudpickle=3.1.0

  - colorama=0.4.6

  - comm=0.2.2

  - cudatoolkit=11.3.1

  - cycler=0.12.1

  - cytoolz=0.12.3

  - dask-core=2023.5.0

  - dav1d=1.2.1

  - debugpy=1.8.5

  - decorator=5.1.1

  - defusedxml=0.7.1

  - entrypoints=0.4

  - exceptiongroup=1.2.2

  - executing=2.1.0

  - ffmpeg=5.1.2

  - font-ttf-dejavu-sans-mono=2.37

  - font-ttf-inconsolata=3.000

  - font-ttf-source-code-pro=2.038

  - font-ttf-ubuntu=0.83

  - fontconfig=2.15.0

  - fonts-conda-ecosystem=1

  - fonts-conda-forge=1

  - fonttools=4.53.1

  - fqdn=1.5.1

  - freeglut=3.2.2

  - freetype=2.13.3

  - fsspec=2024.10.0

  - gettext=0.22.5

  - gettext-tools=0.22.5

  - giflib=5.2.2

  - glib=2.84.2

  - glib-tools=2.84.2

  - graphite2=1.3.13

  - gst-plugins-base=1.21.3

  - gstreamer=1.21.3

  - h11=0.14.0

  - h2=4.1.0

  - harfbuzz=6.0.0

  - hpack=4.0.0

  - httpcore=1.0.7

  - httpx=0.27.2

  - hyperframe=6.0.1

  - icu=70.1

  - idna=3.10

  - imagecodecs=2023.1.23

  - imageio=2.21.1

  - importlib-metadata=8.5.0

  - importlib_resources=6.4.5

  - iniconfig=2.0.0

  - intel-openmp=2024.2.1

  - ipykernel=6.29.5

  - ipython=8.12.2

  - ipywidgets=8.1.5

  - isoduration=20.11.0

  - jasper=2.0.33

  - jedi=0.19.1

  - jinja2=3.1.4

  - jpeg=9e

  - json5=0.9.25

  - jsonpointer=3.0.0

  - jsonschema=4.23.0

  - jsonschema-specifications=2024.10.1

  - jsonschema-with-format-nongpl=4.23.0

  - jupyter=1.0.0

  - jupyter-lsp=2.2.5

  - jupyter_client=8.6.3

  - jupyter_console=6.6.3

  - jupyter_core=5.7.2

  - jupyter_events=0.10.0

  - jupyter_server=2.14.2

  - jupyter_server_terminals=0.5.3

  - jupyterlab=4.2.5

  - jupyterlab_pygments=0.3.0

  - jupyterlab_server=2.27.3

  - jupyterlab_widgets=3.0.13

  - jxrlib=1.1

  - kiwisolver=1.4.5

  - krb5=1.20.1

  - lcms2=2.15

  - lerc=4.0.0

  - libaec=1.1.3

  - libasprintf=0.22.5

  - libasprintf-devel=0.22.5

  - libavif=0.11.1

  - libblas=3.9.0

  - libbrotlicommon=1.0.9

  - libbrotlidec=1.0.9

  - libbrotlienc=1.0.9

  - libcblas=3.9.0

  - libclang=15.0.7

  - libclang13=15.0.7

  - libcurl=8.1.2

  - libdeflate=1.17

  - libexpat=2.7.0

  - libffi=3.4.6

  - libfreetype=2.13.3

  - libgettextpo=0.22.5

  - libgettextpo-devel=0.22.5

  - libglib=2.84.2

  - libhwloc=2.11.2

  - libiconv=1.18

  - libintl=0.22.5

  - libintl-devel=0.22.5

  - liblapack=3.9.0

  - liblapacke=3.9.0

  - liblzma=5.8.1

  - libogg=1.3.5

  - libopencv=4.6.0

  - libopus=1.5.2

  - libpng=1.6.47

  - libprotobuf=3.21.12

  - libsodium=1.0.18

  - libsqlite=3.49.2

  - libssh2=1.10.0

  - libtiff=4.5.0

  - libuv=1.51.0

  - libvorbis=1.3.7

  - libwebp-base=1.5.0

  - libxcb=1.13

  - libxml2=2.13.8

  - libzlib=1.3.1

  - libzopfli=1.0.3

  - locket=1.0.0

  - lz4-c=1.9.4

  - m2w64-gcc-libgfortran=5.3.0

  - m2w64-gcc-libs=5.3.0

  - m2w64-gcc-libs-core=5.3.0

  - m2w64-gmp=6.1.0

  - m2w64-libwinpthread-git=5.0.0.4634.697f757

  - markupsafe=2.1.5

  - matplotlib=3.5.3

  - matplotlib-base=3.5.3

  - matplotlib-inline=0.1.7

  - mistune=3.0.2

  - mkl=2024.2.2

  - mkl-devel=2024.2.2

  - mkl-include=2024.2.2

  - msys2-conda-epoch=20160418

  - munkres=1.1.4

  - nbclient=0.10.2

  - nbconvert=7.16.4

  - nbconvert-core=7.16.4

  - nbconvert-pandoc=7.16.4

  - nbformat=5.10.4

  - nest-asyncio=1.6.0

  - networkx=3.1

  - notebook=7.2.2

  - notebook-shim=0.2.4

  - numpy=1.23.1

  - opencv=4.6.0

  - openh264=2.3.1

  - openjpeg=2.5.0

  - openssl=1.1.1w

  - overrides=7.7.0

  - pandoc=*******

  - pandocfilters=1.5.0

  - parso=0.8.4

  - partd=1.4.1

  - pcre2=10.45

  - pickleshare=0.7.5

  - pillow=9.2.0

  - pip=20.3.4

  - pixman=0.46.0

  - pkgutil-resolve-name=1.3.10

  - platformdirs=4.3.6

  - pluggy=1.5.0

  - ply=3.11

  - prometheus_client=0.21.0

  - prompt-toolkit=3.0.48

  - psutil=6.0.0

  - pthread-stubs=0.4

  - pthreads-win32=2.9.1

  - pure_eval=0.2.3

  - py=1.11.0

  - py-opencv=4.6.0

  - pycparser=2.22

  - pygments=2.18.0

  - pyparsing=3.1.4

  - pyqt=5.15.7

  - pyqt5-sip=12.11.0

  - pysocks=1.7.1

  - pytest=7.1.2

  - python=3.8.5

  - python-dateutil=2.9.0

  - python-fastjsonschema=2.20.0

  - python-json-logger=2.0.7

  - python_abi=3.8

  - pytorch=1.12.1

  - pytorch-mutex=1.0

  - pytz=2024.2

  - pywavelets=1.4.1

  - pywin32=306

  - pywinpty=2.0.13

  - pyyaml=6.0.2

  - pyzmq=26.2.0

  - qt-main=5.15.6

  - qtconsole-base=5.6.1

  - qtpy=2.4.2

  - referencing=0.35.1

  - rfc3339-validator=0.1.4

  - rfc3986-validator=0.1.1

  - rpds-py=0.20.0

  - scikit-image=0.19.3

  - scipy=1.9.1

  - send2trash=1.8.3

  - setuptools=75.3.0

  - sip=6.7.12

  - six=1.16.0

  - snappy=1.1.10

  - sniffio=1.3.1

  - soupsieve=2.6

  - sqlite=3.49.2

  - stack_data=0.6.2

  - svt-av1=1.4.1

  - tbb=2021.13.0

  - terminado=0.18.1

  - tifffile=2023.7.10

  - tinycss2=1.4.0

  - tk=8.6.13

  - toml=0.10.2

  - tomli=2.0.2

  - toolz=1.0.0

  - torchaudio=0.12.1

  - torchvision=0.13.1

  - tornado=6.4.1

  - traitlets=5.14.3

  - types-python-dateutil=2.9.0.20241003

  - typing-extensions=4.12.2

  - typing_utils=0.1.0

  - ucrt=10.0.22621.0

  - unicodedata2=15.1.0

  - uri-template=1.3.0

  - vc=14.3

  - vc14_runtime=14.42.34438

  - vs2015_runtime=14.42.34438

  - wcwidth=0.2.13

  - webcolors=24.8.0

  - webencodings=0.5.1

  - websocket-client=1.8.0

  - wheel=0.45.1

  - widgetsnbextension=4.0.13

  - win_inet_pton=1.1.0

  - winpty=0.4.3

  - x264=1!164.3095

  - x265=3.5

  - xorg-libxau=1.0.11

  - xorg-libxdmcp=1.1.3

  - xz=5.8.1

  - xz-tools=5.8.1

  - yaml=0.2.5

  - zeromq=4.3.5

  - zfp=1.0.1

  - zipp=3.21.0

  - zlib-ng=2.0.7

  - zstandard=0.19.0

  - zstd=1.5.7

  - pip:

      - absl-py==2.3.0

      - aiofiles==24.1.0

      - aiohappyeyeballs==2.4.4

      - aiohttp==3.10.11

      - aiosignal==1.3.1

      - albumentations==1.3.0

      - altair==4.2.2

      - annotated-types==0.7.0

      - antlr4-python3-runtime==4.8

      - appdirs==1.4.4

      - async-timeout==5.0.1

      - backports-zoneinfo==0.2.1

      - blinker==1.8.2

      - blis==0.7.11

      - braceexpand==0.1.7

      - cachetools==5.5.2

      - catalogue==2.0.10

      - cfgv==3.4.0

      - charset-normalizer==2.1.1

      - click==8.1.3

      - clip==1.0

      - cloudpathlib==0.20.0

      - commonmark==0.9.1

      - confection==0.1.5

      - contexttimer==0.3.3

      - cymem==2.0.11

      - decord==0.6.0

      - diffusers==0.15.1

      - distlib==0.3.9

      - docker-pycreds==0.4.0

      - einops==0.6.1

      - fairscale==0.4.4

      - fastapi==0.115.12

      - ffmpy==0.5.0

      - filelock==3.16.1

      - frozenlist==1.5.0

      - ftfy==6.2.3

      - future==1.0.0

      - gitdb==4.0.12

      - gitpython==3.1.44

      - google-auth==2.40.2

      - google-auth-oauthlib==0.4.6

      - gradio==3.31.0

      - gradio-client==0.2.5

      - grpcio==1.70.0

      - huggingface-hub==0.32.2

      - identify==2.6.1

      - iopath==0.1.10

      - joblib==1.4.2

      - kaggle==*******

      - kornia==0.6.7

      - langcodes==3.4.1

      - language-data==1.3.0

      - lightning-utilities==0.11.9

      - linkify-it-py==2.0.3

      - lpips==0.1.4

      - marisa-trie==1.2.1

      - markdown==3.7

      - markdown-it-py==2.2.0

      - mdit-py-plugins==0.3.3

      - mdurl==0.1.2

      - multidict==6.1.0

      - murmurhash==1.0.13

      - narwhals==1.41.0

      - nltk==3.9.1

      - nodeenv==1.9.1

      - oauthlib==3.2.2

      - omegaconf==2.1.1

      - open-clip-torch==2.32.0

      - opencv-python-headless==********

      - opendatasets==0.1.22

      - orjson==3.10.15

      - packaging==24.2

      - pandas==2.0.3

      - pathlib-abc==0.1.1

      - pathtools==0.1.2

      - pathy==0.11.0

      - plotly==6.1.2

      - portalocker==3.0.0

      - pre-commit==3.5.0

      - preshed==3.0.10

      - propcache==0.2.0

      - protobuf==3.19.6

      - pyarrow==17.0.0

      - pyasn1==0.6.1

      - pyasn1-modules==0.4.2

      - pycocoevalcap==1.2

      - pycocotools==2.0.7

      - pydantic==1.10.22

      - pydantic-core==2.27.2

      - pydeck==0.9.1

      - pydeprecate==0.3.1

      - pydub==0.25.1

      - pympler==1.1

      - python-magic==0.4.27

      - python-multipart==0.0.20

      - python-slugify==8.0.4

      - pytorch-fid==0.3.0

      - pytorch-lightning==1.5.0

      - qudida==0.0.4

      - regex==2024.11.6

      - requests==2.28.1

      - requests-oauthlib==2.0.0

      - rich==12.5.1

      - rsa==4.9.1

      - safetensors==0.5.3

      - salesforce-lavis==1.0.2

      - scikit-learn==1.3.2

      - semantic-version==2.10.0

      - sentence-transformers==2.2.2

      - sentencepiece==0.2.0

      - sentry-sdk==2.29.1

      - setproctitle==1.3.6

      - shellingham==1.5.4

      - smart-open==6.4.0

      - smmap==5.0.2

      - spacy==3.4.4

      - spacy-legacy==3.0.12

      - spacy-loggers==1.0.5

      - srsly==2.4.8

      - starlette==0.44.0

      - streamlit==1.22.0

      - tenacity==8.5.0

      - tensorboard==2.10.0

      - tensorboard-data-server==0.6.1

      - tensorboard-plugin-wit==1.8.1

      - text-unidecode==1.3

      - thinc==8.1.12

      - threadpoolctl==3.5.0

      - timm==0.4.12

      - tokenizers==0.12.1

      - torchmetrics==1.5.2

      - tqdm==4.64.0

      - transformers==4.26.1

      - typer==0.7.0

      - tzdata==2025.2

      - tzlocal==5.2

      - uc-micro-py==1.0.3

      - urllib3==1.26.20

      - uvicorn==0.33.0

      - validators==0.34.0

      - virtualenv==20.31.2

      - wandb==0.13.10

      - wasabi==0.10.1

      - watchdog==4.0.2

      - weasel==0.4.1

      - webdataset==0.2.100

      - websockets==12.0

      - werkzeug==3.0.6

      - wrapt==1.17.2

      - yarl==1.15.2

prefix: C:\Users\<USER>\anaconda3\envs\mini

