model:
  target: cldm.exemplar_cldm.ExemplarControlLDM
  params:
    linear_start: 0.00085
    linear_end: 0.0120
    num_timesteps_cond: 1
    log_every_t: 200
    timesteps: 1000
    first_stage_key: "jpg"
    cond_stage_key: "txt"
    control_key: "hint"
    masked_image: "mask_img"
    mask: "mask"
    image_size: 64
    channels: 4
    cond_stage_trainable: false
    conditioning_key: crossattn
    monitor: val/loss_simple_ema
    scale_factor: 0.18215
    use_ema: False
    only_mid_control: False
    load_loss: False
    
    # Exemplar-specific parameters
    use_exemplar_loss: True
    exemplar_loss_weight: 1.0
    contextual_loss_weight: 1.0
    grayscale_loss_weight: 1000.0

    control_stage_config:
      target: cldm.cldm.ControlNet
      params:
        image_size: 32 # unused
        in_channels: 4
        hint_channels: 3
        model_channels: 320
        attention_resolutions: [ 4, 2, 1 ]
        num_res_blocks: 2
        channel_mult: [ 1, 2, 4, 4 ]
        num_heads: 8
        use_spatial_transformer: True
        transformer_depth: 1
        context_dim: 768
        use_checkpoint: True
        legacy: False

    unet_config:
      target: cldm.cldm.ControlledUnetModel
      params:
        image_size: 32 # unused
        in_channels: 9
        out_channels: 4
        model_channels: 320
        attention_resolutions: [ 4, 2, 1 ]
        num_res_blocks: 2
        channel_mult: [ 1, 2, 4, 4 ]
        num_heads: 8
        use_spatial_transformer: True
        transformer_depth: 1
        context_dim: 768
        use_checkpoint: True
        legacy: False

    first_stage_config:
      target: ldm.models.autoencoder.AutoencoderKL
      params:
        embed_dim: 4
        monitor: val/rec_loss
        ddconfig:
          double_z: true
          z_channels: 4
          resolution: 256
          in_channels: 3
          out_ch: 3
          ch: 128
          ch_mult:
          - 1
          - 2
          - 4
          - 4
          num_res_blocks: 2
          attn_resolutions: []
          dropout: 0.0
        lossconfig:
          target: torch.nn.Identity

    contextual_stage_config:
      target: models_deep_exp.NonlocalNet.VGG19_pytorch
      
    cond_stage_config:
      target: ldm.modules.encoders.modules.FrozenCLIPDualEmbedder

    # Exemplar encoder configuration
    exemplar_encoder_config:
      target: ldm.modules.encoders.exemplar_encoder.CLIPExemplarEncoder
      params:
        clip_model_name: "openai/clip-vit-base-patch32"
        feature_dim: 768
        output_dim: 768
        freeze_clip: True
        use_projection: True

    # Exemplar loss configuration
    exemplar_loss_config:
      target: ldm.modules.losses.exemplar_loss.ExemplarLoss
      params:
        contextual_weight: 1.0
        grayscale_weight: 1000.0
        use_adaptive_weights: False
        
    # VGG19 contextual loss configuration
    contextual_loss_config:
      target: ldm.modules.losses.contextual_loss.VGG19ContextualLoss
      params:
        layers: [3, 5]
        layer_weights: [1.0, 1.0]
        temperature: 0.1
        
    # Grayscale consistency loss configuration
    grayscale_loss_config:
      target: ldm.modules.losses.grayscale_loss.GrayscaleConsistencyLoss
      params:
        use_perceptual: False
