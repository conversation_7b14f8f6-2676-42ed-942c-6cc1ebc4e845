"""
RTX 3050 Optimized ControlLDM Implementation

This is a modified copy of the original cldm/cldm.py with RTX 3050 specific optimizations:
- FP16 mixed precision support
- Memory-efficient attention
- Gradient checkpointing
- Optimized device management
- Memory monitoring

Based on: clone/newCtrlColor/cldm/cldm.py
Optimized for: NVIDIA GeForce RTX 3050 Laptop GPU (4.3GB VRAM)
"""

import einops
import torch
import torch as th
import torch.nn as nn
from typing import Optional, Dict, Any

# Import optimized config
from .config_rtx3050 import (
    USE_FP16, DEVICE, ENABLE_GRADIENT_CHECKPOINTING,
    RTX3050MemoryManager, RTX3050AutocastManager,
    clear_gpu_cache
)

# Original imports (preserved)
from ldm.modules.diffusionmodules.util import (
    conv_nd,
    linear,
    zero_module,
    timestep_embedding,
)

from einops import rearrange, repeat
from torchvision.utils import make_grid
from ldm.modules.attention import SpatialTransformer
from ldm.modules.attention_dcn_control import SpatialTransformer_dcn
from ldm.modules.diffusionmodules.openaimodel import UNetModel, TimestepEmbedSequential, ResBlock, Downsample, AttentionBlock
from ldm.models.diffusion.ddpm import LatentDiffusion
from ldm.util import log_txt_as_img, exists, instantiate_from_config
from ldm.models.diffusion.ddim import DDIMSampler


class RTX3050OptimizedControlledUnetModel(UNetModel):
    """
    RTX 3050 Optimized ControlledUnetModel
    
    Enhancements:
    - FP16 mixed precision support
    - Memory-efficient forward pass
    - Gradient checkpointing
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # RTX 3050 optimizations
        self.use_fp16_rtx3050 = USE_FP16
        self.enable_checkpointing = ENABLE_GRADIENT_CHECKPOINTING
        
        # Set dtype based on FP16 setting
        if self.use_fp16_rtx3050:
            self.dtype = torch.float16
        
        print(f"🎯 RTX3050 ControlledUnetModel initialized:")
        print(f"   - FP16: {self.use_fp16_rtx3050}")
        print(f"   - Gradient checkpointing: {self.enable_checkpointing}")
    
    def forward(self, x, timesteps=None, context=None, control=None, only_mid_control=False, **kwargs):
        """RTX 3050 optimized forward pass with memory management"""
        
        with RTX3050MemoryManager(clear_cache_before=False, clear_cache_after=False):
            with RTX3050AutocastManager(enabled=self.use_fp16_rtx3050):
                hs = []
                
                # Timestep embedding with no_grad for memory efficiency
                with torch.no_grad():
                    t_emb = timestep_embedding(timesteps, self.model_channels, repeat_only=False)
                    emb = self.time_embed(t_emb)
                    h = x.type(self.dtype)
                    
                    # Input blocks with optional gradient checkpointing
                    for module in self.input_blocks:
                        if self.enable_checkpointing and self.training:
                            h = torch.utils.checkpoint.checkpoint(module, h, emb, context)
                        else:
                            h = module(h, emb, context)
                        hs.append(h)
                    
                    # Middle block
                    if self.enable_checkpointing and self.training:
                        h = torch.utils.checkpoint.checkpoint(self.middle_block, h, emb, context)
                    else:
                        h = self.middle_block(h, emb, context)

                # Apply control if available
                if control is not None:
                    h += control.pop()

                # Output blocks
                for i, module in enumerate(self.output_blocks):
                    if only_mid_control or control is None:
                        h = torch.cat([h, hs.pop()], dim=1)
                    else:
                        h = torch.cat([h, hs.pop() + control.pop()], dim=1)
                    
                    if self.enable_checkpointing and self.training:
                        h = torch.utils.checkpoint.checkpoint(module, h, emb, context)
                    else:
                        h = module(h, emb, context)

                h = h.type(x.dtype)
                h = self.out(h)
                
                return h


class RTX3050OptimizedControlNet(nn.Module):
    """
    RTX 3050 Optimized ControlNet
    
    Enhancements:
    - Memory-efficient initialization
    - FP16 support
    - Optimized forward pass
    """
    
    def __init__(self, *args, **kwargs):
        # Extract RTX 3050 specific parameters
        self.use_fp16_rtx3050 = kwargs.pop('use_fp16_rtx3050', USE_FP16)
        self.enable_checkpointing = kwargs.pop('enable_checkpointing', ENABLE_GRADIENT_CHECKPOINTING)
        
        super().__init__()
        
        # Initialize with original ControlNet parameters
        self._init_controlnet(*args, **kwargs)
        
        print(f"🎯 RTX3050 ControlNet initialized:")
        print(f"   - FP16: {self.use_fp16_rtx3050}")
        print(f"   - Gradient checkpointing: {self.enable_checkpointing}")
    
    def _init_controlnet(self, image_size, in_channels, model_channels, hint_channels, 
                        num_res_blocks, attention_resolutions, dropout=0,
                        channel_mult=(1, 2, 4, 8), conv_resample=True, dims=2,
                        use_checkpoint=False, use_fp16=False, num_heads=-1,
                        num_head_channels=-1, num_heads_upsample=-1,
                        use_scale_shift_norm=False, resblock_updown=False,
                        use_new_attention_order=False, use_spatial_transformer=False,
                        transformer_depth=1, context_dim=None, n_embed=None,
                        legacy=True, disable_self_attentions=None,
                        num_attention_blocks=None, disable_middle_self_attn=False,
                        use_linear_in_transformer=False):
        """Initialize ControlNet with RTX 3050 optimizations"""
        
        # Use RTX 3050 FP16 setting
        use_fp16 = self.use_fp16_rtx3050
        use_checkpoint = self.enable_checkpointing
        
        # Spatial transformer validation
        if use_spatial_transformer:
            assert context_dim is not None, 'Context dimension required for spatial transformer'

        if context_dim is not None:
            assert use_spatial_transformer, 'Spatial transformer required for cross-attention'
            from omegaconf.listconfig import ListConfig
            if type(context_dim) == ListConfig:
                context_dim = list(context_dim)

        # Head configuration
        if num_heads_upsample == -1:
            num_heads_upsample = num_heads
        if num_heads == -1:
            assert num_head_channels != -1, 'Either num_heads or num_head_channels required'
        if num_head_channels == -1:
            assert num_heads != -1, 'Either num_heads or num_head_channels required'

        # Store configuration
        self.dims = dims
        self.image_size = image_size
        self.in_channels = in_channels
        self.model_channels = model_channels
        
        # Set dtype based on FP16 setting
        self.dtype = th.float16 if use_fp16 else th.float32
        
        # Handle num_res_blocks
        if isinstance(num_res_blocks, int):
            self.num_res_blocks = len(channel_mult) * [num_res_blocks]
        else:
            if len(num_res_blocks) != len(channel_mult):
                raise ValueError("num_res_blocks must match channel_mult length")
            self.num_res_blocks = num_res_blocks
        
        # Validation for attention settings
        if disable_self_attentions is not None:
            assert len(disable_self_attentions) == len(channel_mult)
        if num_attention_blocks is not None:
            assert len(num_attention_blocks) == len(self.num_res_blocks)
            assert all(map(lambda i: self.num_res_blocks[i] >= num_attention_blocks[i], 
                          range(len(num_attention_blocks))))

        # Store remaining configuration
        self.attention_resolutions = attention_resolutions
        self.dropout = dropout
        self.channel_mult = channel_mult
        self.conv_resample = conv_resample
        self.use_checkpoint = use_checkpoint
        self.num_heads = num_heads
        self.num_head_channels = num_head_channels
        self.num_heads_upsample = num_heads_upsample
        self.predict_codebook_ids = n_embed is not None

        # Time embedding
        time_embed_dim = model_channels * 4
        self.time_embed = nn.Sequential(
            linear(model_channels, time_embed_dim),
            nn.SiLU(),
            linear(time_embed_dim, time_embed_dim),
        )

        # Input blocks
        self.input_blocks = nn.ModuleList([
            TimestepEmbedSequential(
                conv_nd(dims, in_channels, model_channels, 3, padding=1)
            )
        ])
        self.zero_convs = nn.ModuleList([self.make_zero_conv(model_channels)])

        # Input hint block
        self.input_hint_block = TimestepEmbedSequential(
            conv_nd(dims, hint_channels, 16, 3, padding=1),
            nn.SiLU(),
            conv_nd(dims, 16, 16, 3, padding=1),
            nn.SiLU(),
            conv_nd(dims, 16, 32, 3, padding=1, stride=2),
            nn.SiLU(),
            conv_nd(dims, 32, 32, 3, padding=1),
            nn.SiLU(),
            conv_nd(dims, 32, 96, 3, padding=1, stride=2),
            nn.SiLU(),
            conv_nd(dims, 96, 96, 3, padding=1),
            nn.SiLU(),
            conv_nd(dims, 96, 256, 3, padding=1, stride=2),
            nn.SiLU(),
            zero_module(conv_nd(dims, 256, model_channels, 3, padding=1))
        )

        # Build the rest of the network
        self._build_network(channel_mult, attention_resolutions, disable_self_attentions,
                           num_attention_blocks, use_spatial_transformer, transformer_depth,
                           context_dim, disable_middle_self_attn, use_linear_in_transformer,
                           use_scale_shift_norm, resblock_updown, legacy, use_new_attention_order)

    def _build_network(self, channel_mult, attention_resolutions, disable_self_attentions,
                      num_attention_blocks, use_spatial_transformer, transformer_depth,
                      context_dim, disable_middle_self_attn, use_linear_in_transformer,
                      use_scale_shift_norm, resblock_updown, legacy, use_new_attention_order):
        """Build the network layers with memory optimization"""
        
        self._feature_size = self.model_channels
        input_block_chans = [self.model_channels]
        ch = self.model_channels
        ds = 1
        
        # Build input blocks
        for level, mult in enumerate(channel_mult):
            for nr in range(self.num_res_blocks[level]):
                layers = [
                    ResBlock(
                        ch,
                        self.model_channels * 4,  # time_embed_dim
                        self.dropout,
                        out_channels=mult * self.model_channels,
                        dims=self.dims,
                        use_checkpoint=self.use_checkpoint,
                        use_scale_shift_norm=use_scale_shift_norm,
                    )
                ]
                ch = mult * self.model_channels
                
                # Add attention if needed
                if ds in attention_resolutions:
                    if self.num_head_channels == -1:
                        dim_head = ch // self.num_heads
                    else:
                        num_heads = ch // self.num_head_channels
                        dim_head = self.num_head_channels
                    
                    if legacy:
                        dim_head = ch // self.num_heads if use_spatial_transformer else self.num_head_channels
                    
                    if exists(disable_self_attentions):
                        disabled_sa = disable_self_attentions[level]
                    else:
                        disabled_sa = False

                    if not exists(num_attention_blocks) or nr < num_attention_blocks[level]:
                        layers.append(
                            AttentionBlock(
                                ch,
                                use_checkpoint=self.use_checkpoint,
                                num_heads=self.num_heads,
                                num_head_channels=dim_head,
                                use_new_attention_order=use_new_attention_order,
                            ) if not use_spatial_transformer else SpatialTransformer(
                                ch, self.num_heads, dim_head, depth=transformer_depth, 
                                context_dim=context_dim, disable_self_attn=disabled_sa, 
                                use_linear=use_linear_in_transformer, use_checkpoint=self.use_checkpoint
                            )
                        )
                
                self.input_blocks.append(TimestepEmbedSequential(*layers))
                self.zero_convs.append(self.make_zero_conv(ch))
                self._feature_size += ch
                input_block_chans.append(ch)
            
            # Downsample
            if level != len(channel_mult) - 1:
                out_ch = ch
                self.input_blocks.append(
                    TimestepEmbedSequential(
                        ResBlock(
                            ch, self.model_channels * 4, self.dropout,
                            out_channels=out_ch, dims=self.dims,
                            use_checkpoint=self.use_checkpoint,
                            use_scale_shift_norm=use_scale_shift_norm, down=True,
                        ) if resblock_updown else Downsample(
                            ch, self.conv_resample, dims=self.dims, out_channels=out_ch
                        )
                    )
                )
                ch = out_ch
                input_block_chans.append(ch)
                self.zero_convs.append(self.make_zero_conv(ch))
                ds *= 2
                self._feature_size += ch

        # Middle block
        if self.num_head_channels == -1:
            dim_head = ch // self.num_heads
        else:
            num_heads = ch // self.num_head_channels
            dim_head = self.num_head_channels
        
        if legacy:
            dim_head = ch // self.num_heads if use_spatial_transformer else self.num_head_channels
        
        self.middle_block = TimestepEmbedSequential(
            ResBlock(
                ch, self.model_channels * 4, self.dropout, dims=self.dims,
                use_checkpoint=self.use_checkpoint, use_scale_shift_norm=use_scale_shift_norm,
            ),
            AttentionBlock(
                ch, use_checkpoint=self.use_checkpoint, num_heads=self.num_heads,
                num_head_channels=dim_head, use_new_attention_order=use_new_attention_order,
            ) if not use_spatial_transformer else SpatialTransformer(
                ch, self.num_heads, dim_head, depth=transformer_depth, context_dim=context_dim,
                disable_self_attn=disable_middle_self_attn, use_linear=use_linear_in_transformer,
                use_checkpoint=self.use_checkpoint
            ),
            ResBlock(
                ch, self.model_channels * 4, self.dropout, dims=self.dims,
                use_checkpoint=self.use_checkpoint, use_scale_shift_norm=use_scale_shift_norm,
            ),
        )
        self.middle_block_out = self.make_zero_conv(ch)
        self._feature_size += ch

    def make_zero_conv(self, channels):
        """Create zero convolution layer"""
        return TimestepEmbedSequential(zero_module(conv_nd(self.dims, channels, channels, 1, padding=0)))

    def forward(self, x, hint, timesteps, context, **kwargs):
        """RTX 3050 optimized forward pass"""
        
        with RTX3050AutocastManager(enabled=self.use_fp16_rtx3050):
            t_emb = timestep_embedding(timesteps, self.model_channels, repeat_only=False)
            emb = self.time_embed(t_emb)

            guided_hint = self.input_hint_block(hint, emb, context)
            outs = []

            h = x.type(self.dtype)
            
            for module, zero_conv in zip(self.input_blocks, self.zero_convs):
                if guided_hint is not None:
                    if self.enable_checkpointing and self.training:
                        h = torch.utils.checkpoint.checkpoint(module, h, emb, context)
                    else:
                        h = module(h, emb, context)
                    h += guided_hint
                    guided_hint = None
                else:
                    if self.enable_checkpointing and self.training:
                        h = torch.utils.checkpoint.checkpoint(module, h, emb, context)
                    else:
                        h = module(h, emb, context)
                
                outs.append(zero_conv(h, emb, context))

            if self.enable_checkpointing and self.training:
                h = torch.utils.checkpoint.checkpoint(self.middle_block, h, emb, context)
            else:
                h = self.middle_block(h, emb, context)
            
            outs.append(self.middle_block_out(h, emb, context))

            return outs
