"""
RTX 3050 Optimized Configuration for CtrlColor

This is a modified copy of the original config.py with RTX 3050 specific optimizations:
- Memory management for 4.3GB VRAM
- FP16 mixed precision
- Optimal batch sizes
- Memory-efficient settings

Based on: clone/newCtrlColor/config.py
Optimized for: NVIDIA GeForce RTX 3050 Laptop GPU
"""

import torch
import os

# ============================================================================
# RTX 3050 DEVICE OPTIMIZATION SETTINGS
# ============================================================================

# Device Configuration
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
USE_FP16 = True  # Enable FP16 for 50% memory savings
MEMORY_FRACTION = 0.85  # Use 85% of 4.3GB VRAM

# Batch Size Optimization (based on test results)
INFERENCE_BATCH_SIZE = 1  # Conservative for inference
TRAINING_BATCH_SIZE = 2   # Optimal for training
MAX_BATCH_SIZE = 4        # Maximum tested batch size

# Image Resolution Optimization
DEFAULT_IMAGE_RESOLUTION = 512  # Conservative default
MAX_IMAGE_RESOLUTION = 768      # Maximum tested resolution
MIN_IMAGE_RESOLUTION = 256      # Minimum for quality

# Memory Management
ENABLE_MEMORY_EFFICIENT_ATTENTION = True
ENABLE_GRADIENT_CHECKPOINTING = True
CLEAR_CACHE_FREQUENCY = 10  # Clear cache every N steps

# Performance Optimization
ENABLE_CUDNN_BENCHMARK = True
ENABLE_AUTOCAST = True
GRADIENT_ACCUMULATION_STEPS = 4  # For effective larger batch sizes

# ============================================================================
# ORIGINAL CONFIG SETTINGS (PRESERVED)
# ============================================================================

# Original save_memory setting
save_memory = False  # Set to True if you have limited VRAM

# Model paths (unchanged)
model_path = "./pretrained_models/main_model.ckpt"
vae_path = "./pretrained_models/content-guided_deformable_vae.ckpt"
config_path = "./models/cldm_v15_inpainting_infer1.yaml"

# ============================================================================
# RTX 3050 MEMORY OPTIMIZATION FUNCTIONS
# ============================================================================

def setup_rtx3050_optimizations():
    """
    Apply RTX 3050 specific optimizations
    """
    if torch.cuda.is_available():
        # Set memory fraction
        torch.cuda.set_per_process_memory_fraction(MEMORY_FRACTION)
        
        # Enable cuDNN benchmark for consistent input sizes
        if ENABLE_CUDNN_BENCHMARK:
            torch.backends.cudnn.benchmark = True
        
        # Enable memory efficient attention if available
        if hasattr(torch.backends.cuda, 'enable_math_sdp'):
            torch.backends.cuda.enable_math_sdp(ENABLE_MEMORY_EFFICIENT_ATTENTION)
        
        print(f"✅ RTX 3050 optimizations applied:")
        print(f"   - Memory fraction: {MEMORY_FRACTION}")
        print(f"   - FP16 enabled: {USE_FP16}")
        print(f"   - cuDNN benchmark: {ENABLE_CUDNN_BENCHMARK}")
        print(f"   - Inference batch size: {INFERENCE_BATCH_SIZE}")
        print(f"   - Training batch size: {TRAINING_BATCH_SIZE}")


def get_optimal_batch_size(mode="inference"):
    """
    Get optimal batch size based on mode
    
    Args:
        mode: "inference" or "training"
        
    Returns:
        Optimal batch size for the mode
    """
    if mode == "inference":
        return INFERENCE_BATCH_SIZE
    elif mode == "training":
        return TRAINING_BATCH_SIZE
    else:
        return 1


def get_optimal_image_resolution(conservative=True):
    """
    Get optimal image resolution
    
    Args:
        conservative: If True, use conservative resolution
        
    Returns:
        Optimal image resolution
    """
    if conservative:
        return DEFAULT_IMAGE_RESOLUTION
    else:
        return MAX_IMAGE_RESOLUTION


def clear_gpu_cache():
    """Clear GPU cache to free memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()


def get_device_info():
    """Get RTX 3050 device information"""
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name(0)
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        return {
            'device': DEVICE,
            'name': device_name,
            'total_memory_gb': total_memory,
            'fp16_enabled': USE_FP16,
            'memory_fraction': MEMORY_FRACTION
        }
    else:
        return {'device': 'cpu', 'name': 'CPU', 'total_memory_gb': 0}


# ============================================================================
# CONTEXT MANAGERS FOR MEMORY OPTIMIZATION
# ============================================================================

class RTX3050MemoryManager:
    """Context manager for RTX 3050 memory optimization"""
    
    def __init__(self, clear_cache_before=True, clear_cache_after=True):
        self.clear_cache_before = clear_cache_before
        self.clear_cache_after = clear_cache_after
    
    def __enter__(self):
        if self.clear_cache_before:
            clear_gpu_cache()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.clear_cache_after:
            clear_gpu_cache()


class RTX3050AutocastManager:
    """Context manager for FP16 autocast"""
    
    def __init__(self, enabled=None):
        self.enabled = enabled if enabled is not None else USE_FP16
        self.autocast_context = None
    
    def __enter__(self):
        if self.enabled and torch.cuda.is_available():
            self.autocast_context = torch.cuda.amp.autocast()
            return self.autocast_context.__enter__()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.autocast_context:
            return self.autocast_context.__exit__(exc_type, exc_val, exc_tb)


# ============================================================================
# INITIALIZATION
# ============================================================================

# Apply optimizations on import
if __name__ != "__main__":
    setup_rtx3050_optimizations()

# Export key settings for easy access
__all__ = [
    'DEVICE', 'USE_FP16', 'MEMORY_FRACTION',
    'INFERENCE_BATCH_SIZE', 'TRAINING_BATCH_SIZE',
    'DEFAULT_IMAGE_RESOLUTION', 'MAX_IMAGE_RESOLUTION',
    'setup_rtx3050_optimizations', 'get_optimal_batch_size',
    'get_optimal_image_resolution', 'clear_gpu_cache',
    'RTX3050MemoryManager', 'RTX3050AutocastManager',
    'get_device_info'
]

# Print optimization status
if torch.cuda.is_available():
    device_info = get_device_info()
    print(f"🎯 RTX 3050 Config Loaded: {device_info['name']} ({device_info['total_memory_gb']:.1f}GB)")
else:
    print("⚠️ CUDA not available, using CPU mode")
