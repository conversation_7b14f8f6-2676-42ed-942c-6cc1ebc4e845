"""
Enhanced RTX 3050 Optimization Settings

Based on CUDA OOM analysis from test_rtx3050_simple.log, this provides
comprehensive memory management for RTX 3050 (4GB VRAM).

Key fixes for the OOM issue:
- CPU-first model loading strategy
- Memory-efficient checkpoint loading
- Dynamic memory monitoring
- Automatic fallback to CPU when needed

Optimizations:
- FP16 mixed precision (50% memory savings)
- Memory management (85% VRAM usage)
- Optimal batch sizes
- Automatic cache clearing
- CPU fallback strategies
"""

import gc
import os
from contextlib import contextmanager

import torch

# ============================================================================
# RTX 3050 OPTIMIZATIONS (ENHANCED)
# ============================================================================


class MemoryManager:
    """Enhanced memory manager for RTX 3050"""

    def __init__(self):
        self.max_memory_gb = 3.4  # 85% of 4GB

    def get_memory_info(self):
        """Get current GPU memory usage"""
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3
            reserved = torch.cuda.memory_reserved() / 1024**3
            return allocated, reserved
        return 0, 0

    def check_memory_available(self, required_gb):
        """Check if enough memory is available"""
        allocated, _ = self.get_memory_info()
        available = self.max_memory_gb - allocated
        return available >= required_gb

    @contextmanager
    def memory_efficient_loading(self):
        """Context manager for memory-efficient operations"""
        clear_memory()
        try:
            yield
        finally:
            clear_memory()


# Global memory manager instance
memory_manager = MemoryManager()


def setup_rtx3050():
    """Apply enhanced RTX 3050 optimizations"""
    if torch.cuda.is_available():
        # Set memory fraction to 85% of 4GB
        torch.cuda.set_per_process_memory_fraction(0.85)

        # Set CUDA memory allocation strategy to avoid fragmentation
        os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128"

        # Enable cuDNN optimizations
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.enabled = True

        # Enable memory efficient attention if available
        try:
            torch.backends.cuda.enable_flash_sdp(True)
        except:
            pass  # Flash attention not available

        print("✅ Enhanced RTX 3050 optimizations applied:")
        print(f"   - GPU: {torch.cuda.get_device_name(0)}")
        print(
            f"   - Memory limit: 85% ({torch.cuda.get_device_properties(0).total_memory / 1024**3 * 0.85:.1f}GB)"
        )
        print("   - FP16: Enabled")
        print("   - Batch size: 1 (memory-safe)")
        print("   - Memory fragmentation: Reduced")
        print("   - CPU fallback: Available")
    else:
        print("⚠️ CUDA not available, using CPU mode")


def clear_memory():
    """Clear GPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()


def get_optimal_settings():
    """Get optimal settings for RTX 3050"""
    return {
        "num_samples": 1,  # Safe batch size
        "image_resolution": 256,  # Memory-safe resolution (reduced from 512)
        "ddim_steps": 10,  # Reduced steps for memory efficiency
        "use_fp16": True,  # Enable FP16
        "clear_cache": True,  # Clear cache between runs
        "cpu_fallback": True,  # Enable CPU fallback
        "load_on_cpu_first": True,  # Load model on CPU first
    }


def load_model_memory_safe(create_model_func, config_path, ckpt_path):
    """
    Memory-safe model loading for RTX 3050

    This addresses the CUDA OOM issue from test_rtx3050_simple.log
    """
    print("🔧 Loading model with memory-safe strategy...")

    with memory_manager.memory_efficient_loading():
        try:
            # Step 1: Create model on CPU
            print("   - Creating model on CPU...")
            model = create_model_func(config_path).cpu()

            # Step 2: Load checkpoint to CPU
            print("   - Loading checkpoint to CPU...")
            checkpoint = torch.load(ckpt_path, map_location="cpu")

            # Step 3: Load state dict on CPU
            print("   - Loading state dict on CPU...")
            if hasattr(model, "load_state_dict"):
                model.load_state_dict(checkpoint, strict=False)

            # Clean up checkpoint
            del checkpoint
            clear_memory()

            # Step 4: Try GPU transfer
            if torch.cuda.is_available():
                print("   - Attempting GPU transfer...")

                # Estimate model size
                param_count = sum(p.numel() for p in model.parameters())
                model_size_gb = param_count * 4 / 1024**3

                if memory_manager.check_memory_available(model_size_gb):
                    model = model.cuda().half()  # Move to GPU and convert to FP16
                    print("   ✅ Model loaded on GPU with FP16")
                else:
                    print("   ⚠️ Insufficient GPU memory, keeping on CPU")

            return model

        except Exception as e:
            print(f"   ❌ Model loading failed: {e}")
            return None


# Auto-apply optimizations when imported
setup_rtx3050()

# Export key functions
__all__ = [
    "setup_rtx3050",
    "clear_memory",
    "get_optimal_settings",
    "load_model_memory_safe",
    "memory_manager",
]
