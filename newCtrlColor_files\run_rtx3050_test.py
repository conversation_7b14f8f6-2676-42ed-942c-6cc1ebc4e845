"""
RTX 3050 Test Launcher

Simple launcher script that sets up the Python path correctly and runs the RTX 3050 optimization tests.
Use this script to test your RTX 3050 setup without import issues.
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def run_simple_test():
    """Run the simple RTX 3050 test"""
    print("🚀 Running Simple RTX 3050 Test...")
    try:
        from full.device_optimization.simple_rtx3050_test import main
        main()
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("⚠️ Running basic CUDA test instead...")
        run_basic_cuda_test()

def run_basic_cuda_test():
    """Run basic CUDA availability test"""
    import torch
    
    print("\n🔍 BASIC CUDA TEST")
    print("="*30)
    
    if torch.cuda.is_available():
        print("✅ CUDA is available")
        print(f"✅ Device count: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"✅ GPU {i}: {props.name}")
            print(f"   - Memory: {props.total_memory / 1e9:.1f}GB")
            print(f"   - Compute Capability: {props.major}.{props.minor}")
        
        # Test basic operations
        try:
            device = torch.device('cuda:0')
            test_tensor = torch.randn(1000, 1000, device=device)
            result = torch.matmul(test_tensor, test_tensor)
            print("✅ Basic CUDA operations work")
            
            # Test FP16
            try:
                fp16_tensor = torch.randn(512, 512, dtype=torch.float16, device=device)
                fp16_result = torch.matmul(fp16_tensor, fp16_tensor)
                print("✅ FP16 operations work")
            except Exception as e:
                print(f"❌ FP16 test failed: {e}")
            
            # Test memory management
            memory_before = torch.cuda.memory_allocated(device)
            large_tensor = torch.randn(2000, 2000, device=device)
            memory_after = torch.cuda.memory_allocated(device)
            del large_tensor
            torch.cuda.empty_cache()
            memory_final = torch.cuda.memory_allocated(device)
            
            print(f"✅ Memory test: {memory_before/1e6:.1f}MB → {memory_after/1e6:.1f}MB → {memory_final/1e6:.1f}MB")
            
        except Exception as e:
            print(f"❌ CUDA operations failed: {e}")
    else:
        print("❌ CUDA is not available")
        print("   - Check NVIDIA drivers")
        print("   - Check PyTorch CUDA installation")

def run_full_test():
    """Run the full RTX 3050 optimization test"""
    print("🚀 Running Full RTX 3050 Optimization Test...")
    try:
        from full.device_optimization.test_rtx3050_optimizations import main
        main()
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("⚠️ Some dependencies missing, running simple test instead...")
        run_simple_test()

def run_implementation_test():
    """Run the complete implementation test"""
    print("🚀 Running Complete Implementation Test...")
    try:
        from full.test_implementation import run_all_tests
        run_all_tests()
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("⚠️ Some modules missing, running basic test instead...")
        run_basic_cuda_test()

def main():
    """Main launcher function"""
    print("🎯 RTX 3050 CTRLCOLOR TEST LAUNCHER")
    print("="*50)
    
    print("\nAvailable tests:")
    print("1. Basic CUDA Test (always works)")
    print("2. Simple RTX 3050 Test (minimal dependencies)")
    print("3. Full RTX 3050 Optimization Test")
    print("4. Complete Implementation Test")
    print("5. Auto-select best test")
    
    try:
        choice = input("\nSelect test (1-5, default=5): ").strip()
        if not choice:
            choice = "5"
    except KeyboardInterrupt:
        print("\n\n👋 Test cancelled by user")
        return
    
    print(f"\nRunning test option {choice}...")
    print("="*50)
    
    if choice == "1":
        run_basic_cuda_test()
    elif choice == "2":
        run_simple_test()
    elif choice == "3":
        run_full_test()
    elif choice == "4":
        run_implementation_test()
    elif choice == "5":
        # Auto-select best available test
        print("🤖 Auto-selecting best available test...")
        
        # Try tests in order of complexity
        try:
            from full.test_implementation import run_all_tests
            print("✅ Running complete implementation test...")
            run_all_tests()
        except ImportError:
            try:
                from full.device_optimization.test_rtx3050_optimizations import main
                print("✅ Running full RTX 3050 test...")
                main()
            except ImportError:
                try:
                    from full.device_optimization.simple_rtx3050_test import main
                    print("✅ Running simple RTX 3050 test...")
                    main()
                except ImportError:
                    print("✅ Running basic CUDA test...")
                    run_basic_cuda_test()
    else:
        print(f"❌ Invalid choice: {choice}")
        print("Running basic CUDA test instead...")
        run_basic_cuda_test()
    
    print("\n" + "="*50)
    print("🎉 Test completed!")
    print("\n📋 Next steps:")
    print("1. If CUDA tests passed, your RTX 3050 is ready")
    print("2. Use FP16 precision for best performance")
    print("3. Keep batch sizes small (1-2)")
    print("4. Monitor memory usage during inference")

if __name__ == "__main__":
    main()
