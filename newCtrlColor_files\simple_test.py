#!/usr/bin/env python3
"""
Simple test to verify exemplar components can be imported
"""

def test_imports():
    """Test basic imports"""
    print("Testing imports...")
    
    try:
        print("1. Testing torch...")
        import torch
        print(f"   ✅ PyTorch {torch.__version__}")
        
        print("2. Testing exemplar encoder...")
        from ldm.modules.encoders.exemplar_encoder import CLIPExemplarEncoder
        print("   ✅ CLIPExemplarEncoder imported")
        
        print("3. Testing exemplar loss...")
        from ldm.modules.losses.exemplar_loss import ExemplarLoss
        print("   ✅ ExemplarLoss imported")
        
        print("4. Testing contextual loss...")
        from ldm.modules.losses.contextual_loss import VGG19ContextualLoss
        print("   ✅ VGG19ContextualLoss imported")
        
        print("5. Testing grayscale loss...")
        from ldm.modules.losses.grayscale_loss import GrayscaleConsistencyLoss
        print("   ✅ GrayscaleConsistencyLoss imported")
        
        print("6. Testing ExemplarControlLDM...")
        from cldm.exemplar_cldm import ExemplarControlLDM
        print("   ✅ ExemplarControlLDM imported")
        
        print("\n🎉 ALL IMPORTS SUCCESSFUL!")
        print("Exemplar-based colorization components are ready!")
        return True
        
    except Exception as e:
        print(f"\n❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_imports()
