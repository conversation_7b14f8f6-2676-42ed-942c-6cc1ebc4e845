#!/usr/bin/env python3
"""
Step-by-step debugging for CtrlColor
Usage: python step_debug.py
"""

import torch
import traceback

def debug_step_by_step():
    """Debug each component step by step"""
    
    print("=== Step 1: Import Test ===")
    try:
        from cldm.exemplar_cldm import ExemplarControlLDM
        print("✅ Import successful")
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return
    
    print("\n=== Step 2: Device Check ===")
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")
    
    print("\n=== Step 3: Config Loading ===")
    try:
        import yaml
        config_path = "./models/exemplar_cldm_v15.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        print("✅ Config loaded successfully")
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return
    
    print("\n=== Step 4: Model Initialization ===")
    try:
        model = ExemplarControlLDM(**config['model']['params'])
        model = model.to(device)
        print("✅ Model initialized successfully")
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        traceback.print_exc()
        return
    
    print("\n=== Step 5: Test Batch Creation ===")
    try:
        batch_size = 1
        test_batch = {
            "image": torch.randn(batch_size, 256, 256, 3).to(device),
            "txt": [("red car", torch.randn(batch_size, 256, 256, 3).to(device))],
            "hint": torch.randn(batch_size, 256, 256, 3).to(device),
            "exemplar": torch.randn(batch_size, 3, 256, 256).to(device),
            "jpg": torch.randn(batch_size, 256, 256, 3).to(device),
            "mask": torch.ones(batch_size, 1, 256, 256).to(device),
            "mask_img": torch.randn(batch_size, 3, 256, 256).to(device),
        }
        print("✅ Test batch created successfully")
    except Exception as e:
        print(f"❌ Test batch creation failed: {e}")
        return
    
    print("\n=== Step 6: Exemplar Encoding ===")
    try:
        with torch.no_grad():
            exemplar_encoding = model.encode_exemplar(test_batch["exemplar"])
            print(f"✅ Exemplar encoding successful: {exemplar_encoding.shape}")
    except Exception as e:
        print(f"❌ Exemplar encoding failed: {e}")
        traceback.print_exc()
        return
    
    print("\n=== Step 7: Training Step ===")
    try:
        loss_dict = model.training_step(test_batch, batch_idx=0)
        print(f"✅ Training step successful: {loss_dict}")
    except Exception as e:
        print(f"❌ Training step failed: {e}")
        traceback.print_exc()
        
        # Enter debugger at failure point
        import pdb; pdb.set_trace()

if __name__ == "__main__":
    debug_step_by_step()
