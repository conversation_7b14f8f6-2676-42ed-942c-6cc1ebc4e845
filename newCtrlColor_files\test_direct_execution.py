#!/usr/bin/env python3
"""
Test script to verify that exemplar files can be run directly
"""

import subprocess
import sys
import os

def test_direct_execution():
    """Test running exemplar files directly"""
    
    # Get the project root directory
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    # Files to test
    test_files = [
        "ldm/modules/losses/contextual_loss.py",
        "ldm/modules/losses/grayscale_loss.py", 
        "ldm/modules/losses/exemplar_loss.py",
        "ldm/modules/encoders/exemplar_encoder.py",
        "cldm/exemplar_cldm.py"
    ]
    
    print("Testing direct execution of exemplar files...")
    print("=" * 60)
    
    results = []
    
    for test_file in test_files:
        file_path = os.path.join(project_root, test_file)
        
        if not os.path.exists(file_path):
            print(f"SKIP: {test_file} (file not found)")
            results.append((test_file, "SKIP", "File not found"))
            continue
            
        print(f"\nTesting: {test_file}")
        print("-" * 40)
        
        try:
            # Run the file directly
            result = subprocess.run(
                [sys.executable, file_path],
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=60  # 60 second timeout
            )
            
            if result.returncode == 0:
                print(f"SUCCESS: {test_file}")
                print("Output:")
                print(result.stdout)
                results.append((test_file, "SUCCESS", result.stdout))
            else:
                print(f"FAILED: {test_file}")
                print("Error:")
                print(result.stderr)
                results.append((test_file, "FAILED", result.stderr))
                
        except subprocess.TimeoutExpired:
            print(f"TIMEOUT: {test_file}")
            results.append((test_file, "TIMEOUT", "Process timed out"))
            
        except Exception as e:
            print(f"ERROR: {test_file} - {e}")
            results.append((test_file, "ERROR", str(e)))
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    success_count = 0
    total_count = len(results)
    
    for file_name, status, details in results:
        status_symbol = {
            "SUCCESS": "✓",
            "FAILED": "✗", 
            "TIMEOUT": "⏱",
            "ERROR": "⚠",
            "SKIP": "⊘"
        }.get(status, "?")
        
        print(f"{status_symbol} {file_name:<40} {status}")
        
        if status == "SUCCESS":
            success_count += 1
    
    print(f"\nResults: {success_count}/{total_count} files executed successfully")
    
    if success_count == total_count:
        print("🎉 All exemplar files can be run directly!")
    else:
        print("⚠️ Some files failed direct execution")
    
    return success_count == total_count

if __name__ == "__main__":
    success = test_direct_execution()
    sys.exit(0 if success else 1)
