#!/usr/bin/env python3
"""
Comprehensive Exemplar-based Colorization Testing Script

This script tests all components of the exemplar-based colorization implementation
to verify that the 4th conditioning mode is working correctly.

Usage: python test_exemplar_complete.py
"""

import os
import sys
import torch
import traceback
from typing import Dict, Any

def test_imports():
    """Test that all required modules can be imported"""
    print("=== Testing Imports ===")
    
    try:
        # Test core imports
        from cldm.exemplar_cldm import ExemplarControlLDM, test_exemplar_pipeline, test_full_exemplar_cldm
        from ldm.modules.encoders.exemplar_encoder import CLIPExemplarEncoder, ExemplarTextFusion
        from ldm.modules.losses.exemplar_loss import ExemplarLoss
        from ldm.modules.losses.contextual_loss import VGG19ContextualLoss
        from ldm.modules.losses.grayscale_loss import GrayscaleConsistencyLoss
        
        print("✅ All core imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_loss_functions():
    """Test individual loss function components"""
    print("\n=== Testing Loss Functions ===")
    
    try:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Testing on device: {device}")
        
        # Test data
        batch_size = 2
        generated = torch.rand(batch_size, 3, 256, 256).to(device)
        exemplar = torch.rand(batch_size, 3, 256, 256).to(device)
        input_image = torch.rand(batch_size, 3, 256, 256).to(device)
        
        # Test contextual loss
        print("Testing VGG19 contextual loss...")
        from ldm.modules.losses.contextual_loss import VGG19ContextualLoss
        contextual_loss = VGG19ContextualLoss().to(device)
        
        with torch.no_grad():
            ctx_loss = contextual_loss(exemplar, generated)
        print(f"Contextual loss: {ctx_loss.item():.6f}")
        
        # Test grayscale loss
        print("Testing grayscale consistency loss...")
        from ldm.modules.losses.grayscale_loss import GrayscaleConsistencyLoss
        gray_loss = GrayscaleConsistencyLoss().to(device)
        
        with torch.no_grad():
            g_loss = gray_loss(input_image, generated)
        print(f"Grayscale loss: {g_loss.item():.6f}")
        
        # Test combined exemplar loss
        print("Testing combined exemplar loss...")
        from ldm.modules.losses.exemplar_loss import ExemplarLoss
        exemplar_loss = ExemplarLoss().to(device)
        
        with torch.no_grad():
            loss_components = exemplar_loss(
                generated, exemplar, input_image, return_components=True
            )
        
        print(f"Total exemplar loss: {loss_components['total_loss'].item():.6f}")
        print(f"Contextual component: {loss_components['contextual_loss'].item():.6f}")
        print(f"Grayscale component: {loss_components['grayscale_loss'].item():.6f}")
        
        print("✅ All loss functions working")
        return True
        
    except Exception as e:
        print(f"❌ Loss function test failed: {e}")
        traceback.print_exc()
        return False

def test_clip_encoder():
    """Test CLIP exemplar encoder"""
    print("\n=== Testing CLIP Exemplar Encoder ===")
    
    try:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Test data
        batch_size = 2
        exemplar_image = torch.rand(batch_size, 3, 256, 256).to(device)
        
        # Test encoder
        from ldm.modules.encoders.exemplar_encoder import CLIPExemplarEncoder
        encoder = CLIPExemplarEncoder().to(device)
        
        with torch.no_grad():
            encoding_result = encoder.encode_exemplar(exemplar_image)
        
        print(f"Features shape: {encoding_result['features'].shape}")
        print(f"Pooled features shape: {encoding_result['pooled_features'].shape}")
        print(f"Color palette shape: {encoding_result['color_palette'].shape}")
        
        # Test fusion
        print("Testing exemplar-text fusion...")
        from ldm.modules.encoders.exemplar_encoder import ExemplarTextFusion
        text_features = torch.rand(batch_size, 77, 768).to(device)
        fusion = ExemplarTextFusion().to(device)
        
        with torch.no_grad():
            fused_features = fusion(text_features, encoding_result["features"])
        
        print(f"Fused features shape: {fused_features.shape}")
        print("✅ CLIP encoder working")
        return True
        
    except Exception as e:
        print(f"❌ CLIP encoder test failed: {e}")
        traceback.print_exc()
        return False

def test_exemplar_pipeline():
    """Test the complete exemplar pipeline"""
    print("\n=== Testing Complete Exemplar Pipeline ===")
    
    try:
        from cldm.exemplar_cldm import test_exemplar_pipeline
        success = test_exemplar_pipeline()
        
        if success:
            print("✅ Exemplar pipeline working")
        else:
            print("❌ Exemplar pipeline failed")
        
        return success
        
    except Exception as e:
        print(f"❌ Exemplar pipeline test failed: {e}")
        traceback.print_exc()
        return False

def test_config_loading():
    """Test that exemplar config can be loaded"""
    print("\n=== Testing Config Loading ===")
    
    try:
        import yaml
        config_path = "./models/exemplar_cldm_v15.yaml"
        
        if not os.path.exists(config_path):
            print(f"❌ Config file not found: {config_path}")
            return False
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        print(f"✅ Config loaded from: {config_path}")
        print(f"Model target: {config['model']['target']}")
        
        # Check exemplar-specific parameters
        params = config['model']['params']
        if 'exemplar_encoder_config' in params:
            print("✅ Exemplar encoder config found")
        if 'exemplar_loss_config' in params:
            print("✅ Exemplar loss config found")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 CtrlColor Exemplar-based Colorization - Complete Test Suite")
    print("=" * 70)
    
    results = {}
    
    # Run all tests
    results['imports'] = test_imports()
    results['loss_functions'] = test_loss_functions()
    results['clip_encoder'] = test_clip_encoder()
    results['exemplar_pipeline'] = test_exemplar_pipeline()
    results['config_loading'] = test_config_loading()
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 TEST SUMMARY")
    print("=" * 70)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! Exemplar-based colorization is ready!")
        print("\nNext steps:")
        print("1. Test with real images using the UI")
        print("2. Run full model initialization test")
        print("3. Integrate with main colorization interface")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
        print("Focus on fixing the failed components first.")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
