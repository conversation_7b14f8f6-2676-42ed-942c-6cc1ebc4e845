#!/usr/bin/env python3
"""
Test script for exemplar-based colorization components
Tests individual components before full integration
"""

import torch
import sys
import os

def test_basic_imports():
    """Test that all exemplar components can be imported"""
    print("=== Testing Basic Imports ===")
    
    try:
        from ldm.modules.encoders.exemplar_encoder import CLIPExemplarEncoder, ExemplarTextFusion
        print("✅ Exemplar encoder imports successful")
        
        from ldm.modules.losses.exemplar_loss import ExemplarLoss
        print("✅ Exemplar loss imports successful")
        
        from ldm.modules.losses.contextual_loss import VGG19ContextualLoss
        print("✅ Contextual loss imports successful")
        
        from ldm.modules.losses.grayscale_loss import Gray<PERSON>le<PERSON>onsistency<PERSON>oss
        print("✅ Grayscale loss imports successful")
        
        from cldm.exemplar_cldm import ExemplarControlLDM
        print("✅ ExemplarControlLDM imports successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_device_setup():
    """Test device setup and memory"""
    print("\n=== Testing Device Setup ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Device: {device}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        print(f"Current usage: {torch.cuda.memory_allocated() / 1e9:.3f} GB")
    
    return device

def test_exemplar_encoder_basic():
    """Test basic exemplar encoder functionality"""
    print("\n=== Testing Exemplar Encoder (Basic) ===")
    
    try:
        from ldm.modules.encoders.exemplar_encoder import CLIPExemplarEncoder
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        encoder = CLIPExemplarEncoder().to(device)
        
        # Test with small batch to avoid memory issues
        batch_size = 1
        test_image = torch.rand(batch_size, 3, 256, 256).to(device)
        
        with torch.no_grad():
            result = encoder.encode_exemplar(test_image)
        
        print(f"✅ Features shape: {result['features'].shape}")
        print(f"✅ Pooled features shape: {result['pooled_features'].shape}")
        print(f"✅ Color palette shape: {result['color_palette'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Exemplar encoder test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_loss_functions_basic():
    """Test basic loss function functionality"""
    print("\n=== Testing Loss Functions (Basic) ===")
    
    try:
        from ldm.modules.losses.exemplar_loss import ExemplarLoss
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        loss_fn = ExemplarLoss().to(device)
        
        # Test with small tensors
        batch_size = 1
        generated = torch.rand(batch_size, 3, 128, 128).to(device)
        exemplar = torch.rand(batch_size, 3, 128, 128).to(device)
        input_img = torch.rand(batch_size, 3, 128, 128).to(device)
        
        with torch.no_grad():
            loss_components = loss_fn(generated, exemplar, input_img, return_components=True)
        
        print(f"✅ Total loss: {loss_components['total_loss'].item():.6f}")
        print(f"✅ Contextual loss: {loss_components['contextual_loss'].item():.6f}")
        print(f"✅ Grayscale loss: {loss_components['grayscale_loss'].item():.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Loss function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all basic component tests"""
    print("CtrlColor Exemplar Components - Basic Test Suite")
    print("=" * 60)
    
    results = []
    
    # Test 1: Basic imports
    results.append(test_basic_imports())
    
    # Test 2: Device setup
    device = test_device_setup()
    
    # Test 3: Exemplar encoder
    results.append(test_exemplar_encoder_basic())
    
    # Test 4: Loss functions
    results.append(test_loss_functions_basic())
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY:")
    print(f"Import test: {'PASSED' if results[0] else 'FAILED'}")
    print(f"Encoder test: {'PASSED' if results[1] else 'FAILED'}")
    print(f"Loss test: {'PASSED' if results[2] else 'FAILED'}")
    
    all_passed = all(results)
    print(f"\nOverall: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 Exemplar components are working! Ready for full integration testing.")
    else:
        print("\n⚠️  Some components need fixing before proceeding.")
    
    return all_passed

if __name__ == "__main__":
    main()
