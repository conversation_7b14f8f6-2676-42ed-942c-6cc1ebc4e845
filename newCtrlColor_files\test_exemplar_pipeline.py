#!/usr/bin/env python3
"""
Test script for CtrlColor Exemplar-based Colorization Pipeline

This script tests the complete exemplar pipeline including:
1. CLIP exemplar encoder
2. VGG19 contextual loss
3. Grayscale consistency loss
4. Combined exemplar loss
5. ExemplarControlLDM integration

Usage:
    python test_exemplar_pipeline.py
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_exemplar_encoder():
    """Test the CLIP exemplar encoder"""
    print("🧪 Testing CLIP Exemplar Encoder...")
    
    try:
        from ldm.modules.encoders.exemplar_encoder import CLIPExemplarEncoder, ExemplarTextFusion
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {device}")
        
        # Create test data
        batch_size = 2
        exemplar_image = torch.rand(batch_size, 3, 256, 256).to(device)
        
        # Test exemplar encoder
        encoder = CLIPExemplarEncoder().to(device)
        
        with torch.no_grad():
            encoding_result = encoder.encode_exemplar(exemplar_image)
        
        print(f"✅ Features shape: {encoding_result['features'].shape}")
        print(f"✅ Pooled features shape: {encoding_result['pooled_features'].shape}")
        print(f"✅ Sequence features shape: {encoding_result['sequence_features'].shape}")
        
        if encoding_result["color_palette"] is not None:
            print(f"✅ Color palette shape: {encoding_result['color_palette'].shape}")
        
        # Test fusion module
        text_features = torch.rand(batch_size, 77, 768).to(device)
        exemplar_features = encoding_result["features"]
        
        fusion = ExemplarTextFusion().to(device)
        fused_features = fusion(text_features, exemplar_features)
        
        print(f"✅ Fused features shape: {fused_features.shape}")
        print("✅ CLIP Exemplar Encoder test passed!")
        
        return True
        
    except Exception as e:
        print(f"❌ CLIP Exemplar Encoder test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_loss_functions():
    """Test the exemplar loss functions"""
    print("\n🧪 Testing Exemplar Loss Functions...")
    
    try:
        from ldm.modules.losses.contextual_loss import VGG19ContextualLoss
        from ldm.modules.losses.grayscale_loss import GrayscaleConsistencyLoss
        from ldm.modules.losses.exemplar_loss import ExemplarLoss
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Create test data
        batch_size = 2
        generated = torch.rand(batch_size, 3, 256, 256).to(device)
        exemplar = torch.rand(batch_size, 3, 256, 256).to(device)
        input_image = torch.rand(batch_size, 3, 256, 256).to(device)
        
        # Test contextual loss
        print("Testing VGG19 Contextual Loss...")
        contextual_loss = VGG19ContextualLoss().to(device)
        
        with torch.no_grad():
            ctx_loss = contextual_loss(exemplar, generated)
        print(f"✅ Contextual loss: {ctx_loss.item():.6f}")
        
        # Test grayscale loss
        print("Testing Grayscale Consistency Loss...")
        grayscale_loss = GrayscaleConsistencyLoss().to(device)
        
        with torch.no_grad():
            gray_loss = grayscale_loss(input_image, generated)
        print(f"✅ Grayscale loss: {gray_loss.item():.6f}")
        
        # Test combined exemplar loss
        print("Testing Combined Exemplar Loss...")
        exemplar_loss = ExemplarLoss().to(device)
        
        with torch.no_grad():
            loss_components = exemplar_loss(
                generated, exemplar, input_image, return_components=True
            )
        
        print(f"✅ Total exemplar loss: {loss_components['total_loss'].item():.6f}")
        print(f"✅ Contextual component: {loss_components['contextual_loss'].item():.6f}")
        print(f"✅ Grayscale component: {loss_components['grayscale_loss'].item():.6f}")
        print("✅ Exemplar Loss Functions test passed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Exemplar Loss Functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_exemplar_cldm():
    """Test the ExemplarControlLDM integration"""
    print("\n🧪 Testing ExemplarControlLDM...")
    
    try:
        from cldm.exemplar_cldm import ExemplarControlLDM
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Create test data
        batch_size = 2
        test_batch = {
            "image": torch.rand(batch_size, 3, 512, 512).to(device),
            "txt": ["red car", "blue sky"],
            "hint": torch.rand(batch_size, 3, 512, 512).to(device),
            "exemplar": torch.rand(batch_size, 3, 512, 512).to(device),
        }
        
        # Test exemplar encoding
        print("Testing exemplar encoding...")
        exemplar_cldm = ExemplarControlLDM(
            control_stage_config=None,  # Simplified for testing
            control_key="hint",
            only_mid_control=False,
        ).to(device)
        
        exemplar_encoding = exemplar_cldm.encode_exemplar(test_batch["exemplar"])
        print(f"✅ Exemplar features shape: {exemplar_encoding['features'].shape}")
        
        # Test exemplar conditioning
        text_cond = torch.rand(batch_size, 77, 768).to(device)  # Mock text conditioning
        exemplar_cond = exemplar_cldm.get_exemplar_conditioning(
            test_batch["exemplar"], text_cond
        )
        print(f"✅ Exemplar conditioning shape: {exemplar_cond.shape}")
        
        print("✅ ExemplarControlLDM test passed!")
        
        return True
        
    except Exception as e:
        print(f"❌ ExemplarControlLDM test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_pipeline():
    """Test the complete exemplar pipeline end-to-end"""
    print("\n🧪 Testing End-to-End Exemplar Pipeline...")
    
    try:
        # Create sample images
        print("Creating test images...")
        
        # Create a simple test image (grayscale with some patterns)
        input_img = np.zeros((256, 256, 3), dtype=np.uint8)
        input_img[50:150, 50:150] = [128, 128, 128]  # Gray square
        input_img[100:200, 100:200] = [200, 200, 200]  # Lighter gray square
        
        # Create an exemplar image (colorful)
        exemplar_img = np.zeros((256, 256, 3), dtype=np.uint8)
        exemplar_img[50:150, 50:150] = [255, 0, 0]  # Red square
        exemplar_img[100:200, 100:200] = [0, 255, 0]  # Green square
        
        # Save test images
        os.makedirs("logs", exist_ok=True)
        cv2.imwrite("logs/test_input.png", cv2.cvtColor(input_img, cv2.COLOR_RGB2BGR))
        cv2.imwrite("logs/test_exemplar.png", cv2.cvtColor(exemplar_img, cv2.COLOR_RGB2BGR))
        
        print("✅ Test images created and saved to logs/")
        
        # Test the complete pipeline components
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Convert to tensors
        input_tensor = torch.from_numpy(input_img).float().permute(2, 0, 1).unsqueeze(0).to(device) / 255.0
        exemplar_tensor = torch.from_numpy(exemplar_img).float().permute(2, 0, 1).unsqueeze(0).to(device) / 255.0
        
        # Test exemplar encoding
        from ldm.modules.encoders.exemplar_encoder import CLIPExemplarEncoder
        encoder = CLIPExemplarEncoder().to(device)
        
        with torch.no_grad():
            exemplar_features = encoder.encode_exemplar(exemplar_tensor)
        
        print(f"✅ Exemplar encoded: {exemplar_features['features'].shape}")
        
        # Test loss computation
        from ldm.modules.losses.exemplar_loss import ExemplarLoss
        loss_fn = ExemplarLoss().to(device)
        
        # Create a mock generated image (slightly different from input)
        generated_tensor = input_tensor + 0.1 * torch.randn_like(input_tensor)
        generated_tensor = torch.clamp(generated_tensor, 0, 1)
        
        with torch.no_grad():
            loss_components = loss_fn(
                generated_tensor, exemplar_tensor, input_tensor, return_components=True
            )
        
        print(f"✅ Loss computed - Total: {loss_components['total_loss'].item():.6f}")
        print(f"   - Contextual: {loss_components['contextual_loss'].item():.6f}")
        print(f"   - Grayscale: {loss_components['grayscale_loss'].item():.6f}")
        
        print("✅ End-to-End Pipeline test passed!")
        
        return True
        
    except Exception as e:
        print(f"❌ End-to-End Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all exemplar pipeline tests"""
    print("🚀 Starting CtrlColor Exemplar Pipeline Tests")
    print("=" * 60)
    
    tests = [
        ("CLIP Exemplar Encoder", test_exemplar_encoder),
        ("Loss Functions", test_loss_functions),
        ("ExemplarControlLDM", test_exemplar_cldm),
        ("End-to-End Pipeline", test_end_to_end_pipeline),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All exemplar pipeline tests passed!")
        print("✅ Exemplar-based colorization is ready for use!")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
