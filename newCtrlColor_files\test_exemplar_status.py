#!/usr/bin/env python3
"""
Test current exemplar implementation status
"""

import torch
import sys
import traceback

def test_component_imports():
    """Test if all exemplar components can be imported"""
    print("=== Testing Component Imports ===")
    
    try:
        from ldm.modules.encoders.exemplar_encoder import CLIPExemplarEncoder, ExemplarTextFusion
        print("✅ Exemplar encoder imports successful")
    except Exception as e:
        print(f"❌ Exemplar encoder import failed: {e}")
        return False
    
    try:
        from ldm.modules.losses.exemplar_loss import ExemplarLoss
        print("✅ Exemplar loss imports successful")
    except Exception as e:
        print(f"❌ Exemplar loss import failed: {e}")
        return False
    
    try:
        from ldm.modules.losses.contextual_loss import VGG19ContextualLoss
        print("✅ Contextual loss imports successful")
    except Exception as e:
        print(f"❌ Contextual loss import failed: {e}")
        return False
    
    try:
        from cldm.exemplar_cldm import ExemplarControlLDM
        print("✅ ExemplarControlLDM imports successful")
    except Exception as e:
        print(f"❌ ExemplarControlLDM import failed: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality of exemplar components"""
    print("\n=== Testing Basic Functionality ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Testing on device: {device}")
    
    try:
        # Test CLIP encoder
        from ldm.modules.encoders.exemplar_encoder import CLIPExemplarEncoder
        encoder = CLIPExemplarEncoder().to(device)
        
        # Test with small image
        test_image = torch.rand(1, 3, 224, 224).to(device)
        with torch.no_grad():
            result = encoder.encode_exemplar(test_image)
        
        print(f"✅ CLIP encoder working - features shape: {result['features'].shape}")
        
    except Exception as e:
        print(f"❌ CLIP encoder test failed: {e}")
        traceback.print_exc()
        return False
    
    try:
        # Test loss functions
        from ldm.modules.losses.exemplar_loss import ExemplarLoss
        loss_fn = ExemplarLoss().to(device)
        
        # Test with small images
        generated = torch.rand(1, 3, 128, 128).to(device)
        exemplar = torch.rand(1, 3, 128, 128).to(device)
        input_img = torch.rand(1, 3, 128, 128).to(device)
        
        with torch.no_grad():
            loss_result = loss_fn(generated, exemplar, input_img, return_components=True)
        
        print(f"✅ Exemplar loss working - total loss: {loss_result['total_loss'].item():.4f}")
        
    except Exception as e:
        print(f"❌ Exemplar loss test failed: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_model_config():
    """Test if model config exists and is loadable"""
    print("\n=== Testing Model Configuration ===")
    
    import os
    import yaml
    
    config_path = "models/exemplar_cldm_v15.yaml"
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        print("Available config files:")
        for root, dirs, files in os.walk("models"):
            for file in files:
                if file.endswith((".yaml", ".yml")):
                    print(f"  {os.path.join(root, file)}")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        print(f"✅ Config loaded successfully from {config_path}")
        
        # Check if exemplar-specific configs are present
        model_params = config.get('model', {}).get('params', {})
        if 'exemplar_encoder_config' in model_params:
            print("✅ Exemplar encoder config found")
        else:
            print("⚠️  Exemplar encoder config not found in model params")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🔍 Testing Current Exemplar Implementation Status")
    print("=" * 60)
    
    # Test imports
    imports_ok = test_component_imports()
    
    # Test basic functionality
    functionality_ok = test_basic_functionality()
    
    # Test config
    config_ok = test_model_config()
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY:")
    print(f"Component imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"Basic functionality: {'✅ PASS' if functionality_ok else '❌ FAIL'}")
    print(f"Model configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    
    overall_status = imports_ok and functionality_ok
    print(f"\n🎯 Overall Status: {'✅ READY TO CONTINUE' if overall_status else '❌ NEEDS FIXES'}")
    
    if overall_status:
        print("\n🚀 NEXT STEPS:")
        print("1. Test full model initialization")
        print("2. Integrate exemplar input into UI")
        print("3. Test end-to-end exemplar colorization")
    
    return overall_status

if __name__ == "__main__":
    main()
