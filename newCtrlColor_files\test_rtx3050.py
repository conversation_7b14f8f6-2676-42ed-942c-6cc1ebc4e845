"""
Comprehensive RTX 3050 Test for CtrlColor

Based on CUDA OOM analysis from command_logs/test_rtx3050_simple.log:
- CUDA out of memory when loading 859.54M parameter model
- 3.35 GiB already allocated out of 4.00 GiB total
- Need CPU-first loading strategy

This single file handles all RTX 3050 optimizations and testing.
"""

import torch
import torch.nn.functional as F
import gc
import os
import sys
import json
from contextlib import contextmanager

# Add paths
sys.path.append('.')
sys.path.append('./cldm')

from cldm.model import create_model, load_state_dict
from cldm.ddim_hacked_sag import DDIMSampler


class RTX3050Manager:
    """Complete RTX 3050 management and optimization"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.max_memory_gb = 3.4  # 85% of 4GB to avoid OOM
        self.gpu_name = None
        self.total_memory_gb = 0
        
        if torch.cuda.is_available():
            self.gpu_name = torch.cuda.get_device_name(0)
            self.total_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        self.setup_optimizations()
    
    def setup_optimizations(self):
        """Apply all RTX 3050 optimizations"""
        if torch.cuda.is_available():
            # Memory management
            torch.cuda.set_per_process_memory_fraction(0.85)
            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
            
            # Performance optimizations
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.enabled = True
            
            # Enable memory efficient attention if available
            try:
                torch.backends.cuda.enable_flash_sdp(True)
            except:
                pass
            
            print("✅ RTX 3050 optimizations applied:")
            print(f"   - GPU: {self.gpu_name}")
            print(f"   - Total VRAM: {self.total_memory_gb:.1f}GB")
            print(f"   - Memory limit: {self.max_memory_gb:.1f}GB (85%)")
            print("   - Memory fragmentation: Reduced")
            print("   - FP16: Enabled")
        else:
            print("⚠️ CUDA not available, using CPU mode")
    
    def get_memory_info(self):
        """Get current GPU memory usage"""
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3
            reserved = torch.cuda.memory_reserved() / 1024**3
            return allocated, reserved
        return 0, 0
    
    def clear_memory(self):
        """Clear GPU cache and run garbage collection"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
    
    @contextmanager
    def memory_efficient_context(self):
        """Context manager for memory-efficient operations"""
        self.clear_memory()
        try:
            yield
        finally:
            self.clear_memory()
    
    def check_memory_available(self, required_gb):
        """Check if enough memory is available"""
        allocated, _ = self.get_memory_info()
        available = self.max_memory_gb - allocated
        return available >= required_gb
    
    def load_model_safe(self, config_path, ckpt_path):
        """
        Safe model loading that addresses the CUDA OOM issue
        
        Strategy:
        1. Load everything on CPU first
        2. Estimate memory requirements
        3. Transfer to GPU only if safe
        4. Use FP16 to reduce memory usage
        """
        print("🔧 Loading model with RTX 3050 safe strategy...")
        
        with self.memory_efficient_context():
            try:
                # Step 1: Create model on CPU
                print("   - Creating model on CPU...")
                model = create_model(config_path).cpu()
                
                # Step 2: Load checkpoint to CPU memory
                print("   - Loading checkpoint to CPU...")
                checkpoint = torch.load(ckpt_path, map_location='cpu')
                
                # Step 3: Load state dict on CPU
                print("   - Loading state dict on CPU...")
                if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                else:
                    state_dict = checkpoint
                
                model.load_state_dict(state_dict, strict=False)
                
                # Clean up checkpoint to free CPU memory
                del checkpoint, state_dict
                self.clear_memory()
                
                # Step 4: Estimate model memory requirements
                param_count = sum(p.numel() for p in model.parameters())
                model_size_gb = param_count * 4 / 1024**3  # FP32 size
                model_size_fp16_gb = param_count * 2 / 1024**3  # FP16 size
                
                print(f"   - Model parameters: {param_count:,}")
                print(f"   - Estimated FP32 size: {model_size_gb:.2f}GB")
                print(f"   - Estimated FP16 size: {model_size_fp16_gb:.2f}GB")
                
                # Step 5: Try GPU transfer
                if torch.cuda.is_available():
                    allocated_before, _ = self.get_memory_info()
                    print(f"   - GPU memory before transfer: {allocated_before:.2f}GB")
                    
                    if self.check_memory_available(model_size_fp16_gb):
                        print("   - Transferring to GPU with FP16...")
                        model = model.cuda().half()
                        
                        allocated_after, _ = self.get_memory_info()
                        print(f"   - GPU memory after transfer: {allocated_after:.2f}GB")
                        print("   ✅ Model loaded on GPU successfully")
                    else:
                        print("   ⚠️ Insufficient GPU memory, keeping on CPU")
                        print("   - Will use CPU inference (slower but functional)")
                
                return model
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"   ❌ CUDA OOM during loading: {e}")
                    print("   - Falling back to CPU...")
                    try:
                        model = model.cpu()
                        return model
                    except:
                        return None
                else:
                    print(f"   ❌ Model loading failed: {e}")
                    return None
            
            except Exception as e:
                print(f"   ❌ Unexpected error: {e}")
                return None
    
    def test_inference(self, model):
        """Test inference with memory monitoring"""
        print("🧪 Testing inference with memory monitoring...")
        
        try:
            with self.memory_efficient_context():
                device = next(model.parameters()).device
                dtype = next(model.parameters()).dtype
                
                # Create minimal test inputs
                batch_size = 1
                height, width = 256, 256  # Reduced size for memory safety
                
                print(f"   - Device: {device}")
                print(f"   - Dtype: {dtype}")
                print(f"   - Test size: {height}x{width}")
                
                # Monitor memory before
                allocated_before, _ = self.get_memory_info()
                print(f"   - Memory before: {allocated_before:.2f}GB")
                
                # Create test inputs
                hint = torch.randn(batch_size, 5, height, width, device=device, dtype=dtype)
                
                # Test forward pass
                with torch.no_grad():
                    if hasattr(model, 'apply_model'):
                        # ControlLDM interface
                        timesteps = torch.randint(0, 1000, (batch_size,), device=device)
                        noise = torch.randn(batch_size, 4, height//8, width//8, device=device, dtype=dtype)
                        
                        cond = {
                            "c_concat": [hint],
                            "c_crossattn": [["test prompt"]]
                        }
                        
                        output = model.apply_model(noise, timesteps, cond)
                        print(f"   - Forward pass successful: {output.shape}")
                    else:
                        # Basic forward
                        output = model(hint)
                        print(f"   - Basic forward successful: {output.shape}")
                
                # Monitor memory after
                allocated_after, _ = self.get_memory_info()
                print(f"   - Memory after: {allocated_after:.2f}GB")
                print(f"   - Memory increase: {allocated_after - allocated_before:.2f}GB")
                
                print("   ✅ Inference test successful!")
                return True
                
        except RuntimeError as e:
            if "out of memory" in str(e):
                print(f"   ❌ CUDA OOM during inference: {e}")
                print("   💡 Try reducing image size or using CPU")
            else:
                print(f"   ❌ Inference error: {e}")
            return False
        
        except Exception as e:
            print(f"   ❌ Unexpected inference error: {e}")
            return False
    
    def run_complete_test(self):
        """Run complete RTX 3050 test suite"""
        print("🚀 RTX 3050 Complete Test Suite")
        print("=" * 60)
        
        # Configuration
        config_path = './models/cldm_v15_inpainting_infer1.yaml'
        ckpt_path = './pretrained_models/main_model.ckpt'
        
        # Check files exist
        if not os.path.exists(config_path):
            print(f"❌ Config file not found: {config_path}")
            return False
        
        if not os.path.exists(ckpt_path):
            print(f"❌ Checkpoint file not found: {ckpt_path}")
            return False
        
        # Test 1: Model loading
        print("\n📋 Test 1: Safe Model Loading")
        model = self.load_model_safe(config_path, ckpt_path)
        if model is None:
            print("❌ Model loading failed")
            return False
        
        # Test 2: Inference
        print("\n📋 Test 2: Memory-Safe Inference")
        inference_success = self.test_inference(model)
        
        # Test 3: Memory efficiency
        print("\n📋 Test 3: Memory Efficiency Report")
        allocated, reserved = self.get_memory_info()
        efficiency = (allocated / self.max_memory_gb) * 100 if allocated > 0 else 0
        
        print(f"   - Memory allocated: {allocated:.2f}GB")
        print(f"   - Memory reserved: {reserved:.2f}GB")
        print(f"   - Memory efficiency: {efficiency:.1f}% of limit")
        
        # Final result
        print("\n" + "=" * 60)
        if inference_success:
            print("✅ RTX 3050 test PASSED!")
            print("💡 CtrlColor can run on RTX 3050 with optimizations")
            
            # Save test results
            results = {
                "gpu": self.gpu_name,
                "total_memory_gb": self.total_memory_gb,
                "memory_limit_gb": self.max_memory_gb,
                "memory_allocated_gb": allocated,
                "memory_efficiency_percent": efficiency,
                "model_loading": "success",
                "inference": "success",
                "overall": "passed"
            }
            
            with open('rtx3050_test_results.json', 'w') as f:
                json.dump(results, f, indent=2)
            
            print("📊 Test results saved to rtx3050_test_results.json")
            
        else:
            print("❌ RTX 3050 test FAILED!")
            print("💡 Consider using CPU mode or smaller models")
        
        return inference_success


def main():
    """Main test function"""
    try:
        manager = RTX3050Manager()
        success = manager.run_complete_test()
        return success
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
