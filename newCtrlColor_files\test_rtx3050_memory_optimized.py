"""
RTX 3050 Memory-Optimized CtrlColor Test

Addresses the CUDA OOM issue from test_rtx3050_simple.log by implementing:
- CPU-first model loading with gradual GPU transfer
- Memory-efficient checkpoint loading
- Dynamic memory management
- Fallback strategies for low VRAM

Based on the error analysis from command_logs/test_rtx3050_simple.log
"""

import torch
import torch.nn.functional as F
import gc
import os
import sys
from contextlib import contextmanager

# Add paths
sys.path.append('.')
sys.path.append('./cldm')

from cldm.model import create_model, load_state_dict
from cldm.ddim_hacked_sag import DDIMSampler


class RTX3050MemoryManager:
    """Memory management for RTX 3050 with 4GB VRAM"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.max_memory_gb = 3.4  # 85% of 4GB
        self.current_memory_gb = 0
        
    def get_memory_info(self):
        """Get current GPU memory usage"""
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3
            reserved = torch.cuda.memory_reserved() / 1024**3
            return allocated, reserved
        return 0, 0
    
    def clear_cache(self):
        """Clear GPU cache and run garbage collection"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
    
    @contextmanager
    def memory_efficient_loading(self):
        """Context manager for memory-efficient operations"""
        self.clear_cache()
        try:
            yield
        finally:
            self.clear_cache()
    
    def check_memory_available(self, required_gb):
        """Check if enough memory is available"""
        allocated, _ = self.get_memory_info()
        available = self.max_memory_gb - allocated
        return available >= required_gb


def load_model_cpu_first(config_path, ckpt_path, memory_manager):
    """
    Load model on CPU first, then transfer to GPU gradually
    
    This addresses the CUDA OOM error from the log by:
    1. Loading checkpoint to CPU memory first
    2. Creating model on CPU
    3. Loading state dict on CPU
    4. Transferring to GPU in chunks if possible
    """
    print("🔧 Loading model with CPU-first strategy...")
    
    with memory_manager.memory_efficient_loading():
        # Step 1: Create model on CPU
        print("   - Creating model on CPU...")
        model = create_model(config_path).cpu()
        
        # Step 2: Load checkpoint to CPU
        print("   - Loading checkpoint to CPU...")
        try:
            # Load checkpoint with CPU mapping to avoid GPU allocation
            checkpoint = torch.load(ckpt_path, map_location='cpu')
            state_dict = checkpoint if isinstance(checkpoint, dict) else checkpoint.state_dict()
            
            # Clean up checkpoint reference
            del checkpoint
            gc.collect()
            
        except Exception as e:
            print(f"   ❌ Failed to load checkpoint: {e}")
            return None
        
        # Step 3: Load state dict on CPU
        print("   - Loading state dict on CPU...")
        try:
            model.load_state_dict(state_dict, strict=False)
            del state_dict
            gc.collect()
            
        except Exception as e:
            print(f"   ❌ Failed to load state dict: {e}")
            return None
        
        # Step 4: Try to move to GPU
        print("   - Attempting GPU transfer...")
        try:
            # Check available memory
            allocated, reserved = memory_manager.get_memory_info()
            print(f"   - GPU memory before transfer: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved")
            
            # Estimate model size (rough calculation)
            param_count = sum(p.numel() for p in model.parameters())
            model_size_gb = param_count * 4 / 1024**3  # 4 bytes per float32 parameter
            print(f"   - Estimated model size: {model_size_gb:.2f}GB")
            
            if memory_manager.check_memory_available(model_size_gb):
                print("   - Sufficient memory available, transferring to GPU...")
                model = model.cuda()
                
                # Enable memory optimizations
                model.half()  # Use FP16
                print("   - FP16 conversion applied")
                
            else:
                print("   ⚠️ Insufficient GPU memory, keeping model on CPU")
                print("   - Will use CPU inference (slower but functional)")
                
        except RuntimeError as e:
            if "out of memory" in str(e):
                print("   ⚠️ GPU OOM during transfer, falling back to CPU")
                model = model.cpu()
            else:
                raise e
    
    allocated, reserved = memory_manager.get_memory_info()
    print(f"   ✅ Model loaded. GPU memory: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved")
    
    return model


def create_optimized_sampler(model, memory_manager):
    """Create memory-optimized DDIM sampler"""
    print("🔧 Creating optimized sampler...")
    
    try:
        # Use fewer steps for memory efficiency
        sampler = DDIMSampler(model)
        
        # Configure for low memory
        sampler.model.model.diffusion_model.use_checkpoint = True  # Gradient checkpointing
        
        print("   ✅ Sampler created with memory optimizations")
        return sampler
        
    except Exception as e:
        print(f"   ❌ Failed to create sampler: {e}")
        return None


def test_inference_memory_safe(model, sampler, memory_manager):
    """Test inference with memory safety"""
    print("🧪 Testing memory-safe inference...")
    
    try:
        with memory_manager.memory_efficient_loading():
            # Create minimal test inputs
            batch_size = 1
            height, width = 256, 256  # Smaller size for memory efficiency
            
            # Create dummy inputs
            device = next(model.parameters()).device
            
            if device.type == 'cuda':
                # GPU inference
                print("   - Running GPU inference...")
                hint = torch.randn(batch_size, 5, height, width, device=device, dtype=torch.float16)
                prompt = ["a simple test image"]
                
            else:
                # CPU inference
                print("   - Running CPU inference...")
                hint = torch.randn(batch_size, 5, height, width, device=device, dtype=torch.float32)
                prompt = ["a simple test image"]
            
            # Monitor memory before inference
            allocated_before, _ = memory_manager.get_memory_info()
            print(f"   - Memory before inference: {allocated_before:.2f}GB")
            
            # Run minimal inference test
            with torch.no_grad():
                # Simplified inference without full pipeline
                timesteps = torch.randint(0, 1000, (batch_size,), device=device)
                
                # Test model forward pass
                if hasattr(model, 'apply_model'):
                    # Use ControlLDM interface
                    noise = torch.randn(batch_size, 4, height//8, width//8, device=device)
                    if device.type == 'cuda':
                        noise = noise.half()
                    
                    # Minimal conditioning
                    cond = {"c_concat": [hint], "c_crossattn": [prompt]}
                    
                    # Forward pass
                    output = model.apply_model(noise, timesteps, cond)
                    print(f"   - Forward pass successful. Output shape: {output.shape}")
                    
                else:
                    print("   - Model doesn't have apply_model method, testing basic forward")
                    output = model(hint, timesteps)
                    print(f"   - Basic forward successful. Output shape: {output.shape}")
            
            # Monitor memory after inference
            allocated_after, _ = memory_manager.get_memory_info()
            print(f"   - Memory after inference: {allocated_after:.2f}GB")
            print(f"   - Memory increase: {allocated_after - allocated_before:.2f}GB")
            
            print("   ✅ Inference test successful!")
            return True
            
    except RuntimeError as e:
        if "out of memory" in str(e):
            print(f"   ❌ CUDA OOM during inference: {e}")
            print("   💡 Try reducing image size or using CPU inference")
        else:
            print(f"   ❌ Inference error: {e}")
        return False
    
    except Exception as e:
        print(f"   ❌ Unexpected error during inference: {e}")
        return False


def main():
    """Main RTX 3050 optimized test"""
    print("🚀 RTX 3050 Memory-Optimized CtrlColor Test")
    print("=" * 60)
    
    # Initialize memory manager
    memory_manager = RTX3050MemoryManager()
    
    # Check initial GPU state
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"🎯 GPU: {gpu_name}")
        print(f"🎯 Total VRAM: {total_memory:.1f}GB")
        
        allocated, reserved = memory_manager.get_memory_info()
        print(f"🎯 Initial memory: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved")
    else:
        print("⚠️ CUDA not available, will use CPU")
    
    print()
    
    # Configuration
    config_path = './models/cldm_v15_inpainting_infer1.yaml'
    ckpt_path = './pretrained_models/main_model.ckpt'
    
    # Check if files exist
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    if not os.path.exists(ckpt_path):
        print(f"❌ Checkpoint file not found: {ckpt_path}")
        return False
    
    # Load model with memory optimization
    model = load_model_cpu_first(config_path, ckpt_path, memory_manager)
    if model is None:
        print("❌ Failed to load model")
        return False
    
    # Create sampler
    sampler = create_optimized_sampler(model, memory_manager)
    if sampler is None:
        print("❌ Failed to create sampler")
        return False
    
    # Test inference
    success = test_inference_memory_safe(model, sampler, memory_manager)
    
    # Final memory report
    print("\n" + "=" * 60)
    if torch.cuda.is_available():
        allocated, reserved = memory_manager.get_memory_info()
        print(f"🎯 Final GPU memory: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved")
        print(f"🎯 Memory efficiency: {(allocated/memory_manager.max_memory_gb)*100:.1f}% of limit used")
    
    if success:
        print("✅ RTX 3050 optimization test PASSED!")
        print("💡 Model can run on RTX 3050 with memory optimizations")
    else:
        print("❌ RTX 3050 optimization test FAILED!")
        print("💡 Consider using CPU inference or smaller models")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
