"""
Simple RTX 3050 Optimized Test Script

This is the original test.py with minimal RTX 3050 optimizations added.
Just run this instead of test.py for RTX 3050 optimization.

Key changes:
- Import RTX 3050 optimizations
- FP16 mixed precision
- Memory management
- Optimal batch sizes
"""

# RTX 3050 optimizations (add this line to original)
from rtx3050_config import *

# Original imports (unchanged)
import os
from share import *
import config

import cv2
import einops
import gradio as gr
import numpy as np
import torch
import random

from pytorch_lightning import seed_everything
from annotator.util import resize_image
from cldm.model import create_model, load_state_dict
from cldm.ddim_haced_sag_step import DDIMSampler
from lavis.models import load_model_and_preprocess
from PIL import Image
import tqdm

from ldm.models.autoencoder_train import AutoencoderKL

# Original model loading (with RTX 3050 optimizations)
ckpt_path = "./pretrained_models/main_model.ckpt"

print("🎯 Loading model with RTX 3050 optimizations...")
model = create_model('./models/cldm_v15_inpainting_infer1.yaml').cpu()
model.load_state_dict(load_state_dict(ckpt_path, location='cuda'), strict=False)
model = model.cuda()

# Enable FP16 for RTX 3050
if torch.cuda.is_available():
    model = model.half()
    print("✅ Model converted to FP16")

ddim_sampler = DDIMSampler(model)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
BLIP_model, vis_processors, _ = load_model_and_preprocess(name="blip_caption", model_type="base_coco", is_eval=True, device=device)

vae_model_ckpt_path = "./pretrained_models/content-guided_deformable_vae.ckpt"

def load_vae():
    init_config = {
        "embed_dim": 4,
        "monitor": "val/rec_loss",
        "ddconfig": {
            "double_z": True,
            "z_channels": 4,
            "resolution": 256,
            "in_channels": 3,
            "out_ch": 3,
            "ch": 128,
            "ch_mult": [1, 2, 4, 4],
            "num_res_blocks": 2,
            "attn_resolutions": [],
            "dropout": 0.0,
        },
        "lossconfig": {
            "target": "ldm.modules.losses.LPIPSWithDiscriminator",
            "params": {
                "disc_start": 501,
                "kl_weight": 0,
                "disc_weight": 0.025,
                "disc_factor": 1.0
            }
        }
    }
    vae = AutoencoderKL(**init_config)
    vae.load_state_dict(load_state_dict(vae_model_ckpt_path, location='cuda'))
    vae = vae.cuda()

    # Enable FP16 for VAE too
    if torch.cuda.is_available():
        vae = vae.half()

    return vae

vae_model = load_vae()

# All original functions preserved (encode_mask, get_mask, etc.)
def encode_mask(mask, masked_image):
    mask = torch.nn.functional.interpolate(mask, size=(mask.shape[2] // 8, mask.shape[3] // 8))
    mask = mask.to(device="cuda")

    # Use FP16 autocast for RTX 3050
    with torch.cuda.amp.autocast(enabled=True):
        masked_image_latents = model.get_first_stage_encoding(model.encode_first_stage(masked_image.cuda())).detach()

    return mask, masked_image_latents

def get_mask(input_image, hint_image):
    mask = input_image.copy()
    H, W, C = input_image.shape
    for i in range(H):
        for j in range(W):
            if input_image[i, j, 0] == hint_image[i, j, 0]:
                mask[i, j, :] = 255.
            else:
                mask[i, j, :] = 0.
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    mask = cv2.morphologyEx(np.array(mask), cv2.MORPH_OPEN, kernel, iterations=1)
    return mask

def prepare_mask_and_masked_image(image, mask):
    # [Original function preserved - too long to include here]
    # This function remains exactly the same as original
    if isinstance(image, torch.Tensor):
        if not isinstance(mask, torch.Tensor):
            raise TypeError(f"`image` is a torch.Tensor but `mask` (type: {type(mask)} is not")
        if image.ndim == 3:
            assert image.shape[0] == 3, "Image outside a batch should be of shape (3, H, W)"
            image = image.unsqueeze(0)
        if mask.ndim == 2:
            mask = mask.unsqueeze(0).unsqueeze(0)
        if mask.ndim == 3:
            if mask.shape[0] == 1:
                mask = mask.unsqueeze(0)
            else:
                mask = mask.unsqueeze(1)
        assert image.ndim == 4 and mask.ndim == 4, "Image and Mask must have 4 dimensions"
        assert image.shape[-2:] == mask.shape[-2:], "Image and Mask must have the same spatial dimensions"
        assert image.shape[0] == mask.shape[0], "Image and Mask must have the same batch size"
        if image.min() < -1 or image.max() > 1:
            raise ValueError("Image should be in [-1, 1] range")
        if mask.min() < 0 or mask.max() > 1:
            raise ValueError("Mask should be in [0, 1] range")
        mask[mask < 0.5] = 0
        mask[mask >= 0.5] = 1
        image = image.to(dtype=torch.float32)
    elif isinstance(mask, torch.Tensor):
        raise TypeError(f"`mask` is a torch.Tensor but `image` (type: {type(image)} is not")
    else:
        if isinstance(image, (Image.Image, np.ndarray)):
            image = [image]
        if isinstance(image, list) and isinstance(image[0], Image.Image):
            image = [np.array(i.convert("RGB"))[None, :] for i in image]
            image = np.concatenate(image, axis=0)
        elif isinstance(image, list) and isinstance(image[0], np.ndarray):
            image = np.concatenate([i[None, :] for i in image], axis=0)
        image = image.transpose(0, 3, 1, 2)
        image = torch.from_numpy(image).to(dtype=torch.float32) / 127.5 - 1.0
        if isinstance(mask, (Image.Image, np.ndarray)):
            mask = [mask]
        if isinstance(mask, list) and isinstance(mask[0], Image.Image):
            mask = np.concatenate([np.array(m.convert("L"))[None, None, :] for m in mask], axis=0)
            mask = mask.astype(np.float32) / 255.0
        elif isinstance(mask, list) and isinstance(mask[0], np.ndarray):
            mask = np.concatenate([m[None, None, :] for m in mask], axis=0)
        mask[mask < 0.5] = 0
        mask[mask >= 0.5] = 1
        mask = torch.from_numpy(mask)
    masked_image = image * (mask < 0.5)
    return mask, masked_image

def is_gray_scale(img, threshold=10):
    img = Image.fromarray(img)
    if len(img.getbands()) == 1:
        return True
    img1 = np.asarray(img.getchannel(channel=0), dtype=np.int16)
    img2 = np.asarray(img.getchannel(channel=1), dtype=np.int16)
    img3 = np.asarray(img.getchannel(channel=2), dtype=np.int16)
    diff1 = (img1 - img2).var()
    diff2 = (img2 - img3).var()
    diff3 = (img3 - img1).var()
    diff_sum = (diff1 + diff2 + diff3) / 3.0
    if diff_sum <= threshold:
        return True
    else:
        return False

# Original process function with RTX 3050 optimizations
def process(using_deformable_vae, change_according_to_strokes, iterative_editing, input_image, hint_image, prompt, a_prompt, n_prompt, num_samples, image_resolution, ddim_steps, guess_mode, strength, scale, sag_scale, SAG_influence_step, seed, eta):

    # RTX 3050 optimization: Clear cache and limit batch size
    clear_memory()
    num_samples = min(num_samples, 1)  # Limit to 1 for RTX 3050
    image_resolution = min(image_resolution, 512)  # Conservative resolution

    print(f"🎯 RTX 3050 optimized settings: samples={num_samples}, resolution={image_resolution}")

    torch.cuda.empty_cache()
    with torch.no_grad():
        # Use FP16 autocast for entire process
        with torch.cuda.amp.autocast(enabled=True):
            ref_flag = True
            input_image_ori = input_image
            if is_gray_scale(input_image):
                print("It is a greyscale image.")
            else:
                print("It is a color image.")
                input_image_ori = input_image
                input_image = cv2.cvtColor(input_image, cv2.COLOR_RGB2LAB)[:, :, 0]
                input_image = cv2.merge([input_image, input_image, input_image])

            mask = get_mask(input_image_ori, hint_image)
            cv2.imwrite("gradio_mask1.png", mask)

            if iterative_editing:
                mask = 255 - mask
                if change_according_to_strokes:
                    kernel = np.ones((15, 15), np.uint8)
                    mask = cv2.morphologyEx(mask, cv2.MORPH_GRADIENT, kernel)
                    hint_image = mask/255.*hint_image+(1-mask/255.)*hint_image
                else:
                    hint_image = mask/255.*input_image+(1-mask/255.)*input_image_ori
            else:
                hint_image = mask/255.*input_image+(1-mask/255.)*hint_image

            hint_image = hint_image.astype(np.uint8)

            if len(prompt) == 0:
                image = Image.fromarray(input_image)
                image = vis_processors["eval"](image).unsqueeze(0).to(device)
                prompt = BLIP_model.generate({"image": image})[0]
                if "a black and white photo of" in prompt or "black and white photograph of" in prompt:
                    prompt = prompt.replace(prompt[:prompt.find("of")+3], "")

            print(prompt)
            H_ori, W_ori, C_ori = input_image.shape
            img = resize_image(input_image, image_resolution)
            mask = resize_image(mask, image_resolution)
            hint_image = resize_image(hint_image, image_resolution)
            mask, masked_image = prepare_mask_and_masked_image(Image.fromarray(hint_image), Image.fromarray(mask))
            mask, masked_image_latents = encode_mask(mask, masked_image)
            H, W, C = img.shape

            ref_image = np.array([[[0]*C]*W]*H).astype(np.float32)
            ref_image = resize_image(ref_image, image_resolution)

            control = torch.from_numpy(img.copy()).float().cuda() / 255.0
            control = torch.stack([control for _ in range(num_samples)], dim=0)
            control = einops.rearrange(control, 'b h w c -> b c h w').clone()

            if seed == -1:
                seed = random.randint(0, 65535)
            seed_everything(seed)

            ref_image = cv2.resize(ref_image, (W, H))
            ref_image = torch.from_numpy(ref_image).cuda().unsqueeze(0)
            init_latents = None

            if config.save_memory:
                model.low_vram_shift(is_diffusing=False)

            print("no reference images, using Frozen encoder")
            cond = {"c_concat": [control], "c_crossattn": [model.get_learned_conditioning([prompt + ', ' + a_prompt] * num_samples)]}
            un_cond = {"c_concat": None if guess_mode else [control], "c_crossattn": [model.get_learned_conditioning([n_prompt] * num_samples)]}
            shape = (4, H // 8, W // 8)

            if config.save_memory:
                model.low_vram_shift(is_diffusing=True)

            # RTX 3050: Use FP16 for noise generation
            generator = torch.manual_seed(seed)
            noise = torch.randn(shape, generator=generator, device=device, dtype=torch.float16)

            model.control_scales = [strength * (0.825 ** float(12 - i)) for i in range(13)] if guess_mode else ([strength] * 13)

            samples, intermediates = ddim_sampler.sample(model, ddim_steps, num_samples,
                                                        shape, cond, mask=mask, masked_image_latents=masked_image_latents, verbose=False, eta=eta,
                                                        x_T=init_latents,
                                                        unconditional_guidance_scale=scale,
                                                        sag_scale=sag_scale,
                                                        SAG_influence_step=SAG_influence_step,
                                                        noise=noise,
                                                        unconditional_conditioning=un_cond)

            if config.save_memory:
                model.low_vram_shift(is_diffusing=False)

            if not using_deformable_vae:
                x_samples = model.decode_first_stage(samples)
            else:
                samples = model.decode_first_stage_before_vae(samples)
                gray_content_z = vae_model.get_gray_content_z(torch.from_numpy(img.copy()).float().cuda() / 255.0)
                x_samples = vae_model.decode(samples, gray_content_z)

            x_samples = (einops.rearrange(x_samples, 'b c h w -> b h w c') * 127.5 + 127.5).cpu().numpy().clip(0, 255).astype(np.uint8)

            results_ori = [x_samples[i] for i in range(num_samples)]
            results_ori = [cv2.resize(i, (W_ori, H_ori), interpolation=cv2.INTER_LANCZOS4) for i in results_ori]

            cv2.imwrite("result_ori.png", cv2.cvtColor(results_ori[0], cv2.COLOR_RGB2BGR))

            results_tmp = [cv2.cvtColor(np.array(i), cv2.COLOR_RGB2LAB) for i in results_ori]
            results = [cv2.merge([input_image[:, :, 0], tmp[:, :, 1], tmp[:, :, 2]]) for tmp in results_tmp]
            results_mergeL = [cv2.cvtColor(np.asarray(i), cv2.COLOR_LAB2RGB) for i in results]
            cv2.imwrite("output.png", cv2.cvtColor(results_mergeL[0], cv2.COLOR_RGB2BGR))

    # Clear memory after processing
    clear_memory()
    return results_mergeL

def get_grayscale_img(img, progress=gr.Progress(track_tqdm=True)):
    clear_memory()  # RTX 3050 optimization
    for j in tqdm.tqdm(range(1), desc="Uploading input..."):
        return img, "Uploading input image done."

# Original Gradio interface with RTX 3050 optimizations
block = gr.Blocks().queue()
with block:
    with gr.Row():
        gr.Markdown("## CtrlColor - RTX 3050 Optimized")
        gr.Markdown("**Optimizations:** FP16 enabled, Memory managed, Batch size limited")

    with gr.Row():
        with gr.Column():
            grayscale_img = gr.Image(visible=False, type="numpy")
            input_image = gr.Image(source='upload', tool='color-sketch', interactive=True)
            Grayscale_button = gr.Button(value="Upload input image")
            text_out = gr.Textbox(value="RTX 3050 optimized! Upload image and draw strokes or input text prompts.")
            prompt = gr.Textbox(label="Prompt")
            change_according_to_strokes = gr.Checkbox(label='Change according to strokes\' color', value=True)
            iterative_editing = gr.Checkbox(label='Only change the strokes\' area', value=False)
            using_deformable_vae = gr.Checkbox(label='Using deformable vae (Less color overflow)', value=False)

            with gr.Accordion("RTX 3050 Settings", open=True):
                num_samples = gr.Slider(1, 1, value=1, step=1, label="Number of samples (RTX 3050: max 1)")
                image_resolution = gr.Slider(256, 512, value=512, step=64, label="Image Resolution (RTX 3050: max 512)")
                ddim_steps = gr.Slider(1, 50, value=20, step=1, label="DDIM Steps")
                strength = gr.Slider(0.0, 2.0, value=1.0, step=0.01, label="Control Strength")
                guess_mode = gr.Checkbox(label='Guess Mode', value=False)
                scale = gr.Slider(0.1, 30.0, value=9.0, step=0.1, label="Guidance Scale")
                sag_scale = gr.Slider(0.0, 1.0, value=0.05, step=0.01, label="SAG Scale")
                SAG_influence_step = gr.Slider(0, 1000, value=600, step=1, label="SAG Influence Step")
                seed = gr.Slider(-1, 2147483647, step=1, randomize=True, label="Seed")
                eta = gr.Number(label="eta (DDIM)", value=0.0)

            a_prompt = gr.Textbox(label="Added Prompt", value='best quality, extremely detailed')
            n_prompt = gr.Textbox(label="Negative Prompt", value='longbody, lowres, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality')
            run_button = gr.Button(label="Run (RTX 3050 Optimized)", variant="primary")

        with gr.Column():
            result_gallery = gr.Gallery(label='Output', show_label=False, elem_id="gallery").style(grid=2, height='auto')

    Grayscale_button.click(fn=get_grayscale_img, inputs=[input_image], outputs=[grayscale_img, text_out])
    run_button.click(fn=process, inputs=[using_deformable_vae, change_according_to_strokes, iterative_editing, input_image, input_image, prompt, a_prompt, n_prompt, num_samples, image_resolution, ddim_steps, guess_mode, strength, scale, sag_scale, SAG_influence_step, seed, eta], outputs=[result_gallery])

if __name__ == "__main__":
    print("🚀 Launching RTX 3050 optimized CtrlColor...")
    block.launch(server_port=7860, share=False, inbrowser=True)
